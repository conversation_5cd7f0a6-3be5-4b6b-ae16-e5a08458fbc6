import { pgTable, uuid, jsonb, varchar, timestamp, boolean } from "drizzle-orm/pg-core";
import { sites, agents } from "../schema";
import { once } from "../schema";

/**
 * Agent configurations table for storing site-specific agent policies and behavior settings
 */
export const agentConfigurations = once.table("agent_configurations", {
  id: uuid("id").defaultRandom().primaryKey(),

  // References
  siteId: uuid("site_id").references(() => sites.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  agentId: uuid("agent_id").references(() => agents.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),

  // Configuration type
  configType: varchar("config_type", { length: 30 })
    .$type<"returns" | "orders" | "products" | "customer_service" | "global">()
    .notNull(),

  // Whether this configuration is enabled
  enabled: boolean("enabled").default(true),

  // JSON configuration data
  settings: jsonb("settings").notNull(),

  // Timestamps
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true, mode: "date" }).defaultNow(),
});

/**
 * Example configuration types and their settings structure:
 *
 * 1. Returns Configuration
 * {
 *   "enabled": true,
 *   "requireEmail": true,
 *   "requirePhone": false,
 *   "requireReason": true,
 *   "maxDaysAfterPurchase": 30,
 *   "autoApproveBelow": 50,
 *   "requirePhoto": false,
 *   "notifyChannels": ["email", "sms"],
 *   "customFields": [
 *     { "name": "preferredRefundMethod", "required": true }
 *   ]
 * }
 *
 * 2. Order Cancellation Configuration
 * {
 *   "enabled": true,
 *   "maxHoursAfterPurchase": 24,
 *   "requireReason": true,
 *   "autoApproveBelow": 100,
 *   "notifyChannels": ["email"]
 * }
 *
 * 3. Customer Service Configuration
 * {
 *   "collectCustomerInfo": true,
 *   "requiredFields": ["email"],
 *   "optionalFields": ["phone", "orderNumber"],
 *   "escalationThreshold": 0.7,
 *   "maxTurnsBeforeHuman": 5
 * }
 */

// Migration helper function
export async function createAgentConfigurationsTable(db: any) {
  try {
    await db.schema.createTable(agentConfigurations);
    console.log("Created agent_configurations table");

    // Create default configurations for existing sites/agents
    // This would be implemented in a migration script
  } catch (error) {
    console.error("Error creating agent_configurations table:", error);
  }
}
