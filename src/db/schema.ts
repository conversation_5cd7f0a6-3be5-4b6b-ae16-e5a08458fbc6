/* SaaS multi‑tenant schema — tweaked with production‑grade safeguards */

import {
  pgSchema,
  pgTable,
  uuid,
  varchar,
  text,
  boolean,
  integer,
  jsonb,
  timestamp,
  primaryKey,
  index,
  uniqueIndex,
  check,
  vector,
  foreignKey,
  numeric,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

/* -------------------------------------------------------------------------- */
/* 0. Namespace                                                               */
/* -------------------------------------------------------------------------- */
export const once = pgSchema("once");

/* -------------------------------------------------------------------------- */
/* 1. AUTH (only timestamptz + ON UPDATE tweaks)                               */
/* -------------------------------------------------------------------------- */
export const users = once.table("users", {
  id: text("id").notNull().primaryKey(),
  name: text("name"),
  email: text("email").notNull(),
  emailVerified: timestamp("email_verified", { withTimezone: true, mode: "date" }),
  image: text("image"),
  password: text("password"),
});

export const accounts = once.table(
  "accounts",
  {
    userId: text("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade", onUpdate: "cascade" }),
    type: text("type").notNull(),
    provider: text("provider").notNull(),
    providerAccountId: text("provider_account_id").notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: integer("expires_at"),
    token_type: text("token_type"),
    scope: text("scope"),
    id_token: text("id_token"),
    session_state: text("session_state"),
  },
  (t) => ({ pk: primaryKey({ columns: [t.provider, t.providerAccountId] }) }),
);

export const sessions = once.table("sessions", {
  sessionToken: text("session_token").notNull().primaryKey(),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade", onUpdate: "cascade" }),
  expires: timestamp("expires", { withTimezone: true, mode: "date" }).notNull(),
});

export const verificationTokens = once.table(
  "verification_tokens",
  {
    identifier: text("identifier").notNull(),
    token: text("token").notNull(),
    expires: timestamp("expires", { withTimezone: true, mode: "date" }).notNull(),
  },
  (t) => ({ pk: primaryKey({ columns: [t.identifier, t.token] }) }),
);

/* -------------------------------------------------------------------------- */
/* 2. ORGANIZATIONS / TENANT                                                 */
/* -------------------------------------------------------------------------- */
export const organizations = once.table("organizations", {
  id: uuid("id").defaultRandom().primaryKey(),
  name: varchar("name", { length: 120 }).notNull(),
  slug: varchar("slug", { length: 120 }).notNull().unique(),
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
});

export const orgMembers = once.table(
  "org_members",
  {
    orgId: uuid("org_id")
      .notNull()
      .references(() => organizations.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),
    userId: text("user_id")
      .notNull()
      .references(() => users.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),
    role: varchar("role", { length: 20 }).$type<"owner" | "admin" | "agent">().default("admin"),
  },
  (t) => ({ pk: primaryKey({ columns: [t.orgId, t.userId] }) }),
);

/* -------------------------------------------------------------------------- */
/* 3. SITES                                                                  */
/* -------------------------------------------------------------------------- */
export const sites = once.table("sites", {
  id: uuid("id").defaultRandom().primaryKey(),
  orgId: uuid("org_id")
    .notNull()
    .references(() => organizations.id, {
      onDelete: "cascade",
      onUpdate: "cascade",
    }),
  name: varchar("name", { length: 120 }).notNull(),
  url: varchar("url", { length: 256 }).notNull(),
  platform: varchar("platform", { length: 32 }).$type<"shopify" | "woocommerce" | "lightspeed">(),
  platformShopId: varchar("platform_shop_id", { length: 128 }),
  platformToken: text("platform_token"), // 🔐  encrypt via KMS/pgcrypto
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
});

/* -------------------------------------------------------------------------- */
/* 4. AGENTS                                                                 */
/* -------------------------------------------------------------------------- */
export const agents = once.table(
  "agents",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    siteId: uuid("site_id").references(() => sites.id, {
      onDelete: "cascade",
      onUpdate: "cascade",
    }),
    name: varchar("name", { length: 120 }).notNull(),
    mode: varchar("mode", { length: 30 })
      .$type<"cs_default" | "faq_only" | "vision_return">()
      .default("cs_default"),
    currentModel: varchar("current_model", { length: 120 }).notNull(),
    reflectionEnabled: boolean("reflection_enabled").default(true),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
  },
  (t) => ({
    nameUnique: uniqueIndex("agents_site_name_uq").on(t.siteId, t.name),
  }),
);

/* -------------------------------------------------------------------------- */
/* 5. CHAT                                                                  */
/* -------------------------------------------------------------------------- */
export const chatSessions = once.table("chat_sessions", {
  id: uuid("id").defaultRandom().primaryKey(),
  agentId: uuid("agent_id").references(() => agents.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  externalUserId: varchar("external_user_id", { length: 120 }),
  state: varchar("state", { length: 20 }).$type<"open" | "closed">().default("open"),
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
  closedAt: timestamp("closed_at", { withTimezone: true, mode: "date" }),
  metadata: jsonb("metadata").default({}),
});

export const chatMessages = once.table("chat_messages", {
  id: uuid("id").defaultRandom().primaryKey(),
  sessionId: uuid("session_id").references(() => chatSessions.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  role: varchar("role", { length: 10 }).$type<"user" | "assistant" | "system">(),
  content: text("content").notNull(),
  traceId: varchar("trace_id", { length: 60 }),
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
});

/* -------------------------------------------------------------------------- */
/* 6. JOBS                                                                  */
/* -------------------------------------------------------------------------- */
export const jobs = once.table(
  "jobs",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    agentId: uuid("agent_id").references(() => agents.id, {
      onDelete: "cascade",
      onUpdate: "cascade",
    }),
    type: varchar("type", { length: 40 }).$type<
      | "return_create"
      | "return_refund"
      | "damage_check"
      | "email_followup"
      | "order_status"
      | "email_response"
      | "process_knowledge_source"
      | "product_recommendations"
      | "generate_embeddings"
      | "return_request_created"
      | "product_text_embed"
      | "product_image_embed"
      | "initial_product_sync"
      | "process_shopify_webhook"
    >(),
    payload: jsonb("payload"),
    status: varchar("status", { length: 20 })
      .$type<"queued" | "running" | "done" | "error" | "awaiting_review" | "scheduled">()
      .default("queued"),
    attempts: integer("attempts").default(0),
    runAfter: timestamp("run_after", { withTimezone: true, mode: "date" }).defaultNow(),
    finishedAt: timestamp("finished_at", { withTimezone: true, mode: "date" }),
    parentJobId: uuid("parent_job_id"),
    stage: varchar("stage", { length: 40 }),
  },
  (t) => ({
    queueIdx: index("jobs_status_runafter_idx").on(t.status, t.runAfter),
    parentJobFk: foreignKey({
      columns: [t.parentJobId],
      foreignColumns: [t.id],
      name: "jobs_parent_job_fk",
    }).onDelete("set null"),
  }),
);

/* -------------------------------------------------------------------------- */
/* 7. RETURNS                                                                */
/* -------------------------------------------------------------------------- */
export const returnRequests = once.table("return_requests", {
  id: uuid("id").defaultRandom().primaryKey(),
  siteId: uuid("site_id").references(() => sites.id, { onDelete: "cascade", onUpdate: "cascade" }),
  sessionId: uuid("session_id").references(() => chatSessions.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  orderNumber: varchar("order_number", { length: 50 }).notNull(),
  orderShopifyId: varchar("order_shopify_id", { length: 100 }),
  customerEmail: varchar("customer_email", { length: 256 }),
  customerPhone: varchar("customer_phone", { length: 50 }),
  reason: text("reason"),
  status: varchar("status", { length: 30 })
    .$type<
      | "pending"
      | "reviewing"
      | "waiting_on_customer"
      | "waiting_on_staff"
      | "approved"
      | "denied"
      | "processing"
      | "items_received"
      | "completed"
      | "cancelled"
    >()
    .default("pending"),
  requestedItems: jsonb("requested_items"), // Array of {lineItemId, quantity, reason}
  chatContext: text("chat_context"), // AI-generated summary of the conversation
  analysisData: jsonb("analysis_data"), // AI analysis result stored as JSON
  staffNotes: text("staff_notes"),
  shopifyReturnId: varchar("shopify_return_id", { length: 100 }),
  reviewedBy: text("reviewed_by").references(() => users.id),
  reviewedAt: timestamp("reviewed_at", { withTimezone: true, mode: "date" }),
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true, mode: "date" }).defaultNow(),
});

/* -------------------------------------------------------------------------- */
/* 8. EMAIL NOTIFICATIONS                                                    */
/* -------------------------------------------------------------------------- */
export const emailNotifications = once.table("email_notifications", {
  id: uuid("id").defaultRandom().primaryKey(),
  returnRequestId: uuid("return_request_id").references(() => returnRequests.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  sessionId: uuid("session_id").references(() => chatSessions.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  to: varchar("to", { length: 256 }).notNull(),
  from: varchar("from", { length: 256 }),
  subject: text("subject").notNull(),
  content: text("content").notNull(),
  templateType: varchar("template_type", { length: 50 }).$type<
    | "return_received"
    | "return_approved"
    | "return_denied"
    | "return_questions"
    | "return_items_received"
    | "return_completed"
    | "return_reminder"
  >(),
  status: varchar("status", { length: 20 })
    .$type<"pending" | "sent" | "failed" | "scheduled">()
    .default("pending"),
  sentAt: timestamp("sent_at", { withTimezone: true, mode: "date" }),
  scheduledFor: timestamp("scheduled_for", { withTimezone: true, mode: "date" }),
  aiGenerated: boolean("ai_generated").default(true),
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
});

/* -------------------------------------------------------------------------- */
/* 9. FEEDBACK & FINE‑TUNE                                                   */
/* -------------------------------------------------------------------------- */
export const feedback = once.table(
  "feedback",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    messageId: uuid("message_id").references(() => chatMessages.id, {
      onDelete: "cascade",
      onUpdate: "cascade",
    }),
    rating: integer("rating").$type<-1 | 0 | 1>().notNull(),
    correction: text("correction"),
    comment: text("comment"),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
  },
  (t) => ({
    ratingCheck: check("feedback_rating_chk", sql`${t.rating} IN (-1,0,1)`),
    msgUnique: uniqueIndex("feedback_msg_uq").on(t.messageId),
  }),
);

export const fineTuneRuns = once.table("fine_tune_runs", {
  id: uuid("id").defaultRandom().primaryKey(),
  agentId: uuid("agent_id").references(() => agents.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  openaiJobId: varchar("openai_job_id", { length: 60 }),
  baseModel: varchar("base_model", { length: 120 }),
  outputModel: varchar("output_model", { length: 120 }),
  status: varchar("status", { length: 20 }).$type<"running" | "succeeded" | "failed">(),
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
  completedAt: timestamp("completed_at", { withTimezone: true, mode: "date" }),
});

/* -------------------------------------------------------------------------- */
/* 10. Prompt overrides                                                       */
/* -------------------------------------------------------------------------- */
export const agentPrompts = once.table(
  "agent_prompts",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    agentId: uuid("agent_id").references(() => agents.id, {
      onDelete: "cascade",
      onUpdate: "cascade",
    }),
    slot: varchar("slot", { length: 24 }).$type<"system" | "welcome" | "fallback">(),
    content: text("content").notNull(),
    temperature: integer("temperature").default(0),
    topP: integer("top_p").default(1),
    active: boolean("active").default(true),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
  },
  (t) => ({
    activeSlot: uniqueIndex("agent_prompts_active_slot")
      .on(t.agentId, t.slot)
      .where(sql`${t.active} = true`),
  }),
);

export const promptVersions = once.table("prompt_versions", {
  id: uuid("id").defaultRandom().primaryKey(),
  promptId: uuid("prompt_id").references(() => agentPrompts.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  content: text("content").notNull(),
  createdBy: text("created_by").references(() => users.id),
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
});

/* -------------------------------------------------------------------------- */
/* 11. Agent Configurations                                                  */
/* -------------------------------------------------------------------------- */
export const agentConfigurations = once.table("agent_configurations", {
  id: uuid("id").defaultRandom().primaryKey(),

  // References
  siteId: uuid("site_id").references(() => sites.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  agentId: uuid("agent_id").references(() => agents.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),

  // Configuration type
  configType: varchar("config_type", { length: 30 })
    .$type<"returns" | "orders" | "products" | "customer_service" | "global">()
    .notNull(),

  // Whether this configuration is enabled
  enabled: boolean("enabled").default(true),

  // JSON configuration data
  settings: jsonb("settings").notNull(),

  // Timestamps
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true, mode: "date" }).defaultNow(),
});

/* -------------------------------------------------------------------------- */
/* 12. Knowledge topics & sources & pgvector chunks                          */
/* -------------------------------------------------------------------------- */
export const knowledgeTopics = once.table(
  "knowledge_topics",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    siteId: uuid("site_id")
      .references(() => sites.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      })
      .notNull(),
    slug: varchar("slug", { length: 100 }).notNull(),
    name: varchar("name", { length: 100 }).notNull(),
    description: text("description"),
    icon: varchar("icon", { length: 50 }),
    displayOrder: integer("display_order").default(0),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "date" }).defaultNow(),
  },
  (t) => ({
    // Compound unique index on siteId and slug for URL generation
    siteSlugUnique: uniqueIndex("knowledge_topics_site_slug_uq").on(t.siteId, t.slug),
    // Index on siteId for filtering
    siteIdx: index("knowledge_topics_site_idx").on(t.siteId),
    // Index on displayOrder for sorting
    displayOrderIdx: index("knowledge_topics_display_order_idx").on(t.displayOrder),
  }),
);

export const knowledgeSources = once.table("knowledge_sources", {
  id: uuid("id").defaultRandom().primaryKey(),
  siteId: uuid("site_id").references(() => sites.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  topicId: uuid("topic_id").references(() => knowledgeTopics.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  kind: varchar("kind", { length: 24 }).$type<"url" | "file" | "feed" | "shopify" | "article">(),
  label: varchar("label", { length: 120 }),
  meta: jsonb("meta"),
  status: varchar("status", { length: 16 })
    .$type<"new" | "processing" | "ready" | "error">()
    .default("new"),
  insertedAt: timestamp("inserted_at", { withTimezone: true, mode: "date" }).defaultNow(),
  processedAt: timestamp("processed_at", { withTimezone: true, mode: "date" }),
});

export const knowledgeChunks = once.table(
  "knowledge_chunks",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    sourceId: uuid("source_id").references(() => knowledgeSources.id, {
      onDelete: "cascade",
      onUpdate: "cascade",
    }),
    content: text("content").notNull(),
    embedding: vector("embedding", { dimensions: 1536 }),
    tokenCount: integer("token_count"),
    criticalityScore: integer("criticality_score").notNull().default(50),
    clarityScore: integer("clarity_score").notNull().default(50),
    actionabilityScore: integer("actionability_score").notNull().default(50),
    expectedFrequencyScore: integer("expected_frequency_score").notNull().default(50),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
  },
  (t) => [index("chunks_vec_idx").using("ivfflat", t.embedding.op("vector_l2_ops"))],
);

/* ---------------------------------------------------------------------- */
/* 20. Catalog products & vectors                                         */
/* ---------------------------------------------------------------------- */
export const catalogProducts = once.table(
  "catalog_products",
  {
    id: uuid("id").defaultRandom().primaryKey(), // internal
    siteId: uuid("site_id")
      .references(() => sites.id, { onDelete: "cascade", onUpdate: "cascade" })
      .notNull(),
    shopifyProductId: varchar("shopify_id", { length: 32 }).notNull(),
    title: varchar("title", { length: 255 }).notNull(),
    description: text("description"),
    price: numeric("price", { precision: 10, scale: 2 }),
    vendor: varchar("vendor", { length: 120 }),
    tags: varchar("tags", { length: 255 }),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "date" }).defaultNow(),
  },
  (t) => ({
    uniq: uniqueIndex("catalog_site_shopify_uq").on(t.siteId, t.shopifyProductId),
    siteIdx: index("catalog_site_idx").on(t.siteId),
  }),
);

export const catalogImages = once.table(
  "catalog_images",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    productId: uuid("product_id")
      .references(() => catalogProducts.id, { onDelete: "cascade", onUpdate: "cascade" })
      .notNull(),
    src: varchar("src", { length: 600 }).notNull(),
    position: integer("position").default(0),
    embedding: vector("embedding", { dimensions: 1024 }), // Azure AI Vision CLIP embeddings
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "date" }).defaultNow(),
  },
  (t) => ({
    productPositionUnique: uniqueIndex("catalog_images_product_position_unique").on(
      t.productId,
      t.position,
    ),
    productIdx: index("catalog_img_prod_idx").on(t.productId),
    vectorIdx: index("catalog_img_vec_idx")
      .using("ivfflat", t.embedding.op("vector_l2_ops"))
      .with({ lists: 100 }),
  }),
);

export const catalogTextEmbeds = once.table(
  "catalog_text_embeds",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    productId: uuid("product_id")
      .references(() => catalogProducts.id, { onDelete: "cascade", onUpdate: "cascade" })
      .notNull(),
    embedding: vector("embedding", { dimensions: 1536 }),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "date" }).defaultNow(),
  },
  (t) => ({
    productUnique: uniqueIndex("catalog_text_embeds_product_unique").on(t.productId),
    productIdx: index("catalog_txt_prod_idx").on(t.productId),
    vectorIdx: index("catalog_txt_vec_idx")
      .using("ivfflat", t.embedding.op("vector_l2_ops"))
      .with({ lists: 100 }),
  }),
);

/* -------------------------------------------------------------------------- */
/* 13. Shop Policy Configurations (for LangGraph Agent)                      */
/* -------------------------------------------------------------------------- */
export const shopPolicyConfigs = once.table(
  "shop_policy_configs",
  {
    id: uuid("id").defaultRandom().primaryKey(),

    // References - one config per site
    siteId: uuid("site_id")
      .references(() => sites.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      })
      .notNull(),

    // Core personality and behavior
    personalityPrompt: text("personality_prompt").notNull(),

    // Enabled tools for this shop
    enabledTools: jsonb("enabled_tools").$type<string[]>().notNull(), // ["lookupOrder", "createReturn", etc.]

    // Tool-specific policies (natural language rules)
    toolPolicies: jsonb("tool_policies").$type<Record<string, string[]>>().notNull(),

    // Escalation settings
    maxTurnsBeforeEscalation: integer("max_turns_before_escalation").default(5),
    escalationEnabled: boolean("escalation_enabled").default(true),
    escalationEmail: varchar("escalation_email", { length: 256 }),

    // Model settings
    primaryModel: varchar("primary_model", { length: 120 }).default("gpt-4o"),
    fallbackModel: varchar("fallback_model", { length: 120 }).default("gpt-5-mini"),
    temperature: integer("temperature").default(20), // Store as int, divide by 100 for actual temp

    // Self-reflection settings (from Plan.md)
    reflectionEnabled: boolean("reflection_enabled").default(true),
    reflectionThreshold: integer("reflection_threshold").default(40), // confidence threshold * 100

    // Business hours and availability
    businessHours: jsonb("business_hours").$type<{
      timezone: string;
      schedule: Record<string, { start: string; end: string; enabled: boolean }>;
    }>(),

    // AURA emotional intelligence configuration
    aura: jsonb("aura").$type<{
      enabled: boolean;
      mode: "shadow" | "soft" | "full";
      empathyAck?: { enabled: boolean; delayMs: number; nlText: string; enText: string };
      baseCaps?: { maxCouponEUR: number; approvalThresholdEUR: number };
      locales?: string[];
    }>(),

    // Active status
    active: boolean("active").default(true),

    // Timestamps
    createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "date" }).defaultNow(),
  },
  (t) => ({
    // Ensure one active config per site
    siteUnique: uniqueIndex("shop_policy_configs_site_active")
      .on(t.siteId)
      .where(sql`${t.active} = true`),
  }),
);

/* -------------------------------------------------------------------------- */
/* 14. Email History (for email channel support)                            */
/* -------------------------------------------------------------------------- */
export const emailHistory = once.table("email_history", {
  id: uuid("id").defaultRandom().primaryKey(),
  siteId: uuid("site_id")
    .references(() => sites.id, {
      onDelete: "cascade",
      onUpdate: "cascade",
    })
    .notNull(),
  sessionId: uuid("session_id").references(() => chatSessions.id, {
    onDelete: "cascade",
    onUpdate: "cascade",
  }),
  customerEmail: varchar("customer_email", { length: 256 }).notNull(),
  direction: varchar("direction", { length: 10 }).$type<"inbound" | "outbound">().notNull(),
  subject: text("subject"),
  content: text("content").notNull(),
  messageId: varchar("message_id", { length: 256 }), // External email message ID
  replyToId: varchar("reply_to_id", { length: 256 }), // Parent message ID for threading
  aiGenerated: boolean("ai_generated").default(false),
  createdAt: timestamp("created_at", { withTimezone: true, mode: "date" }).defaultNow(),
});
