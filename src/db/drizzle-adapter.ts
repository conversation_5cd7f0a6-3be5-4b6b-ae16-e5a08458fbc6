import { and, eq } from "drizzle-orm";
import type { Adapter } from "@auth/core/adapters";
import { accounts, sessions, users, verificationTokens } from "./schema";
import { db } from "@/db";
import { generateUUID } from "@/lib/utils";

export function DrizzleAdapter(): Adapter {
  return {
    async createUser(data) {
      const id = generateUUID();
      await db.insert(users).values({ ...data, id });
      const user = await db.query.users.findFirst({
        where: eq(users.id, id),
      });
      return user!;
    },
    async getUser(id) {
      const user = await db.query.users.findFirst({
        where: eq(users.id, id),
      });
      return user ?? null;
    },
    async getUserByEmail(email) {
      const user = await db.query.users.findFirst({
        where: eq(users.email, email),
      });
      return user ?? null;
    },
    async getUserByAccount({ providerAccountId, provider }) {
      const result = await db.query.users.findFirst({
        where: eq(
          users.id,
          db
            .select({ id: accounts.userId })
            .from(accounts)
            .where(
              and(
                eq(accounts.providerAccountId, providerAccountId),
                eq(accounts.provider, provider),
              ),
            )
            .limit(1),
        ),
      });
      return result ?? null;
    },
    async updateUser(data) {
      const [user] = await db.update(users).set(data).where(eq(users.id, data.id)).returning();
      return user;
    },
    async deleteUser(userId) {
      const [user] = await db.delete(users).where(eq(users.id, userId)).returning();
      return user;
    },
    async linkAccount(account) {
      await db.insert(accounts).values(account);
    },
    async unlinkAccount({ providerAccountId, provider }) {
      await db
        .delete(accounts)
        .where(
          and(eq(accounts.providerAccountId, providerAccountId), eq(accounts.provider, provider)),
        );
    },
    async createSession(data) {
      const [session] = await db.insert(sessions).values(data).returning();
      return session;
    },
    async getSessionAndUser(sessionToken) {
      const result = await db.query.sessions.findFirst({
        where: eq(sessions.sessionToken, sessionToken),
        with: {
          user: true,
        },
      });
      if (!result) return null;
      const { user, ...session } = result;
      return {
        user,
        session,
      };
    },
    async updateSession(data) {
      const [session] = await db
        .update(sessions)
        .set(data)
        .where(eq(sessions.sessionToken, data.sessionToken))
        .returning();
      return session;
    },
    async deleteSession(sessionToken) {
      const [session] = await db
        .delete(sessions)
        .where(eq(sessions.sessionToken, sessionToken))
        .returning();
      return session;
    },
    async createVerificationToken(data) {
      const [verificationToken] = await db.insert(verificationTokens).values(data).returning();
      return verificationToken;
    },
    async useVerificationToken({ identifier, token }) {
      const [verificationToken] = await db
        .delete(verificationTokens)
        .where(
          and(eq(verificationTokens.identifier, identifier), eq(verificationTokens.token, token)),
        )
        .returning();
      return verificationToken;
    },
  };
}
