"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  IconClock,
  IconPackage,
  IconExternalLink,
  IconBrain,
  IconUser,
  IconMail,
  IconCalendar,
  IconCheck,
  IconLoader,
  IconEye,
} from "@tabler/icons-react";

interface ChatMessage {
  role: "user" | "assistant" | "system";
  content: string;
  createdAt: Date | null;
}

interface ReturnRequest {
  id: string;
  siteId: string;
  orderNumber: string;
  orderShopifyId: string | null;
  customerEmail: string | null;
  customerPhone: string | null;
  reason: string | null;
  status:
    | "pending"
    | "reviewing"
    | "waiting_on_customer"
    | "waiting_on_staff"
    | "approved"
    | "denied"
    | "processing"
    | "items_received"
    | "completed"
    | "cancelled";
  requestedItems: unknown;
  staffNotes: string | null;
  shopifyReturnId: string | null;
  reviewedBy: string | null;
  reviewedAt: Date | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  sessionId: string | null;
  chatContext?: ChatMessage[] | null;
}

// Define the color scheme type
type ColorScheme = "yellow" | "blue" | "purple" | "green";

// Define the icon component type (assuming you're using Tabler icons or similar)
type IconComponent = React.ComponentType<{ className?: string }>;

// Props interface for the StatCard component
interface StatCardProps {
  value: string | number;
  label: string;
  icon: IconComponent;
  colorScheme: ColorScheme;
}

const StatCard: React.FC<StatCardProps> = ({ value, label, icon: Icon, colorScheme }) => {
  const colorClasses: Record<
    ColorScheme,
    {
      bg: string;
      borderGradient: string;
      text: string;
      subtext: string;
      icon: string;
    }
  > = {
    yellow: {
      bg: "from-yellow-50 to-white",
      borderGradient: "from-yellow-100 to-yellow-50",
      text: "text-yellow-700",
      subtext: "text-yellow-500",
      icon: "text-yellow-500",
    },
    blue: {
      bg: "from-blue-50 to-white",
      borderGradient: "from-blue-100 to-blue-50",
      text: "text-blue-700",
      subtext: "text-blue-500",
      icon: "text-blue-500",
    },
    purple: {
      bg: "from-purple-50 to-white",
      borderGradient: "from-purple-100 to-purple-50",
      text: "text-purple-700",
      subtext: "text-purple-500",
      icon: "text-purple-500",
    },
    green: {
      bg: "from-green-50 to-white",
      borderGradient: "from-green-100 to-green-50",
      text: "text-green-700",
      subtext: "text-green-500",
      icon: "text-green-500",
    },
  };

  const colors = colorClasses[colorScheme];

  return (
    <div
      className={`relative rounded-lg p-0.5 bg-gradient-to-b ${colors.borderGradient} group transition-all duration-200`}
    >
      {/* Inner card with subtle top-to-bottom gradient */}
      <div
        className={`bg-gradient-to-b ${colors.bg} rounded-lg p-3 h-full relative overflow-hidden`}
      >
        {/* Subtle shine effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Content */}
        <div className="relative z-10 flex items-center justify-between h-full">
          <div className="flex-1">
            <p className={`text-lg font-semibold ${colors.text}`}>{value}</p>
            <p className={`text-xs ${colors.subtext} mt-0.5`}>{label}</p>
          </div>

          {/* Icon with subtle background */}
          <div className="ml-2 relative">
            <div className="absolute inset-0 bg-white/60 rounded-full blur-sm"></div>
            <Icon className={`w-4 h-4 ${colors.icon} relative z-10`} />
          </div>
        </div>

        {/* Optional: Add trending arrow */}
        <div className={`absolute top-2 right-2 ${colors.icon} opacity-20`}>
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 17L17 7M17 7H7M17 7V17"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

interface ReturnsManagementProps {
  siteId: string;
  returns: ReturnRequest[];
  currentFilter: string;
  stats: {
    total: number;
    pending: number;
    reviewing: number;
    approved: number;
    denied: number;
    processing: number;
    items_received: number;
    completed: number;
  };
  userRole: "owner" | "admin" | "agent";
}

const FILTER_OPTIONS = [
  { value: "all", label: "Alle", count: "total", color: "bg-gray-100 text-gray-700" },
  { value: "pending", label: "Nieuw", count: "pending", color: "bg-yellow-100 text-yellow-700" },
  {
    value: "reviewing",
    label: "In behandeling",
    count: "reviewing",
    color: "bg-blue-100 text-blue-700",
  },
  {
    value: "approved",
    label: "Goedgekeurd",
    count: "approved",
    color: "bg-green-100 text-green-700",
  },
  {
    value: "processing",
    label: "Wordt verwerkt",
    count: "processing",
    color: "bg-purple-100 text-purple-700",
  },
  {
    value: "items_received",
    label: "Items ontvangen",
    count: "items_received",
    color: "bg-orange-100 text-orange-700",
  },
  { value: "completed", label: "Voltooid", count: "completed", color: "bg-gray-100 text-gray-700" },
  { value: "denied", label: "Afgewezen", count: "denied", color: "bg-red-100 text-red-700" },
] as const;

export function ReturnsManagement({
  siteId,
  returns,
  currentFilter,
  stats,
  userRole,
}: ReturnsManagementProps) {
  const canManageReturns = userRole === "owner" || userRole === "admin";

  const getStatusBadge = (status: ReturnRequest["status"]) => {
    const statusConfig = {
      pending: { label: "Nieuw", className: "bg-yellow-100 text-yellow-700 border-yellow-300" },
      reviewing: {
        label: "In behandeling",
        className: "bg-blue-100 text-blue-700 border-blue-300",
      },
      waiting_on_customer: {
        label: "Wachtend op klant",
        className: "bg-pink-100 text-pink-700 border-pink-300",
      },
      waiting_on_staff: {
        label: "Reactie vereist",
        className: "bg-pink-100 text-pink-700 border-pink-300",
      },
      approved: { label: "Goedgekeurd", className: "bg-green-100 text-green-700 border-green-300" },
      denied: { label: "Afgewezen", className: "bg-red-100 text-red-700 border-red-300" },
      processing: {
        label: "Wordt verwerkt",
        className: "bg-purple-100 text-purple-700 border-purple-300",
      },
      items_received: {
        label: "Items ontvangen",
        className: "bg-orange-100 text-orange-700 border-orange-300",
      },
      completed: { label: "Voltooid", className: "bg-gray-100 text-gray-700 border-gray-300" },
      cancelled: { label: "Geannuleerd", className: "bg-gray-100 text-gray-700 border-gray-300" },
    };

    const config = statusConfig[status];
    return (
      <Badge variant="outline" className={`${config.className} text-xs px-2 py-0.5`}>
        {config.label}
      </Badge>
    );
  };

  const formatTimeAgo = (date: Date | null) => {
    if (!date) return "onbekend";
    const now = new Date();
    const diffMs = now.getTime() - new Date(date).getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 60) return `${diffMins}m`;
    if (diffHours < 24) return `${diffHours}u`;
    return `${diffDays}d`;
  };

  const getActionButton = (returnReq: ReturnRequest) => {
    if (!canManageReturns) return null;

    switch (returnReq.status) {
      case "pending":
        return (
          <Button size="sm" className="h-7 px-2 text-xs bg-primary hover:bg-blue-700" asChild>
            <a href={`/dashboard/${returnReq.siteId || siteId}/returns/${returnReq.id}/review`}>
              <IconBrain className="w-3 h-3 mr-1" />
              Beoordelen
            </a>
          </Button>
        );
      case "processing":
        return (
          <Button size="sm" className="h-7 px-2 text-xs bg-orange-600 hover:bg-orange-700" asChild>
            <a href={`/dashboard/${returnReq.siteId || siteId}/returns/${returnReq.id}/review`}>
              <IconPackage className="w-3 h-3 mr-1" />
              Items ontvangen
            </a>
          </Button>
        );
      default:
        return (
          <Button variant="outline" size="sm" className="h-7 px-2 text-xs bg-transparent" asChild>
            <a href={`/dashboard/${returnReq.siteId || siteId}/returns/${returnReq.id}/review`}>
              <IconEye className="w-3 h-3 mr-1" />
              Bekijk
            </a>
          </Button>
        );
    }
  };

  const currentFilterOption = FILTER_OPTIONS.find((option) => option.value === currentFilter);

  return (
    <div className="space-y-4">
      {/* Enhanced Gradient Stats */}
      <div className="grid grid-cols-4 gap-3">
        <StatCard value={stats.pending} label="Nieuw" icon={IconClock} colorScheme="yellow" />
        <StatCard
          value={stats.reviewing}
          label="In behandeling"
          icon={IconBrain}
          colorScheme="blue"
        />
        <StatCard
          value={stats.processing}
          label="Wordt verwerkt"
          icon={IconLoader}
          colorScheme="purple"
        />
        <StatCard value={stats.completed} label="Voltooid" icon={IconCheck} colorScheme="green" />
      </div>

      {/* Enhanced Filter Tabs */}
      <Tabs value={currentFilter} className="w-full">
        <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8 h-auto bg-gray-100 p-1">
          {FILTER_OPTIONS.map((option) => (
            <TabsTrigger
              key={option.value}
              value={option.value}
              asChild
              className="data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-gray-200 rounded-md"
            >
              <a
                href={`?filter=${option.value}`}
                className={`flex flex-col items-center gap-1 p-2 rounded-md transition-all ${
                  currentFilter === option.value
                    ? `${option.color} border shadow-sm`
                    : "hover:bg-gray-50"
                }`}
              >
                <span className="text-xs font-medium">{option.label}</span>
                <Badge
                  variant="secondary"
                  className={`text-xs h-4 px-1.5 ${
                    currentFilter === option.value
                      ? "bg-white/80 text-gray-700"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  {stats[option.count as keyof typeof stats]}
                </Badge>
              </a>
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      {/* Compact Returns List */}
      <div className="space-y-2">
        {returns.length === 0 ? (
          <Card className="p-8 text-center">
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <IconPackage className="w-6 h-6 text-gray-400" />
            </div>
            <h3 className="font-medium mb-1">Geen retouren gevonden</h3>
            <p className="text-sm text-gray-500">
              Er zijn momenteel geen retouren die voldoen aan de geselecteerde filter.
            </p>
          </Card>
        ) : (
          returns.map((returnReq) => (
            <Card
              key={returnReq.id}
              className="hover:shadow-md transition-shadow border-l-2 border-l-gray-200 hover:border-l-primary"
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  {/* Left: Order info */}
                  <div className="flex items-center gap-4 flex-1 min-w-0">
                    <div className="min-w-0">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium text-sm">#{returnReq.orderNumber}</h3>
                        {getStatusBadge(returnReq.status)}
                      </div>
                      <div className="flex items-center gap-3 mt-1 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <IconCalendar className="w-3 h-3" />
                          {formatTimeAgo(returnReq.createdAt)}
                        </span>
                        {returnReq.customerEmail && (
                          <span className="flex items-center gap-1 truncate">
                            <IconMail className="w-3 h-3" />
                            <span className="truncate max-w-[150px]">
                              {returnReq.customerEmail}
                            </span>
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Center: Reason */}
                  <div className="flex-1 px-4 min-w-0">
                    {returnReq.reason ? (
                      <p className="text-xs text-gray-600 truncate" title={returnReq.reason}>
                        {returnReq.reason}
                      </p>
                    ) : (
                      <p className="text-xs text-gray-400 italic">Geen reden opgegeven</p>
                    )}
                    {returnReq.staffNotes && (
                      <p
                        className="text-xs text-primary truncate mt-1"
                        title={returnReq.staffNotes}
                      >
                        <IconUser className="w-3 h-3 inline mr-1" />
                        {returnReq.staffNotes}
                      </p>
                    )}
                  </div>

                  {/* Right: Actions */}
                  <div className="flex items-center gap-2">
                    {returnReq.sessionId && (
                      <Button variant="ghost" size="sm" className="h-7 px-2 text-xs" asChild>
                        <a
                          href={`/chat/${returnReq.sessionId}`}
                          target="_blank"
                          rel="noreferrer"
                          title="Chat bekijken"
                        >
                          <IconExternalLink className="w-3 h-3" />
                        </a>
                      </Button>
                    )}
                    {getActionButton(returnReq)}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
