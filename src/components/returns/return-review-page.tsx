"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  IconCheck,
  IconX,
  IconQuestionMark,
  IconMessage,
  IconRobot,
  IconAlertTriangle,
  IconLoader2,
  IconArrowLeft,
  IconUser,
  IconPackage,
  IconCalendar,
  IconPhone,
  IconShoppingBag,
  IconChevronDown,
  IconChevronUp,
} from "@tabler/icons-react";
import { ChevronRight } from "lucide-react";

interface ChatMessage {
  role: "user" | "assistant" | "system" | null;
  content: string;
  createdAt: Date | null;
}

interface AIAnalysis {
  sentiment: "positive" | "neutral" | "frustrated" | "angry";
  recommendation: "approve" | "deny" | "ask_questions";
  confidence: number;
  reasoning: string;
  conversationSummary: string;
  riskFactors: string[];
  customerProfile: string;
}

interface ReturnRequest {
  id: string;
  orderNumber: string;
  orderShopifyId: string | null;
  customerEmail: string | null;
  customerPhone: string | null;
  reason: string | null;
  status:
    | "pending"
    | "reviewing"
    | "waiting_on_customer"
    | "waiting_on_staff"
    | "approved"
    | "denied"
    | "processing"
    | "items_received"
    | "completed"
    | "cancelled";
  requestedItems: unknown;
  staffNotes: string | null;
  shopifyReturnId: string | null;
  reviewedBy: string | null;
  reviewedAt: Date | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  sessionId: string | null;
  chatContext?: ChatMessage[] | null;
  analysisData?: AIAnalysis | null;
}

interface ReturnReviewPageProps {
  returnRequest: ReturnRequest;
  siteId: string;
  agentName: string;
}

export function ReturnReviewPage({ returnRequest, siteId, agentName }: ReturnReviewPageProps) {
  const router = useRouter();
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [staffNotes, setStaffNotes] = useState(returnRequest.staffNotes || "");
  const [denyReason, setDenyReason] = useState("");
  const [additionalQuestions, setAdditionalQuestions] = useState("");
  const [aiAnalysis, setAiAnalysis] = useState<AIAnalysis | null>(null);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [aiInsightsOpen, setAiInsightsOpen] = useState(false);

  const sentimentLabels = {
    positive: "Positief",
    neutral: "Neutraal",
    frustrated: "Frustrated",
    angry: "Angry",
  };

  // Load AI analysis on mount
  useEffect(() => {
    if (returnRequest.analysisData && typeof returnRequest.analysisData === "object") {
      setAiAnalysis(returnRequest.analysisData);
    } else {
      analyzeReturn();
    }
  }, []);

  const analyzeReturn = async () => {
    setAnalysisLoading(true);
    try {
      const response = await fetch("/api/ai/analyze-return", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderNumber: returnRequest.orderNumber,
          orderValue: 100,
          chatHistory: returnRequest.chatContext || [],
          customerEmail: returnRequest.customerEmail,
          returnReason: returnRequest.reason,
        }),
      });

      if (response.ok) {
        const analysis = await response.json();
        setAiAnalysis(analysis);
      } else {
        // Fallback analysis
        setAiAnalysis({
          sentiment: "neutral",
          recommendation: "ask_questions",
          confidence: 0.8,
          reasoning: "Geen Agent analyse beschikbaar.",
          conversationSummary: "-",
          riskFactors: [],
          customerProfile: "-",
        });
      }
    } catch (error) {
      console.error("Failed to analyze return:", error);
      setAiAnalysis(null);
    } finally {
      setAnalysisLoading(false);
    }
  };

  const handleDecision = async (
    decision: "approve" | "deny" | "ask_questions" | "items_received",
  ) => {
    setActionLoading(decision);
    try {
      const response = await fetch(`/api/returns`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          returnRequestId: returnRequest.id,
          decision,
          staffNotes: staffNotes || undefined,
          additionalQuestions: decision === "ask_questions" ? additionalQuestions : undefined,
          denyReason: decision === "deny" ? denyReason : undefined,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to process decision");
      }

      toast.success(
        decision === "approve"
          ? "Retour goedgekeurd"
          : decision === "deny"
            ? "Retour afgewezen"
            : decision === "items_received"
              ? "Items ontvangen gemarkeerd - terugbetaling wordt verwerkt"
              : "Aanvullende vragen verstuurd",
      );

      router.push(`/dashboard/${siteId}/returns`);
    } catch (error) {
      toast.error("Er ging iets mis bij het verwerken van de beslissing");
      console.error("Decision error:", error);
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Nieuw
          </Badge>
        );
      case "reviewing":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            In behandeling
          </Badge>
        );
      case "waiting_on_customer":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            Wachtend op klant
          </Badge>
        );
      case "waiting_on_staff":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            Reactie vereist
          </Badge>
        );
      case "processing":
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
            Wordt verwerkt
          </Badge>
        );
      default:
        return <Badge variant="outline">Onbekend</Badge>;
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return "Onbekend";
    return new Date(date).toLocaleString("nl-NL", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatTimeAgo = (date: Date | null) => {
    if (!date) return "onbekend";
    const now = new Date();
    const diffMs = now.getTime() - new Date(date).getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    if (diffMins < 60) return `${diffMins} min geleden`;
    if (diffHours < 24) return `${diffHours} uur geleden`;
    return `${Math.floor(diffHours / 24)} dagen geleden`;
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case "positive":
        return "😊";
      case "neutral":
        return "😐";
      case "frustrated":
        return "😤";
      case "angry":
        return "😡";
      default:
        return "🤔";
    }
  };

  const getRecommendationAction = (recommendation: string) => {
    switch (recommendation) {
      case "approve":
        return { text: "GOEDKEUREN", icon: "✅", color: "text-green-400" };
      case "deny":
        return { text: "AFWIJZEN", icon: "❌", color: "text-red-400" };
      case "ask_questions":
        return { text: "MEER INFO VRAGEN", icon: "❓", color: "text-yellow-400" };
      default:
        return { text: "ONBEKEND", icon: "🤔", color: "text-gray-400" };
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="">
        <div className="px-6 py-4">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" asChild>
                <a href={`/dashboard/${siteId}/returns`}>
                  <IconArrowLeft className="w-4 h-4 mr-2" />
                  Terug naar overzicht
                </a>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Retour #{returnRequest.orderNumber}
                </h1>
                <p className="text-sm text-gray-500">
                  Aangevraagd {formatTimeAgo(returnRequest.createdAt)} •{" "}
                  {formatDate(returnRequest.createdAt)}
                </p>
              </div>
            </div>
            {getStatusBadge(returnRequest.status)}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Return Details */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <IconShoppingBag className="w-5 h-5 text-gray-600" />
                  <CardTitle>Retour Details</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-gray-500 block">Bestelnummer</span>
                    <span className="font-semibold text-lg">#{returnRequest.orderNumber}</span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500 block">Status</span>
                    {getStatusBadge(returnRequest.status)}
                  </div>
                </div>

                <Separator />

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <IconUser className="w-4 h-4 text-gray-400" />
                    <div>
                      <span className="text-sm text-gray-500">Klant</span>
                      <p className="font-medium">{returnRequest.customerEmail}</p>
                    </div>
                  </div>

                  {returnRequest.customerPhone && (
                    <div className="flex items-center gap-3">
                      <IconPhone className="w-4 h-4 text-gray-400" />
                      <div>
                        <span className="text-sm text-gray-500">Telefoon</span>
                        <p className="font-medium">{returnRequest.customerPhone}</p>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center gap-3">
                    <IconCalendar className="w-4 h-4 text-gray-400" />
                    <div>
                      <span className="text-sm text-gray-500">Aangevraagd op</span>
                      <p className="font-medium">{formatDate(returnRequest.createdAt)}</p>
                    </div>
                  </div>
                </div>

                {returnRequest.reason && (
                  <>
                    <Separator />
                    <div>
                      <span className="text-sm text-gray-500 block mb-2">Reden voor retour</span>
                      <p className="text-gray-900 bg-gray-50 p-3 rounded-lg border">
                        {returnRequest.reason}
                      </p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Customer Conversation */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <IconMessage className="w-5 h-5 text-gray-600" />
                    <CardTitle>Klantgesprek</CardTitle>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {returnRequest.chatContext?.length || 0} berichten
                  </Badge>
                </div>
                <CardDescription>Volledige conversatie tussen klant en {agentName}</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  {returnRequest.chatContext && returnRequest.chatContext.length > 0 ? (
                    <div className="space-y-4">
                      {returnRequest.chatContext
                        .filter((msg) => {
                          if (msg.role === "system") return false;
                          const content = msg.content?.toLowerCase() || "";
                          if (content.includes("you are responding in a chat conversation"))
                            return false;
                          if (content.includes("chat guidelines")) return false;
                          if (content.includes("generate a natural")) return false;
                          return true;
                        })
                        .map((message) => {
                          let cleanContent = message.content;
                          if (cleanContent && cleanContent.includes("__STATE_DATA__")) {
                            cleanContent = cleanContent.split("__STATE_DATA__")[0].trim();
                          }
                          return { ...message, content: cleanContent };
                        })
                        .filter((message) => message.content && message.content.trim().length > 0)
                        .map((message, index) => (
                          <div key={index} className="flex gap-3">
                            <div className="flex-shrink-0">
                              {message.role === "user" ? (
                                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                  <IconUser className="w-4 h-4 text-blue-600" />
                                </div>
                              ) : (
                                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                  <IconRobot className="w-4 h-4 text-green-600" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-sm font-medium text-gray-900">
                                  {message.role === "user" ? "Klant" : agentName}
                                </span>
                                <span className="text-xs text-gray-500">
                                  {message.createdAt ? formatTimeAgo(message.createdAt) : ""}
                                </span>
                              </div>
                              <div
                                className={`p-3 rounded-lg ${
                                  message.role === "user"
                                    ? "bg-blue-50 border border-blue-200"
                                    : "bg-green-50 border border-green-200"
                                }`}
                              >
                                <p className="text-sm text-gray-700">{message.content}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <IconMessage className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">Geen gespreksgeschiedenis beschikbaar</p>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Actions Sidebar */}
          <div className="space-y-6">
            {/* AI Assistant Recommendation - Compact with Expand */}
            {aiAnalysis && (
              <div className="relative">
                {/* Animated border effect */}
                <div className="absolute inset-0 rounded-lg overflow-hidden">
                  <div className="absolute inset-[-2px] bg-gradient-to-r from-purple-500 via-blue-500 to-purple-500 rounded-lg animate-pulse"></div>
                </div>

                {/* Main card */}
                <div
                  className="relative rounded-lg p-4 shadow-lg"
                  style={{
                    backgroundColor: "hsl(240, 15%, 9%)",
                    backgroundImage: `
                      radial-gradient(at 88% 40%, hsla(240, 15%, 9%, 1) 0px, transparent 85%),
                      radial-gradient(at 49% 30%, hsla(240, 15%, 9%, 1) 0px, transparent 85%),
                      radial-gradient(at 14% 26%, hsla(240, 15%, 9%, 1) 0px, transparent 85%),
                      radial-gradient(at 0% 64%, hsla(263, 93%, 56%, 1) 0px, transparent 85%),
                      radial-gradient(at 41% 94%, hsla(284, 100%, 84%, 1) 0px, transparent 85%),
                      radial-gradient(at 100% 99%, hsla(306, 100%, 57%, 1) 0px, transparent 85%)
                    `,
                    boxShadow: "0px -16px 24px 0px rgba(255, 255, 255, 0.25) inset",
                  }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <img
                        src="/nousu-icons/nousu.svg"
                        alt="Chat icon"
                        className="h-12 w-12 mt-2"
                        width={32}
                        height={32}
                      />
                      <span className="text-sm font-medium text-white ml-[-10.5px]">
                        {agentName}
                      </span>
                      <Badge
                        variant="outline"
                        className="text-xs bg-purple-100 text-purple-700 border-purple-300"
                      >
                        Agent Advies
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setAiInsightsOpen(!aiInsightsOpen)}
                      className="h-6 w-6 p-0 text-gray-400 hover:text-white hover:bg-gray-800"
                    >
                      {aiInsightsOpen ? (
                        <IconChevronUp className="w-3 h-3" />
                      ) : (
                        <IconChevronDown className="w-3 h-3" />
                      )}
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span
                        className={`text-sm font-semibold ${getRecommendationAction(aiAnalysis.recommendation).color}`}
                      >
                        {getRecommendationAction(aiAnalysis.recommendation).icon}{" "}
                        {getRecommendationAction(aiAnalysis.recommendation).text}
                      </span>
                    </div>

                    <div className="text-xs text-gray-300 leading-relaxed">
                      {aiInsightsOpen ? (
                        <p>{aiAnalysis.reasoning}</p>
                      ) : (
                        <p>
                          {aiAnalysis.reasoning.length > 80
                            ? `${aiAnalysis.reasoning.substring(0, 80)}...`
                            : aiAnalysis.reasoning}
                        </p>
                      )}
                    </div>

                    {aiInsightsOpen && (
                      <div className="pt-2 border-t border-gray-700 space-y-2">
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-gray-400">Stemming:</span>
                            <p className="text-white">
                              {getSentimentIcon(aiAnalysis.sentiment)}{" "}
                              {sentimentLabels[aiAnalysis.sentiment]}
                            </p>
                          </div>
                          <div>
                            <span className="text-gray-400">Profiel:</span>
                            <p className="text-white">{aiAnalysis.customerProfile}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {aiAnalysis.riskFactors.length > 0 && (
                      <div className="flex items-center gap-1 mt-2">
                        <IconAlertTriangle className="w-3 h-3 text-amber-400" />
                        <span className="text-xs text-amber-300">
                          {aiAnalysis.riskFactors.length} aandachtspunt
                          {aiAnalysis.riskFactors.length > 1 ? "en" : ""}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {analysisLoading && (
              <div className="relative">
                <div className="absolute inset-0 rounded-lg overflow-hidden">
                  <div className="absolute inset-[-2px] bg-gradient-to-r from-gray-500 via-gray-400 to-gray-500 rounded-lg animate-pulse"></div>
                </div>
                <div
                  className="relative rounded-lg p-4 shadow-lg"
                  style={{
                    backgroundColor: "hsl(240, 15%, 9%)",
                    backgroundImage: `
                      radial-gradient(at 50% 50%, hsla(240, 15%, 15%, 1) 0px, transparent 85%)
                    `,
                  }}
                >
                  <div className="flex items-center gap-2">
                    <IconLoader2 className="w-4 h-4 animate-spin text-gray-400" />
                    <span className="text-sm text-gray-300">AI analyseert gesprek...</span>
                  </div>
                </div>
              </div>
            )}
            {/* Primary Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {returnRequest.status === "processing" ? "Items verwerken" : "Actie vereist"}
                </CardTitle>
                <CardDescription>
                  {returnRequest.status === "processing"
                    ? "Markeer wanneer de items zijn ontvangen"
                    : "Kies wat je wilt doen met deze retour"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {returnRequest.status === "processing" ? (
                  <Button
                    onClick={() => handleDecision("items_received")}
                    disabled={actionLoading === "items_received"}
                    className="group relative overflow-hidden w-full h-12 bg-orange-600 hover:bg-orange-700"
                  >
                    <span className="flex items-center mr-8 transition-opacity duration-500 group-hover:opacity-0">
                      {actionLoading === "items_received" && (
                        <IconLoader2 className="w-4 h-4 mr-2 animate-spin" />
                      )}
                      <IconPackage className="w-4 h-4 mr-2" />
                      <div className="text-left">
                        <div className="font-medium">Items ontvangen</div>
                        <div className="text-xs opacity-90">Start terugbetaling</div>
                      </div>
                    </span>
                    <i className="absolute right-1 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-primary-foreground/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95 text-black-500">
                      <IconCheck size={16} strokeWidth={2} aria-hidden="true" />
                    </i>
                  </Button>
                ) : (
                  <>
                    <Button
                      onClick={() => handleDecision("approve")}
                      disabled={actionLoading === "approve"}
                      className="group relative overflow-hidden w-full h-12 bg-green-600 hover:bg-green-700"
                      size="lg"
                    >
                      <span className="flex items-center mr-8 transition-opacity duration-500 group-hover:opacity-0">
                        {actionLoading === "approve" && (
                          <IconLoader2 className="w-4 h-4 mr-2 animate-spin" />
                        )}
                        <div className="text-left">
                          <div className="font-medium">Goedkeuren</div>
                          <div className="text-xs opacity-90">Retour accepteren</div>
                        </div>
                      </span>
                      <i className="absolute right-1 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-primary-foreground/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95 text-black-500">
                        <IconCheck size={16} strokeWidth={2} aria-hidden="true" />
                      </i>
                    </Button>

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          className="group relative overflow-hidden w-full h-12 bg-transparent"
                        >
                          <span className="flex items-center mr-8 transition-opacity duration-500 group-hover:opacity-0">
                            <div className="text-left">
                              <div className="font-medium">Meer info vragen</div>
                              <div className="text-xs text-gray-500">Extra vragen stellen</div>
                            </div>
                          </span>
                          <i className="absolute right-1 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-primary-foreground/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95 text-black-500">
                            <IconQuestionMark size={16} strokeWidth={2} aria-hidden="true" />
                          </i>
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Wat wil je vragen?</DialogTitle>
                          <DialogDescription>
                            Stel aanvullende vragen om een betere beslissing te maken.
                          </DialogDescription>
                        </DialogHeader>
                        <Textarea
                          value={additionalQuestions}
                          onChange={(e) => setAdditionalQuestions(e.target.value)}
                          placeholder="Bijv: Kun je een foto sturen van het defect?"
                          rows={4}
                        />
                        <DialogFooter>
                          <Button
                            className="group relative overflow-hidden w-full h-12 bg-blue-600 hover:bg-blue-700"
                            onClick={() => handleDecision("ask_questions")}
                            disabled={
                              !additionalQuestions.trim() || actionLoading === "ask_questions"
                            }
                          >
                            <span className="flex items-center mr-8 transition-opacity duration-500 group-hover:opacity-0">
                              {actionLoading === "ask_questions" && (
                                <IconLoader2 className="w-4 h-4 mr-2 animate-spin" />
                              )}
                              <div className="text-left">
                                <div className="font-medium">Verstuur vragen</div>
                              </div>
                            </span>
                            <i className="absolute right-1 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-primary-foreground/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95 text-black-500">
                              <IconCheck size={16} strokeWidth={2} aria-hidden="true" />
                            </i>
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          className="group relative overflow-hidden w-full h-12 border-red-200 text-red-700 hover:bg-red-50 hover:text-red-700 bg-transparent"
                        >
                          <span className="flex items-center mr-8 transition-opacity duration-500 group-hover:opacity-0">
                            <div className="text-left">
                              <div className="font-medium">Afwijzen</div>
                              <div className="text-xs opacity-70">Retour weigeren</div>
                            </div>
                          </span>
                          <i className="absolute right-1 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-primary-foreground/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95 text-black-500">
                            <IconX size={16} strokeWidth={2} aria-hidden="true" />
                          </i>
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Waarom afwijzen?</DialogTitle>
                          <DialogDescription>
                            Geef een duidelijke reden voor de afwijzing.
                          </DialogDescription>
                        </DialogHeader>
                        <Textarea
                          value={denyReason}
                          onChange={(e) => setDenyReason(e.target.value)}
                          placeholder="Bijv: Product valt buiten retourbeleid..."
                          rows={3}
                        />
                        <DialogFooter>
                          <Button
                            variant="destructive"
                            className="group relative overflow-hidden w-full h-12"
                            onClick={() => handleDecision("deny")}
                            disabled={!denyReason.trim() || actionLoading === "deny"}
                          >
                            <span className="flex items-center mr-8 transition-opacity duration-500 group-hover:opacity-0">
                              {actionLoading === "deny" && (
                                <IconLoader2 className="w-4 h-4 mr-2 animate-spin" />
                              )}
                              <div className="text-left">
                                <div className="font-medium">Afwijzen</div>
                              </div>
                            </span>
                            <i className="absolute right-1 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-primary-foreground/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95 text-black-500">
                              <IconCheck size={16} strokeWidth={2} aria-hidden="true" />
                            </i>
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Internal Notes */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Interne notities</CardTitle>
                <CardDescription>Voor jouw team (klant ziet dit niet)</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={staffNotes}
                  onChange={(e) => setStaffNotes(e.target.value)}
                  placeholder="Voeg notities toe voor je team..."
                  rows={4}
                  className="resize-none"
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
