"use client";

import Link from "next/link";
import {
  IconCamera,
  IconAlertTriangle,
  IconAlertCircle,
  IconInfoCircle,
} from "@tabler/icons-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ActionCard {
  id: string;
  priority: "urgent" | "high" | "normal";
  type: "vision_review" | "oauth_expired" | "escalation" | "other";
  title: string;
  description: string;
  action: {
    label: string;
    href?: string;
    onClick?: () => void;
  };
}

interface ActionCardsProps {
  cards: ActionCard[];
}

export function ActionCards({ cards }: ActionCardsProps) {
  const getCardIcon = (type: ActionCard["type"]) => {
    switch (type) {
      case "vision_review":
        return <IconCamera className="h-5 w-5" />;
      case "oauth_expired":
        return <IconAlertTriangle className="h-5 w-5" />;
      case "escalation":
        return <IconAlertCircle className="h-5 w-5" />;
      default:
        return <IconInfoCircle className="h-5 w-5" />;
    }
  };

  const getCardStyles = (priority: ActionCard["priority"]) => {
    switch (priority) {
      case "urgent":
        return {
          border: "border-red-200 dark:border-red-800",
          bg: "bg-red-50 dark:bg-red-950/20",
          icon: "text-red-600 dark:text-red-400",
          pulse: true,
        };
      case "high":
        return {
          border: "border-orange-200 dark:border-orange-800",
          bg: "bg-orange-50 dark:bg-orange-950/20",
          icon: "text-orange-600 dark:text-orange-400",
          pulse: false,
        };
      default:
        return {
          border: "border-border",
          bg: "bg-background",
          icon: "text-muted-foreground",
          pulse: false,
        };
    }
  };

  if (cards.length === 0) {
    return null;
  }

  const displayCards = cards.slice(0, 5);
  const remainingCount = cards.length - 5;

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {displayCards.map((card) => {
          const styles = getCardStyles(card.priority);

          return (
            <Card
              key={card.id}
              className={cn(
                "relative overflow-hidden transition-all hover:shadow-md",
                styles.border,
                styles.bg,
              )}
            >
              {styles.pulse && (
                <div className="absolute inset-0 -z-10">
                  <div className="absolute inset-0 bg-red-400 opacity-10 animate-pulse" />
                </div>
              )}

              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className={cn("rounded-lg p-2", styles.bg)}>
                    <div className={styles.icon}>{getCardIcon(card.type)}</div>
                  </div>
                  {card.priority === "urgent" && (
                    <span className="text-xs font-medium text-red-600 dark:text-red-400 uppercase tracking-wider">
                      Urgent
                    </span>
                  )}
                </div>
                <CardTitle className="text-base mt-3">{card.title}</CardTitle>
                <CardDescription className="text-sm">{card.description}</CardDescription>
              </CardHeader>

              <CardContent>
                {card.action.href ? (
                  <Button asChild size="sm" className="w-full">
                    <Link href={card.action.href}>{card.action.label}</Link>
                  </Button>
                ) : (
                  <Button size="sm" className="w-full" onClick={card.action.onClick}>
                    {card.action.label}
                  </Button>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {remainingCount > 0 && (
        <div className="text-center">
          <Button variant="outline" size="sm">
            Bekijk alle {cards.length} acties
          </Button>
        </div>
      )}
    </div>
  );
}
