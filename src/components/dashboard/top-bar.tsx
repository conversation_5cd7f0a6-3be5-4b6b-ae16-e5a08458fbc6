"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import {
  IconCommand,
  IconSearch,
  IconBuilding,
  IconWorld,
  IconChevronDown,
} from "@tabler/icons-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface Site {
  id: string;
  name: string;
  url: string;
  platform?: "shopify" | "woocommerce" | "lightspeed" | null;
  orgId?: string | null;
}

interface Organization {
  id: string;
  name: string;
  slug: string;
  createdAt?: Date | null;
}

interface TopBarProps {
  currentSite: Site;
  currentOrg: Organization;
  sites: Site[];
  organizations: Organization[];
  user: {
    name: string;
    email: string;
    avatar?: string;
  };
  onOpenCommandPalette?: () => void;
}

export function TopBar({
  currentSite,
  currentOrg,
  sites,
  organizations,
  user,
  onOpenCommandPalette,
}: TopBarProps) {
  const router = useRouter();

  const handleSiteChange = (siteId: string) => {
    router.push(`/dashboard/${siteId}/home`);
  };

  const handleOrgChange = (orgId: string) => {
    // This would typically involve more complex logic to switch organization context
    // For now, we'll just navigate to the first site of the new org
    const newOrgSites = sites.filter((site) => {
      // You'd need to add orgId to Site interface and filter properly
      return true; // Placeholder
    });
    if (newOrgSites.length > 0) {
      router.push(`/dashboard/${newOrgSites[0].id}/home`);
    }
  };

  return (
    <header className="sticky top-0 z-50 flex h-14 items-center gap-4 border-b bg-background/80 backdrop-blur-sm px-6">
      {/* Command Palette Trigger */}
      <Button
        variant="outline"
        size="sm"
        className="relative h-8 w-full justify-start text-sm text-muted-foreground sm:w-64 sm:pr-12"
        onClick={onOpenCommandPalette}
      >
        <IconSearch className="mr-2 h-4 w-4" />
        <span className="hidden sm:inline-flex">Zoeken...</span>
        <span className="inline-flex sm:hidden">Zoek</span>
        <kbd className="pointer-events-none absolute right-1.5 top-1.5 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      <div className="flex flex-1 items-center justify-end gap-4">
        {/* Site Switcher */}
        {sites.length > 1 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-2">
                <IconWorld className="h-4 w-4" />
                <span className="hidden sm:inline-flex">{currentSite.name}</span>
                <IconChevronDown className="h-3 w-3 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Selecteer Site</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {sites.map((site) => (
                <DropdownMenuItem
                  key={site.id}
                  onClick={() => handleSiteChange(site.id)}
                  className={cn(
                    "flex items-center justify-between",
                    currentSite.id === site.id && "bg-secondary",
                  )}
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{site.name}</span>
                    <span className="text-xs text-muted-foreground">{site.url}</span>
                  </div>
                  {site.platform && (
                    <Badge variant="outline" className="text-xs">
                      {site.platform}
                    </Badge>
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Organization Switcher */}
        {organizations.length > 1 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-2">
                <IconBuilding className="h-4 w-4" />
                <span className="hidden sm:inline-flex">{currentOrg.name}</span>
                <IconChevronDown className="h-3 w-3 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Selecteer Organisatie</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {organizations.map((org) => (
                <DropdownMenuItem
                  key={org.id}
                  onClick={() => handleOrgChange(org.id)}
                  className={cn(currentOrg.id === org.id && "bg-secondary")}
                >
                  <span className="font-medium">{org.name}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="relative h-8 w-8 rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback>
                  {user.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .toUpperCase()}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{user.name}</p>
                <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Profiel</DropdownMenuItem>
            <DropdownMenuItem>Facturatie</DropdownMenuItem>
            <DropdownMenuItem>Team</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Uitloggen</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
