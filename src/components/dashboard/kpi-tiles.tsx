"use client";

import { useRouter } from "next/navigation";
import {
  IconMessage,
  IconPackage,
  IconClock,
  IconStar,
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
} from "@tabler/icons-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface KPIData {
  openChats: number;
  pendingReturns: number;
  avgResponseTime: string;
  csat: number;
}

interface KPITilesProps {
  data: KPIData;
  siteId: string;
}

export function KPITiles({ data, siteId }: KPITilesProps) {
  const router = useRouter();

  const tiles = [
    {
      title: "Open Chats",
      value: data.openChats,
      icon: IconMessage,
      change: 12,
      trend: "up" as const,
      onClick: () => router.push(`/dashboard/${siteId}/chats?filter=open`),
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Retouren in Behandeling",
      value: data.pendingReturns,
      icon: IconPackage,
      change: -5,
      trend: "down" as const,
      onClick: () => router.push(`/dashboard/${siteId}/returns?filter=pending`),
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
    {
      title: "Gem. Reactietijd",
      value: data.avgResponseTime,
      icon: IconClock,
      change: 0,
      trend: "neutral" as const,
      onClick: () => router.push(`/dashboard/${siteId}/chats`),
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "CSAT Score",
      value: `${data.csat}/5`,
      icon: IconStar,
      change: 3,
      trend: "up" as const,
      onClick: () => router.push(`/dashboard/${siteId}/chats`),
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
  ];

  const getTrendIcon = (trend: "up" | "down" | "neutral") => {
    switch (trend) {
      case "up":
        return <IconTrendingUp className="h-4 w-4" />;
      case "down":
        return <IconTrendingDown className="h-4 w-4" />;
      default:
        return <IconMinus className="h-4 w-4" />;
    }
  };

  const getTrendColor = (trend: "up" | "down" | "neutral", change: number) => {
    if (trend === "neutral") return "text-muted-foreground";
    if (trend === "up") return change > 0 ? "text-green-600" : "text-red-600";
    if (trend === "down") return change < 0 ? "text-green-600" : "text-red-600";
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {tiles.map((tile) => {
        const Icon = tile.icon;
        return (
          <Card
            key={tile.title}
            className="cursor-pointer transition-all hover:shadow-md hover:border-primary/20"
            onClick={tile.onClick}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {tile.title}
              </CardTitle>
              <div className={cn("rounded-lg p-2", tile.bgColor)}>
                <Icon className={cn("h-4 w-4", tile.color)} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{tile.value}</div>
              {tile.change !== 0 && (
                <div className="flex items-center gap-1 text-xs mt-1">
                  {getTrendIcon(tile.trend)}
                  <span className={getTrendColor(tile.trend, tile.change)}>
                    {Math.abs(tile.change)}%
                  </span>
                  <span className="text-muted-foreground">vs. gisteren</span>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
