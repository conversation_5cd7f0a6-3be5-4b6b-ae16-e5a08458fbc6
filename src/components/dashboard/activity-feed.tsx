"use client";

import { formatDistanceToNow } from "date-fns";
import { nl } from "date-fns/locale";
import {
  IconPackageExport,
  IconCheck,
  IconX,
  IconAlertCircle,
  IconMessage,
} from "@tabler/icons-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ActivityItem {
  id: string;
  type: "chat_closed" | "return_approved" | "return_denied" | "return_created" | "escalation";
  timestamp: Date;
  title: string;
  description?: string;
  metadata?: Record<string, any>;
}

interface ActivityFeedProps {
  activities: ActivityItem[];
}

export function ActivityFeed({ activities }: ActivityFeedProps) {
  const getActivityIcon = (type: ActivityItem["type"]) => {
    switch (type) {
      case "chat_closed":
        return <IconMessage className="h-4 w-4" />;
      case "return_approved":
        return <IconCheck className="h-4 w-4" />;
      case "return_denied":
        return <IconX className="h-4 w-4" />;
      case "return_created":
        return <IconPackageExport className="h-4 w-4" />;
      case "escalation":
        return <IconAlertCircle className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: ActivityItem["type"]) => {
    switch (type) {
      case "chat_closed":
        return "bg-blue-100 text-blue-700";
      case "return_approved":
        return "bg-green-100 text-green-700";
      case "return_denied":
        return "bg-red-100 text-red-700";
      case "return_created":
        return "bg-orange-100 text-orange-700";
      case "escalation":
        return "bg-yellow-100 text-yellow-700";
    }
  };

  const getStatusBadge = (status?: string) => {
    if (!status) return null;

    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      pending: "secondary",
      approved: "default",
      denied: "destructive",
      reviewing: "outline",
    };

    return (
      <Badge variant={variants[status] || "default"} className="text-xs">
        {status}
      </Badge>
    );
  };

  if (activities.length === 0) {
    return (
      <Card className="p-8 text-center">
        <p className="text-muted-foreground">Nog geen recente activiteit</p>
      </Card>
    );
  }

  // Group activities by date
  const groupedActivities = activities.reduce(
    (groups, activity) => {
      const date = new Date(activity.timestamp);
      const dateKey = date.toDateString();

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(activity);
      return groups;
    },
    {} as Record<string, ActivityItem[]>,
  );

  const today = new Date().toDateString();
  const yesterday = new Date(Date.now() - 86400000).toDateString();

  return (
    <div className="space-y-6">
      {Object.entries(groupedActivities).map(([dateKey, items]) => {
        let dateLabel = dateKey;
        if (dateKey === today) dateLabel = "Vandaag";
        else if (dateKey === yesterday) dateLabel = "Gisteren";
        else {
          const date = new Date(dateKey);
          dateLabel = date.toLocaleDateString("nl-NL", {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric",
          });
        }

        return (
          <div key={dateKey}>
            <h3 className="text-sm font-medium text-muted-foreground mb-3">{dateLabel}</h3>
            <div className="space-y-3">
              {items.map((activity) => (
                <Card key={activity.id} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className={cn("rounded-full p-2", getActivityColor(activity.type))}>
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium truncate">{activity.title}</p>
                        {activity.metadata?.status && getStatusBadge(activity.metadata.status)}
                      </div>
                      {activity.description && (
                        <p className="text-sm text-muted-foreground mt-1">{activity.description}</p>
                      )}
                      {activity.metadata?.customerEmail && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {activity.metadata.customerEmail}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground whitespace-nowrap">
                        {formatDistanceToNow(activity.timestamp, {
                          addSuffix: true,
                          locale: nl,
                        })}
                      </span>
                      <Button variant="ghost" size="sm">
                        Bekijk
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
}
