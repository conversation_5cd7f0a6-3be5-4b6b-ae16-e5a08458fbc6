"use client";

import { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  IconShoppingCart,
  IconPlug,
  IconPlugConnected,
  IconRefresh,
  IconExternalLink,
  IconAlertCircle,
} from "@tabler/icons-react";

interface Site {
  id: string;
  name: string;
  url: string;
  platform?: "shopify" | "woocommerce" | "lightspeed" | null;
  platformShopId?: string | null;
  platformToken?: string | null;
}

interface IntegrationsSettingsProps {
  site: Site;
  userRole: "owner" | "admin" | "agent";
}

export function IntegrationsSettings({ site, userRole }: IntegrationsSettingsProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);

  const canManageIntegrations = userRole === "owner" || userRole === "admin";
  const isShopifyConnected = site.platform === "shopify" && site.platformToken;

  const handleShopifyConnect = async () => {
    if (!canManageIntegrations) return;

    setIsConnecting(true);
    try {
      // Redirect to Shopify OAuth flow
      const response = await fetch(`/api/shopify/auth`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          siteId: site.id,
          shopUrl: site.url,
        }),
      });

      const data = await response.json();

      if (data.authUrl) {
        window.location.href = data.authUrl;
      } else {
        throw new Error("Failed to get authorization URL");
      }
    } catch (error) {
      toast.error("Er ging iets mis bij het verbinden met Shopify");
      console.error("Shopify connect error:", error);
      setIsConnecting(false);
    }
  };

  const handleShopifyDisconnect = async () => {
    if (!canManageIntegrations) return;

    setIsDisconnecting(true);
    try {
      const response = await fetch(`/api/shopify/disconnect`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          siteId: site.id,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to disconnect");
      }

      toast.success("Shopify verbinding verbroken");
      window.location.reload();
    } catch (error) {
      toast.error("Er ging iets mis bij het verbreken van de verbinding");
      console.error("Shopify disconnect error:", error);
    } finally {
      setIsDisconnecting(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      const response = await fetch(`/api/shopify/test`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          siteId: site.id,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success("Verbinding werkt correct!");
      } else {
        toast.error("Verbinding heeft problemen");
      }
    } catch (error) {
      toast.error("Er ging iets mis bij het testen van de verbinding");
      console.error("Test connection error:", error);
    }
  };

  return (
    <div className="space-y-6">
      {!canManageIntegrations && (
        <Alert>
          <IconAlertCircle className="h-4 w-4" />
          <AlertDescription>
            Je hebt geen rechten om integraties te beheren. Neem contact op met een admin.
          </AlertDescription>
        </Alert>
      )}

      {/* Shopify Integration */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <img
                src="https://cdn.iconscout.com/icon/free/png-256/free-shopify-logo-icon-download-in-svg-png-gif-file-formats--online-shopping-brand-logos-pack-icons-226579.png?f=webp"
                alt="Shopify"
                width={32}
                height={32}
              />
              <div>
                <CardTitle>Shopify</CardTitle>
                <CardDescription>
                  Verbind je Shopify store om bestellingen en retouren te beheren
                </CardDescription>
              </div>
            </div>
            {isShopifyConnected ? (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <IconPlugConnected className="w-3 h-3 mr-1" />
                Verbonden
              </Badge>
            ) : (
              <Badge variant="outline">
                <IconPlug className="w-3 h-3 mr-1" />
                Niet verbonden
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isShopifyConnected ? (
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                <p>
                  <strong>Shop ID:</strong> {site.platformShopId}
                </p>
                <p>
                  <strong>Shop URL:</strong> {site.url}
                </p>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTestConnection}
                  disabled={!canManageIntegrations}
                >
                  <IconRefresh className="w-4 h-4 mr-2" />
                  Test Verbinding
                </Button>

                <Button variant="outline" size="sm" asChild>
                  <a href={`https://${site.url}/admin`} target="_blank" rel="noopener noreferrer">
                    <IconExternalLink className="w-4 h-4 mr-2" />
                    Open Shopify Admin
                  </a>
                </Button>

                {canManageIntegrations && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleShopifyDisconnect}
                    disabled={isDisconnecting}
                  >
                    {isDisconnecting ? "Verbreken..." : "Verbreek Verbinding"}
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Verbind je Shopify store om je AI-agent toegang te geven tot bestellingen, producten
                en retouren. Dit is vereist voor volledige functionaliteit.
              </p>

              <Button
                onClick={handleShopifyConnect}
                disabled={!canManageIntegrations || isConnecting}
              >
                <IconShoppingCart className="w-4 h-4 mr-2" />
                {isConnecting ? "Verbinden..." : "Verbind met Shopify"}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Webhooks Status */}
      {isShopifyConnected && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Webhook Status</CardTitle>
            <CardDescription>
              Automatische synchronisatie van bestellingen en events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Orders Created</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Actief
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Orders Updated</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Actief
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Returns Created</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Actief
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Coming Soon Integrations */}
      <Card className="opacity-60">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">WooCommerce & Lightspeed</CardTitle>
              <CardDescription>Binnenkort beschikbaar</CardDescription>
            </div>
            <Badge variant="outline">Binnenkort</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            We werken aan integraties voor WooCommerce en Lightspeed. Meld je aan voor de wachtlijst
            om als eerste op de hoogte te worden gesteld.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
