"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  IconCreditCard,
  IconTrendingUp,
  IconAlertCircle,
  IconCheck,
  IconX,
} from "@tabler/icons-react";

interface Organization {
  id: string;
  name: string;
  slug: string;
  createdAt?: Date | null;
}

interface BillingSettingsProps {
  organization: Organization;
  userRole: "owner" | "admin" | "agent";
}

interface UsageData {
  chatMessages: number;
  apiCalls: number;
  emailsSent: number;
  limits: {
    chatMessages: number;
    apiCalls: number;
    emailsSent: number;
  };
}

const PLANS = [
  {
    id: "starter",
    name: "Starter",
    price: "€29",
    period: "per maand",
    features: ["1.000 chat berichten", "5.000 API calls", "100 emails", "1 site", "Basis support"],
    limits: {
      chatMessages: 1000,
      apiCalls: 5000,
      emailsSent: 100,
      sites: 1,
    },
  },
  {
    id: "professional",
    name: "Professional",
    price: "€99",
    period: "per maand",
    features: [
      "10.000 chat berichten",
      "25.000 API calls",
      "1.000 emails",
      "5 sites",
      "Priority support",
      "Advanced analytics",
    ],
    limits: {
      chatMessages: 10000,
      apiCalls: 25000,
      emailsSent: 1000,
      sites: 5,
    },
    popular: true,
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: "€299",
    period: "per maand",
    features: [
      "Unlimited chat berichten",
      "Unlimited API calls",
      "Unlimited emails",
      "Unlimited sites",
      "24/7 support",
      "Custom integrations",
      "SLA garantie",
    ],
    limits: {
      chatMessages: -1,
      apiCalls: -1,
      emailsSent: -1,
      sites: -1,
    },
  },
];

export function BillingSettings({ organization, userRole }: BillingSettingsProps) {
  const [usage, setUsage] = useState<UsageData | null>(null);
  const [currentPlan, setCurrentPlan] = useState("starter");
  const [isLoading, setIsLoading] = useState(true);

  const canManageBilling = userRole === "owner";

  useEffect(() => {
    fetchUsageData();
  }, [organization.id]);

  const fetchUsageData = async () => {
    try {
      const response = await fetch(`/api/organizations/${organization.id}/usage`);
      if (!response.ok) throw new Error("Failed to fetch usage");

      const data = await response.json();
      setUsage(data.usage);
      setCurrentPlan(data.plan || "starter");
    } catch (error) {
      toast.error("Er ging iets mis bij het ophalen van gebruiksgegevens");
      console.error("Fetch usage error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpgrade = async (planId: string) => {
    if (!canManageBilling) return;

    try {
      const response = await fetch(`/api/organizations/${organization.id}/billing/upgrade`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ planId }),
      });

      if (!response.ok) throw new Error("Failed to upgrade");

      const data = await response.json();

      if (data.checkoutUrl) {
        window.location.href = data.checkoutUrl;
      } else {
        toast.success("Plan bijgewerkt!");
        setCurrentPlan(planId);
      }
    } catch (error) {
      toast.error("Er ging iets mis bij het upgraden");
      console.error("Upgrade error:", error);
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return "text-red-600";
    if (percentage >= 75) return "text-yellow-600";
    return "text-green-600";
  };

  if (isLoading) {
    return <div>Billing gegevens laden...</div>;
  }

  return (
    <div className="space-y-6">
      {!canManageBilling && (
        <Alert>
          <IconAlertCircle className="h-4 w-4" />
          <AlertDescription>
            Je hebt geen rechten om billing te beheren. Neem contact op met de organisatie eigenaar.
          </AlertDescription>
        </Alert>
      )}

      {/* Current Usage */}
      {usage && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Huidig Gebruik</CardTitle>
            <CardDescription>Je gebruik deze maand voor {organization.name}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Chat Berichten</span>
                  <span
                    className={getUsageColor(
                      getUsagePercentage(usage.chatMessages, usage.limits.chatMessages),
                    )}
                  >
                    {usage.chatMessages.toLocaleString()} /{" "}
                    {usage.limits.chatMessages === -1
                      ? "∞"
                      : usage.limits.chatMessages.toLocaleString()}
                  </span>
                </div>
                <Progress
                  value={getUsagePercentage(usage.chatMessages, usage.limits.chatMessages)}
                  className="h-2"
                />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>API Calls</span>
                  <span
                    className={getUsageColor(
                      getUsagePercentage(usage.apiCalls, usage.limits.apiCalls),
                    )}
                  >
                    {usage.apiCalls.toLocaleString()} /{" "}
                    {usage.limits.apiCalls === -1 ? "∞" : usage.limits.apiCalls.toLocaleString()}
                  </span>
                </div>
                <Progress
                  value={getUsagePercentage(usage.apiCalls, usage.limits.apiCalls)}
                  className="h-2"
                />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Emails Verstuurd</span>
                  <span
                    className={getUsageColor(
                      getUsagePercentage(usage.emailsSent, usage.limits.emailsSent),
                    )}
                  >
                    {usage.emailsSent.toLocaleString()} /{" "}
                    {usage.limits.emailsSent === -1
                      ? "∞"
                      : usage.limits.emailsSent.toLocaleString()}
                  </span>
                </div>
                <Progress
                  value={getUsagePercentage(usage.emailsSent, usage.limits.emailsSent)}
                  className="h-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Available Plans */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Beschikbare Plannen</h3>
        <div className="grid md:grid-cols-3 gap-6">
          {PLANS.map((plan) => (
            <Card
              key={plan.id}
              className={`relative ${plan.popular ? "border-primary" : ""} ${currentPlan === plan.id ? "ring-2 ring-primary" : ""}`}
            >
              {plan.popular && (
                <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-primary">
                  Populair
                </Badge>
              )}
              {currentPlan === plan.id && (
                <Badge className="absolute -top-2 right-4 bg-green-600">Huidige Plan</Badge>
              )}

              <CardHeader className="text-center">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="text-3xl font-bold">
                  {plan.price}
                  <span className="text-sm font-normal text-muted-foreground">/{plan.period}</span>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <IconCheck className="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>

                {currentPlan === plan.id ? (
                  <Button className="w-full" disabled>
                    Huidige Plan
                  </Button>
                ) : (
                  <Button
                    className="w-full"
                    variant={plan.popular ? "default" : "outline"}
                    onClick={() => handleUpgrade(plan.id)}
                    disabled={!canManageBilling}
                  >
                    {PLANS.findIndex((p) => p.id === currentPlan) <
                    PLANS.findIndex((p) => p.id === plan.id)
                      ? "Upgrade"
                      : "Downgrade"}
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Factuurgeschiedenis</CardTitle>
          <CardDescription>Je laatste facturen en betalingen</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <IconCreditCard className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Nog geen facturen beschikbaar</p>
            <p className="text-sm">Facturen verschijnen hier na je eerste betaling</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
