"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { IconAlertTriangle, IconDeviceFloppy, IconRefresh } from "@tabler/icons-react";

interface AgentConfig {
  personalityPrompt: string;
  enabledTools: string[];
  toolPolicies: Record<string, string[]>;
  maxTurnsBeforeEscalation: number;
  escalationEnabled: boolean;
  escalationEmail: string;
  primaryModel: string;
  fallbackModel: string;
  temperature: number;
  reflectionEnabled: boolean;
  reflectionThreshold: number;
  businessHours: {
    timezone: string;
    schedule: Record<string, { start: string; end: string; enabled: boolean }>;
  };
}

interface AgentSettingsProps {
  siteId: string;
  config: AgentConfig;
  hasExistingConfig: boolean;
}

const AVAILABLE_TOOLS = [
  { id: "lookupOrder", name: "Bestellingen Opzoeken", description: "Zoek orderinformatie op" },
  { id: "createReturn", name: "Retouren Aanmaken", description: "Maak automatisch retouren aan" },
  {
    id: "checkInventory",
    name: "Voorraad Checken",
    description: "Controleer productbeschikbaarheid",
  },
  { id: "processRefund", name: "Terugbetalingen", description: "Verwerk terugbetalingen" },
  {
    id: "escalateToHuman",
    name: "Doorverwijzen",
    description: "Verwijs door naar menselijke agent",
  },
];

const MODEL_OPTIONS = [
  { value: "gpt-4o", label: "GPT-5 (Meest geavanceerd)" },
  { value: "gpt-5-mini", label: "GPT-5 Mini (Sneller & goedkoper)" },
  { value: "gpt-4-turbo", label: "GPT-4 Turbo (Deprecated)" },
];

export function AgentSettings({ siteId, config, hasExistingConfig }: AgentSettingsProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<AgentConfig>(config);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/shop-config`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          siteId,
          config: formData,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to save configuration");
      }

      toast.success("Agent configuratie opgeslagen!");
      router.refresh();
    } catch (error) {
      toast.error("Er ging iets mis bij het opslaan");
      console.error("Save error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTool = (toolId: string) => {
    setFormData((prev) => ({
      ...prev,
      enabledTools: prev.enabledTools.includes(toolId)
        ? prev.enabledTools.filter((id) => id !== toolId)
        : [...prev.enabledTools, toolId],
    }));
  };

  const updateToolPolicy = (toolId: string, policies: string[]) => {
    setFormData((prev) => ({
      ...prev,
      toolPolicies: {
        ...prev.toolPolicies,
        [toolId]: policies,
      },
    }));
  };

  return (
    <div className="space-y-6">
      {!hasExistingConfig && (
        <Alert>
          <IconAlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Er is nog geen configuratie opgeslagen. De onderstaande instellingen zijn
            standaardwaarden.
          </AlertDescription>
        </Alert>
      )}

      {/* Technical Agent Settings */}
      <div className="space-y-4">
        <div>
          <Label>Model Instellingen</Label>
          <div className="grid grid-cols-2 gap-4 mt-2">
            <div>
              <Label htmlFor="primary-model" className="text-sm">
                Primair Model
              </Label>
              <Select
                value={formData.primaryModel}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, primaryModel: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {MODEL_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="fallback-model" className="text-sm">
                Fallback Model
              </Label>
              <Select
                value={formData.fallbackModel}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, fallbackModel: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {MODEL_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div>
          <Label>Creativiteit (Temperature: {formData.temperature / 100})</Label>
          <Slider
            value={[formData.temperature]}
            onValueChange={([value]) => setFormData((prev) => ({ ...prev, temperature: value }))}
            max={100}
            min={0}
            step={5}
            className="mt-2"
          />
          <p className="text-sm text-muted-foreground mt-1">
            Lager = meer voorspelbaar, hoger = meer creatief
          </p>
        </div>
      </div>

      <Separator />

      {/* Tool Configuration */}
      <div className="space-y-4">
        <div>
          <Label>Beschikbare Tools</Label>
          <p className="text-sm text-muted-foreground">
            Selecteer welke acties je AI-agent mag uitvoeren
          </p>
        </div>

        <div className="grid gap-4">
          {AVAILABLE_TOOLS.map((tool) => (
            <Card
              key={tool.id}
              className={formData.enabledTools.includes(tool.id) ? "border-primary" : ""}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">{tool.name}</CardTitle>
                    <CardDescription>{tool.description}</CardDescription>
                  </div>
                  <Switch
                    checked={formData.enabledTools.includes(tool.id)}
                    onCheckedChange={() => toggleTool(tool.id)}
                  />
                </div>
              </CardHeader>
              {formData.enabledTools.includes(tool.id) && tool.id === "createReturn" && (
                <CardContent className="pt-0">
                  <Label className="text-sm font-medium">Retour Beleid</Label>
                  <div className="space-y-2 mt-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={formData.toolPolicies.createReturn?.includes(
                          "auto_approve_under_50",
                        )}
                        onCheckedChange={(checked) => {
                          const policies = formData.toolPolicies.createReturn || [];
                          if (checked) {
                            updateToolPolicy("createReturn", [
                              ...policies,
                              "auto_approve_under_50",
                            ]);
                          } else {
                            updateToolPolicy(
                              "createReturn",
                              policies.filter((p) => p !== "auto_approve_under_50"),
                            );
                          }
                        }}
                      />
                      <Label className="text-sm">Automatisch goedkeuren onder €50</Label>
                      <Badge variant="outline" className="text-xs">
                        Gebruik op eigen risico
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* Advanced Settings */}
      <div className="space-y-4">
        <Label>Geavanceerde Instellingen</Label>

        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm">Zelf-Reflectie</Label>
              <p className="text-xs text-muted-foreground">Agent controleert eigen antwoorden</p>
            </div>
            <Switch
              checked={formData.reflectionEnabled}
              onCheckedChange={(checked) =>
                setFormData((prev) => ({ ...prev, reflectionEnabled: checked }))
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm">Escalatie naar Mens</Label>
              <p className="text-xs text-muted-foreground">
                Na {formData.maxTurnsBeforeEscalation} berichten
              </p>
            </div>
            <Switch
              checked={formData.escalationEnabled}
              onCheckedChange={(checked) =>
                setFormData((prev) => ({ ...prev, escalationEnabled: checked }))
              }
            />
          </div>
        </div>

        {formData.escalationEnabled && (
          <div>
            <Label htmlFor="escalation-email">Escalatie Email</Label>
            <Input
              id="escalation-email"
              type="email"
              value={formData.escalationEmail}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, escalationEmail: e.target.value }))
              }
              placeholder="<EMAIL>"
            />
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={() => setFormData(config)} disabled={isLoading}>
          <IconRefresh className="w-4 h-4 mr-2" />
          Reset
        </Button>
        <Button onClick={handleSave} disabled={isLoading}>
          <IconDeviceFloppy className="w-4 h-4 mr-2" />
          {isLoading ? "Opslaan..." : "Opslaan"}
        </Button>
      </div>
    </div>
  );
}
