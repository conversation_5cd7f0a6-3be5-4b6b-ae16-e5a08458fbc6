"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { IconDeviceFloppy, IconRefresh } from "@tabler/icons-react";

interface BrandGuidelinesProps {
  siteId: string;
  currentPrompt: string;
}

const EXAMPLE_PROMPTS = [
  {
    title: "Informeel & Vriendelijk",
    content:
      "Spreek klanten aan met 'je' en gebruik een vriendelijke, persoonlijke toon. Gebruik emoji's waar gepast en wees behulpzaam zonder te formeel te zijn.",
  },
  {
    title: "Professioneel & Zakelijk",
    content:
      "Spreek klanten aan met 'u' en hanteer een professionele, respectvolle toon. Blijf altijd beleefd en zakelijk in je communicatie.",
  },
  {
    title: "Luxe & Premium",
    content:
      "Gebruik een verfijnde, elegante toon die past bij een premium merk. Spreek klanten aan met 'u' en toon expertise en exclusiviteit.",
  },
];

export function BrandGuidelines({ siteId, currentPrompt }: BrandGuidelinesProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [prompt, setPrompt] = useState(currentPrompt);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/shop-config`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          siteId,
          personalityPrompt: prompt,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to save brand guidelines");
      }

      toast.success("Brand guidelines opgeslagen!");
      router.refresh();
    } catch (error) {
      toast.error("Er ging iets mis bij het opslaan");
      console.error("Save error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExampleClick = (examplePrompt: string) => {
    setPrompt(examplePrompt);
  };

  return (
    <div className="space-y-6">
      <div>
        <Label htmlFor="brand-prompt">Brand Voice & Tone</Label>
        <Textarea
          id="brand-prompt"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="Beschrijf hoe je AI-agent je merk moet vertegenwoordigen..."
          className="min-h-[150px] mt-2"
        />
        <p className="text-sm text-muted-foreground mt-1">
          Deze instructies bepalen hoe je AI-agent klanten aanspreekt en je merkidentiteit
          uitdraagt.
        </p>
      </div>

      <div>
        <Label>Voorbeelden</Label>
        <p className="text-sm text-muted-foreground mb-3">
          Klik op een voorbeeld om het te gebruiken als basis
        </p>
        <div className="grid gap-3">
          {EXAMPLE_PROMPTS.map((example, index) => (
            <Card
              key={index}
              className="cursor-pointer hover:border-primary transition-colors"
              onClick={() => handleExampleClick(example.content)}
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">{example.title}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-xs">{example.content}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={() => setPrompt(currentPrompt)} disabled={isLoading}>
          <IconRefresh className="w-4 h-4 mr-2" />
          Reset
        </Button>
        <Button onClick={handleSave} disabled={isLoading}>
          <IconDeviceFloppy className="w-4 h-4 mr-2" />
          {isLoading ? "Opslaan..." : "Opslaan"}
        </Button>
      </div>
    </div>
  );
}
