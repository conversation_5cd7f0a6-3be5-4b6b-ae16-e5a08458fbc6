"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  IconPlus,
  IconTrash,
  IconMail,
  IconAlertCircle,
  IconCrown,
  IconShield,
  IconUser,
} from "@tabler/icons-react";

interface TeamMember {
  userId: string;
  user: {
    id: string;
    name: string | null;
    email: string;
    image: string | null;
  };
  role: "owner" | "admin" | "agent";
}

interface TeamSettingsProps {
  organizationId: string;
  userRole: "owner" | "admin" | "agent";
}

export function TeamSettings({ organizationId, userRole }: TeamSettingsProps) {
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isInviting, setIsInviting] = useState(false);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState<"admin" | "agent">("agent");

  const canManageTeam = userRole === "owner" || userRole === "admin";
  const canChangeRoles = userRole === "owner";

  useEffect(() => {
    fetchMembers();
  }, [organizationId]);

  const fetchMembers = async () => {
    try {
      const response = await fetch(`/api/organizations/${organizationId}/members`);
      if (!response.ok) throw new Error("Failed to fetch members");

      const data = await response.json();
      setMembers(data.members);
    } catch (error) {
      toast.error("Er ging iets mis bij het ophalen van teamleden");
      console.error("Fetch members error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInvite = async () => {
    if (!inviteEmail.trim() || !canManageTeam) return;

    setIsInviting(true);
    try {
      const response = await fetch(`/api/organizations/${organizationId}/invite`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: inviteEmail.trim(),
          role: inviteRole,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to send invite");
      }

      toast.success("Uitnodiging verstuurd!");
      setInviteEmail("");
      fetchMembers();
    } catch (error) {
      toast.error("Er ging iets mis bij het uitnodigen");
      console.error("Invite error:", error);
    } finally {
      setIsInviting(false);
    }
  };

  const handleRoleChange = async (userId: string, newRole: "owner" | "admin" | "agent") => {
    if (!canChangeRoles) return;

    try {
      const response = await fetch(`/api/organizations/${organizationId}/members/${userId}/role`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ role: newRole }),
      });

      if (!response.ok) throw new Error("Failed to update role");

      toast.success("Rol bijgewerkt!");
      fetchMembers();
    } catch (error) {
      toast.error("Er ging iets mis bij het bijwerken van de rol");
      console.error("Role change error:", error);
    }
  };

  const handleRemoveMember = async (userId: string) => {
    if (!canManageTeam) return;

    try {
      const response = await fetch(`/api/organizations/${organizationId}/members/${userId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to remove member");

      toast.success("Teamlid verwijderd!");
      fetchMembers();
    } catch (error) {
      toast.error("Er ging iets mis bij het verwijderen");
      console.error("Remove member error:", error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "owner":
        return <IconCrown className="w-4 h-4" />;
      case "admin":
        return <IconShield className="w-4 h-4" />;
      case "agent":
        return <IconUser className="w-4 h-4" />;
      default:
        return <IconUser className="w-4 h-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "owner":
        return "bg-yellow-100 text-yellow-800";
      case "admin":
        return "bg-blue-100 text-blue-800";
      case "agent":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return <div>Teamleden laden...</div>;
  }

  return (
    <div className="space-y-6">
      {!canManageTeam && (
        <Alert>
          <IconAlertCircle className="h-4 w-4" />
          <AlertDescription>
            Je hebt geen rechten om het team te beheren. Neem contact op met een admin.
          </AlertDescription>
        </Alert>
      )}

      {/* Invite New Member */}
      {canManageTeam && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Teamlid Uitnodigen</CardTitle>
            <CardDescription>Nodig nieuwe teamleden uit voor je organisatie</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4">
              <div className="flex-1">
                <Label htmlFor="invite-email">Email</Label>
                <Input
                  id="invite-email"
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="w-32">
                <Label htmlFor="invite-role">Rol</Label>
                <Select
                  value={inviteRole}
                  onValueChange={(value: "admin" | "agent") => setInviteRole(value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="agent">Agent</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    {userRole === "owner" && <SelectItem value="owner">Owner</SelectItem>}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button onClick={handleInvite} disabled={isInviting || !inviteEmail.trim()}>
                  <IconPlus className="w-4 h-4 mr-2" />
                  {isInviting ? "Uitnodigen..." : "Uitnodigen"}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Members */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Huidige Teamleden</CardTitle>
          <CardDescription>Beheer rollen en toegang van je teamleden</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {members.map((member) => (
              <div
                key={member.userId}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarImage src={member.user.image || ""} />
                    <AvatarFallback>
                      {member.user.name?.charAt(0) || member.user.email.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{member.user.name || "Geen naam"}</p>
                    <p className="text-sm text-muted-foreground">{member.user.email}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Badge className={getRoleColor(member.role)}>
                    {getRoleIcon(member.role)}
                    <span className="ml-1 capitalize">{member.role}</span>
                  </Badge>

                  {canChangeRoles && member.role !== "owner" && (
                    <Select
                      value={member.role}
                      onValueChange={(value: "owner" | "admin" | "agent") =>
                        handleRoleChange(member.userId, value)
                      }
                    >
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="agent">Agent</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        {userRole === "owner" && <SelectItem value="owner">Owner</SelectItem>}
                      </SelectContent>
                    </Select>
                  )}

                  {canManageTeam && member.role !== "owner" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveMember(member.userId)}
                    >
                      <IconTrash className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Role Descriptions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Rol Beschrijvingen</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Badge className="bg-yellow-100 text-yellow-800">
                <IconCrown className="w-3 h-3 mr-1" />
                Owner
              </Badge>
              <p className="text-sm text-muted-foreground">
                Volledige toegang tot alle instellingen, billing en teamleden beheer
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <Badge className="bg-blue-100 text-blue-800">
                <IconShield className="w-3 h-3 mr-1" />
                Admin
              </Badge>
              <p className="text-sm text-muted-foreground">
                Kan instellingen wijzigen, teamleden uitnodigen en sites beheren
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <Badge className="bg-gray-100 text-gray-800">
                <IconUser className="w-3 h-3 mr-1" />
                Agent
              </Badge>
              <p className="text-sm text-muted-foreground">
                Kan chats bekijken en retouren beheren, maar geen instellingen wijzigen
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
