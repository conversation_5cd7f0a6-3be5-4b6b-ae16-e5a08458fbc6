// src/components/AiButton/AiButton.tsx

import React from "react";
import styled from "styled-components";

// Definieer de mogelijke groottes
type ButtonSize = "sm" | "default" | "lg";

// De props voor ons component, inclusief de nieuwe 'size' prop
interface AiButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  size?: ButtonSize; // Voeg de size prop toe, optioneel
  // 'children' is al inbegrepen.
}

// Het SVG-icoon als een apart, klein component.
// We zullen de grootte van dit icoon afhankelijk maken van de button size via props.
const SparkleIcon = styled.svg<{ $size: ButtonSize }>`
  /* Basis icoon grootte */
  width: 32px;
  height: 32px;
  filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.25));

  /* Pas de grootte aan op basis van de $size prop */
  ${(props) =>
    props.$size === "sm" &&
    `
    width: 20px;
    height: 20px;
  `}
  ${(props) =>
    props.$size === "lg" &&
    `
    width: 40px;
    height: 40px;
  `}
`;

// De styled button component
const StyledAiButton = styled.button<{ $size: ButtonSize }>`
  display: flex;
  align-items: center;
  gap: 12px;
  border: none;
  cursor: pointer;
  position: relative;
  color: white;
  font-weight: 600;
  background: linear-gradient(
    135deg,
    #e84c9b 0%,
    #f78fb9 45%,
    #fdd8b5 100%
  );
  box-shadow:
    inset 0.5px 1.5px 2px rgba(255, 255, 255, 0.5),
    inset -1px -1px 3px rgba(0, 0, 0, 0.15),
    0 10px 25px rgba(232, 76, 155, 0.25);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;

  /* Dynamische styling op basis van de $size prop */
  ${(props) => {
    switch (props.$size) {
      case "sm":
        return `
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 14px;
          gap: 8px;
          box-shadow:
            inset 0.5px 1.5px 1px rgba(255, 255, 255, 0.4),
            inset -0.5px -0.5px 2px rgba(0, 0, 0, 0.1),
            0 5px 15px rgba(232, 76, 155, 0.2);
        `;
      case "lg":
        return `
          padding: 24px 48px;
          border-radius: 40px;
          font-size: 32px;
          gap: 16px;
          box-shadow:
            inset 1px 2px 3px rgba(255, 255, 255, 0.6),
            inset -1.5px -1.5px 4px rgba(0, 0, 0, 0.2),
            0 12px 30px rgba(232, 76, 155, 0.3);
        `;
      case "default": // Dit is de huidige "massieve" grootte
      default:
        return `
          padding: 20px 40px;
          border-radius: 36px;
          font-size: 28px;
          gap: 12px;
          box-shadow:
            inset 0.5px 1.5px 2px rgba(255, 255, 255, 0.5),
            inset -1px -1px 3px rgba(0, 0, 0, 0.15),
            0 10px 25px rgba(232, 76, 155, 0.25);
        `;
    }
  }}

  &:hover {
    transform: translateY(-3px) scale(1.03);
    /* De hover schaduwen moeten ook schalen met de grootte */
    ${(props) => {
      switch (props.$size) {
        case "sm":
          return `
            box-shadow:
              inset 0.5px 1.5px 2px rgba(255, 255, 255, 0.5),
              inset -0.5px -0.5px 3px rgba(0, 0, 0, 0.15),
              0 8px 20px rgba(232, 76, 155, 0.25);
          `;
        case "lg":
          return `
            box-shadow:
              inset 1px 2px 4px rgba(255, 255, 255, 0.7),
              inset -1.5px -1.5px 5px rgba(0, 0, 0, 0.25),
              0 18px 40px rgba(232, 76, 155, 0.4);
          `;
        case "default":
        default:
          return `
            box-shadow:
              inset 0.5px 1.5px 3px rgba(255, 255, 255, 0.6),
              inset -1px -1px 4px rgba(0, 0, 0, 0.2),
              0 15px 30px rgba(232, 76, 155, 0.3);
          `;
      }
    }}
  }
`;

export const AiButton: React.FC<AiButtonProps> = ({
  children,
  className,
  size = "default", // Default naar "default" size als het niet is opgegeven
  ...props
}) => {
  return (
    <StyledAiButton className={className} $size={size} {...props}>
      <SparkleIcon $size={size} viewBox="0 0 56 56" fill="white" xmlns="http://www.w3.org/2000/svg">
        <path d="M 26.6875 12.6602 C 26.9687 12.6602 27.1094 12.4961 27.1797 12.2383 C 27.9062 8.3242 27.8594 8.2305 31.9375 7.4570 C 32.2187 7.4102 32.3828 7.2461 32.3828 6.9648 C 32.3828 6.6836 32.2187 6.5195 31.9375 6.4726 C 27.8828 5.6524 28.0000 5.5586 27.1797 1.6914 C 27.1094 1.4336 26.9687 1.2695 26.6875 1.2695 C 26.4062 1.2695 26.2656 1.4336 26.1953 1.6914 C 25.3750 5.5586 25.5156 5.6524 21.4375 6.4726 C 21.1797 6.5195 20.9922 6.6836 20.9922 6.9648 C 20.9922 7.2461 21.1797 7.4102 21.4375 7.4570 C 25.5156 8.2774 25.4687 8.3242 26.1953 12.2383 C 26.2656 12.4961 26.4062 12.6602 26.6875 12.6602 Z M 15.3438 28.7852 C 15.7891 28.7852 16.0938 28.5039 16.1406 28.0821 C 16.9844 21.8242 17.1953 21.8242 23.6641 20.5821 C 24.0860 20.5117 24.3906 20.2305 24.3906 19.7852 C 24.3906 19.3633 24.0860 19.0586 23.6641 18.9883 C 17.1953 18.0977 16.9609 17.8867 16.1406 11.5117 C 16.0938 11.0899 15.7891 10.7852 15.3438 10.7852 C 14.9219 10.7852 14.6172 11.0899 14.5703 11.5352 C 13.7969 17.8164 13.4687 17.7930 7.0469 18.9883 C 6.6250 19.0821 6.3203 19.3633 6.3203 19.7852 C 6.3203 20.2539 6.6250 20.5117 7.1406 20.5821 C 13.5156 21.6133 13.7969 21.7774 14.5703 28.0352 C 14.6172 28.5039 14.9219 28.7852 15.3438 28.7852 Z M 31.2344 54.7305 C 31.8438 54.7305 32.2891 54.2852 32.4062 53.6524 C 34.0703 40.8086 35.8750 38.8633 48.5781 37.4570 C 49.2344 37.3867 49.6797 36.8945 49.6797 36.2852 C 49.6797 35.6758 49.2344 35.2070 48.5781 35.1133 C 35.8750 33.7070 34.0703 31.7617 32.4062 18.9180 C 32.2891 18.2852 31.8438 17.8633 31.2344 17.8633 C 30.6250 17.8633 30.1797 18.2852 30.0860 18.9180 C 28.4219 31.7617 26.5938 33.7070 13.9140 35.1133 C 13.2344 35.2070 12.7891 35.6758 12.7891 36.2852 C 12.7891 36.8945 13.2344 37.3867 13.9140 37.4570 C 26.5703 39.1211 28.3281 40.8321 30.0860 53.6524 C 30.1797 54.2852 30.6250 54.7305 31.2344 54.7305 Z"/>
      </SparkleIcon>
      <span>{children}</span>
    </StyledAiButton>
  );
};