/* src/components/AskAiButton/AskAiButton.module.css */

.askAiButton {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 40px;
  border: none;
  cursor: pointer;
  position: relative;
  border-radius: 36px;
  color: white;
  font-size: 28px;
  font-weight: 600;
  background: linear-gradient(
    135deg,
    #e84c9b 0%,
    #f78fb9 45%,
    #fdd8b5 100%
  );
  box-shadow:
    inset 0.5px 1.5px 2px rgba(255, 255, 255, 0.5),
    inset -1px -1px 3px rgba(0, 0, 0, 0.15),
    0 10px 25px rgba(232, 76, 155, 0.25);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.askAiButton:hover {
  transform: translateY(-3px) scale(1.03);
  box-shadow:
    inset 0.5px 1.5px 3px rgba(255, 255, 255, 0.6),
    inset -1px -1px 4px rgba(0, 0, 0, 0.2),
    0 15px 30px rgba(232, 76, 155, 0.3);
}

/* Stijl voor het icoon binnen de knop */
.icon {
  width: 32px;
  height: 32px;
  filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.25));
}