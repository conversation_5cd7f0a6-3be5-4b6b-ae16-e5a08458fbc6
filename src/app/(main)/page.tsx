"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  MessageSquare,
  Users,
  Clock,
  TrendingUp,
  Shield,
  Zap,
  CheckCircle,
  ArrowRight,
  Star,
  BarChart3,
  HeadphonesIcon,
  UserCheck,
  Menu,
  X,
} from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import Header2 from "@/components/mvpblocks/header-2";
import { gsap } from "gsap";
import { SplitText } from "gsap/SplitText";
import { AiButton } from "@/components/ui/AIButton";
import { CardHeader, CardContent, CardFooter } from "@/components/ui/card";

// Register GSAP plugins
gsap.registerPlugin(SplitText);

export default function LandingPage() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Ref for the main container
  const containerRef = useRef<HTMLDivElement>(null);

  // Refs for animation targets
  const heroRef = useRef<HTMLDivElement>(null);
  const badgeRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const gradientTextRef = useRef<HTMLSpanElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const navRef = useRef<HTMLElement>(null);

  const navLinks = [
    { href: "#product", label: "Product" },
    { href: "#prijzen", label: "Prijzen" },
    { href: "#resources", label: "Resources" },
    { href: "#blog", label: "Blog" },
  ];

  useEffect(() => {
    const ctx = gsap.context(
      () => {
        // 1. EERST alles op autoAlpha: 0 zetten (opacity + visibility)
        gsap.set([badgeRef.current, subtitleRef.current, buttonsRef.current], {
          autoAlpha: 0,
          y: 30,
        });

        gsap.set(navRef.current, { y: -20 });

        // 2. Split de titel
        const titleSplit = new SplitText(titleRef.current, {
          type: "words",
          wordsClass: "word",
        });

        gsap.set(titleSplit.words, { autoAlpha: 0, y: 50 });

        // 3. NU pas container zichtbaar maken
        gsap.set(containerRef.current, { visibility: "visible" });

        // 4. Animatie met autoAlpha
        const tl = gsap.timeline();

        tl.to(badgeRef.current, {
          autoAlpha: 1,
          y: 0,
          duration: 0.6,
          ease: "back.out(1.7)",
        })
          .to(
            titleSplit.words,
            {
              autoAlpha: 1,
              y: 0,
              duration: 0.8,
              stagger: 0.05,
              ease: "back.out(1.7)",
            },
            "-=0.3",
          )
          .to(
            subtitleRef.current,
            {
              autoAlpha: 1,
              y: 0,
              duration: 0.7,
              ease: "power2.out",
            },
            "-=0.4",
          )
          .to(
            buttonsRef.current,
            {
              autoAlpha: 1,
              y: 0,
              duration: 0.6,
              ease: "back.out(1.7)",
            },
            "-=0.3",
          );

        return () => titleSplit.revert();
      },
      { scope: containerRef },
    );

    return () => ctx.revert();
  }, []);

  return (
    // **KEY CHANGE**: Apply the ref and the new CSS class here
    <div ref={containerRef} className="min-h-screen invisible-on-load">
      {/* Sticky Navbar */}
      <nav ref={navRef} className="sticky top-0 z-50 w-full pt-4 px-4 md:px-6">
        <Header2 />
      </nav>

      {/* Hero Section Background */}
      <div
        ref={heroRef}
        className="bg-[url('/images/hero-bg.webp')] bg-cover bg-center bg-no-repeat min-h-screen -mt-20 pt-20 relative transition-all duration-1000 ease-out"
      >
        <div className="absolute bottom-0 left-0 right-0 h-60 bg-gradient-to-t from-white to-transparent"></div>

        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center px-6">
          <div className="mx-auto max-w-7xl text-center">
            <Badge
              ref={badgeRef}
              style={{ opacity: 0 }}
              className="mb-6 bg-primary-foreground text-primary border-primary-hover hover:bg-primary-hover transition-all duration-300"
            >
              🚀 Meer dan een chatbot - Een echte AI-medewerker
            </Badge>

            <h1
              ref={titleRef}
              className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-800 mb-6 leading-tight"
            >
              Jouw nieuwe{" "}
              <span ref={gradientTextRef} className="text-primary">
                AI-klantenservice
              </span>
              <br />
              medewerker is hier
            </h1>

            <p
              ref={subtitleRef}
              style={{ opacity: 0 }}
              className="text-lg sm:text-xl text-slate-600 mb-8 max-w-3xl mx-auto leading-relaxed"
            >
              Nousu is geen vraag-en-antwoord chatbot. Het is een intelligente AI-agent die
              functioneert als een volwaardig lid van jouw klantenserviceteam - met menselijke
              precisie en 24/7 beschikbaarheid.
            </p>

            <div
              ref={buttonsRef}
              style={{ opacity: 0 }}
              className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-primary to-primary/60 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 px-8 py-3 transform"
              >
                Gratis proberen
                <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-slate-300 text-slate-700 hover:bg-slate-50 px-8 py-3 transition-all duration-300 hover:-translate-y-1 transform"
              >
                Demo aanvragen
              </Button>
            </div>
          </div>
        </section>
      </div>

      {/* Trusted By Section
      <section className="py-16 bg-white">
        <div className="mx-auto max-w-7xl px-6">
          <p className="text-center text-slate-600 mb-8 font-medium">
            Vertrouwd door meer dan 500+ bedrijven
          </p>
          <div className="flex flex-wrap justify-center items-center gap-x-8 sm:gap-x-12 gap-y-6 opacity-60">
            <div className="text-xl sm:text-2xl font-bold text-slate-400">TechCorp</div>
            <div className="text-xl sm:text-2xl font-bold text-slate-400">InnovateBV</div>
            <div className="text-xl sm:text-2xl font-bold text-slate-400">ServicePro</div>
            <div className="text-xl sm:text-2xl font-bold text-slate-400">CustomerFirst</div>
            <div className="text-xl sm:text-2xl font-bold text-slate-400">SupportMax</div>
          </div>
        </div>
      </section>
      */}

      {/* Why Nousu is Different Section */}
      <section className="bg-white px-4 sm:px-8 md:px-16 lg:px-28 pt-8 md:pt-12">
        <div className="bg-[url('/images/differences-mobile.png')] md:bg-[url('/images/differences.png')] bg-cover pb-128 sm:py-20 md:py-28 bg-center bg-no-repeat rounded-xl">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-12 mt-2">
            <div className="text-left mb-8 md:mb-12 lg:mb-16 pt-8 md:pt-0">
              <Badge className="mb-4 bg-primary-foreground text-primary border-primary-hover">
                Het verschil
              </Badge>
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-slate-800 mb-4 md:mb-6">
                Waarom Nousu geen <br className="hidden sm:block" />
                gewone chatbot is
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-slate-600 max-w-3xl">
                Terwijl andere tools alleen antwoorden geven, <br className="hidden sm:block" />
                neemt Nousu daadwerkelijk actie als een echte medewerker
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6 sm:gap-8 md:gap-12 items-center">
              <div className="space-y-6 sm:space-y-8">
                {/* Feature 1 */}
                <div className="flex items-start space-x-3 sm:space-x-4">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary-foreground rounded-lg flex items-center justify-center flex-shrink-0">
                    <MessageSquare className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-lg sm:text-xl font-semibold text-slate-800 mb-2">
                      Gewone Chatbots
                    </h3>
                    <p className="text-sm sm:text-base text-slate-600">
                      Beantwoorden alleen vragen met vooraf ingestelde antwoorden. Klanten moeten
                      zelf actie ondernemen.
                    </p>
                  </div>
                </div>
                {/* Feature 2 */}
                <div className="flex items-start space-x-3 sm:space-x-4">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <UserCheck className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg sm:text-xl font-semibold text-slate-800 mb-2">
                      Nousu AI-Agent
                    </h3>
                    <p className="text-sm sm:text-base text-slate-600">
                      Neemt daadwerkelijk actie: verwerkt retouren, plant afspraken, lost problemen
                      op, en werkt samen met jouw systemen.
                    </p>
                  </div>
                </div>
              </div>
              {/* Chat Example - this should be responsive by nature of its internal flex/max-width
              <div className="bg-white border border-gray-200 rounded-lg">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 rounded-t-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">N</span>
                    </div>
                    <div>
                      <div className="font-medium text-sm">Nousu AI-Agent</div>
                      <div className="text-xs text-green-600 flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Online
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 space-y-3">
                  <div className="flex gap-2">
                    <Avatar className="w-6 h-6 mt-1 flex-shrink-0">
                      <AvatarFallback className="text-xs bg-white border-2 border-gray-100">
                        LB
                      </AvatarFallback>
                    </Avatar>
                    <div className="bg-gray-100 rounded-lg p-2 max-w-xs">
                      <div className="text-xs text-gray-600 mb-0.5">Lisa Bakker • 14:32</div>
                      <div className="text-sm">
                        Hallo, ik wil graag mijn bestelling van vorige week retourneren. Het product
                        past niet goed.
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2 justify-end">
                    <div className="bg-primary text-white rounded-lg p-2 max-w-xs">
                      <div className="text-xs opacity-80 mb-0.5">Nousu AI • 14:32</div>
                      <div className="text-sm">
                        Hallo Lisa! Ik zie je bestelling #4829 van 19 juni. Ik ga direct je retour
                        verwerken.
                      </div>
                    </div>
                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                      <span className="text-white text-xs">N</span>
                    </div>
                  </div>

                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-2">
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-3 h-3 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-xs font-medium text-orange-700">
                        Bezig met verwerken...
                      </span>
                    </div>
                    <div className="text-xs text-orange-600 space-y-0.5">
                      <div className="flex items-center gap-1">
                        <CheckCircle className="w-3 h-3" />
                        Bestelling gevonden
                      </div>
                      <div className="flex items-center gap-1">
                        <CheckCircle className="w-3 h-3" />
                        Retourlabel aangemaakt
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 border border-orange-500 rounded-full"></div>
                        Terugbetaling verwerken...
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2 justify-end">
                    <div className="bg-primary text-white rounded-lg p-2 max-w-xs">
                      <div className="text-xs opacity-80 mb-0.5">Nousu AI • 14:33</div>
                      <div className="text-sm">
                        ✅ Klaar! Je retour is verwerkt. Je krijgt €89,99 terug binnen 3-5 werkdagen.
                        Het retourlabel is naar je e-mail gestuurd.
                      </div>
                    </div>
                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                      <span className="text-white text-xs">N</span>
                    </div>
                  </div>
                </div>
              </div>*/}
            </div>
          </div>
        </div>
      </section>

      {/* Capabilities Section */}
      <section className="py-16 md:py-20 bg-white">
        <div className="mx-auto max-w-7xl px-6">
          <div className="text-center mb-12 md:mb-16">
            <Badge className="mb-4 bg-blue-50 text-blue-600 border-blue-200">Mogelijkheden</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-6">
              Wat jouw nieuwe AI-medewerker kan
            </h2>
          </div>

          <div className="grid sm:grid-cols-1 md:grid-cols-3 gap-8">
            {/* Card 1 */}
            <Card className="grid grid-rows-[auto_auto_1fr_auto] bg-white rounded-lg border-primary/30 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1  pb-6 pt-0">
              <div className="w-full">
                <a
                  href="/"
                  target="_blank"
                  className=""
                >
                  <img
                    src="images/nousu-emotions.png"
                    alt="Nousu Emotions"
                    className="h-full w-full object-contain object-center rounded-lg"
                  />
                </a>
              </div>
              <CardHeader>
                <h3 className="text-lg font-semibold md:text-xl">
                    Complexe gesprekken voeren
                </h3>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Begrijpt context, emoties en nuances. Voert natuurlijke gesprekken zoals een ervaren medewerker.
              </p>
              </CardContent>
            </Card>
            {/* Card 2 */}
      <Card className="grid grid-rows-[auto_auto_1fr_auto] bg-white rounded-lg border-blue-100 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1  pb-6 pt-0">
              <div className="w-full">
                <a
                  href="/"
                  target="_blank"
                  className=""
                >
                  <img
                    src="images/nousu-connected.png"
                    alt="Nousu DB"
                    className="h-full w-full object-contain object-center rounded-lg"
                  />
                </a>
              </div>
              <CardHeader>
                <h3 className="text-lg font-semibold md:text-xl">
                    Directe acties uitvoeren
                </h3>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Integreert met jouw systemen om daadwerkelijk taken uit te voeren, niet alleen informatie te geven.</p>
              </CardContent>
            </Card>
            {/* Card 3 */}
            <Card className="grid grid-rows-[auto_auto_1fr_auto] bg-white rounded-lg border-green-100 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1  pb-6 pt-0">
              <div className="w-full">
                <a
                  href="/"
                  target="_blank"
                  className=""
                >
                  <img
                    src="images/nousu-feedback.png"
                    alt="Nousu Feedback"
                    className="h-full w-full object-contain object-center rounded-lg"
                  />
                </a>
              </div>
              <CardHeader>
                <h3 className="text-lg font-semibold md:text-xl">
                  Continu leren & verbeteren
                </h3>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Wordt elke dag beter door te leren van interacties en feedback van jouw team.
              </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Why Teams Choose Nousu Section */}
      <section className="py-16 md:py-20 bg-slate-50">
        <div className="mx-auto max-w-7xl px-6">
          <div className="text-center mb-12 md:mb-16">
            <Badge className="mb-4 bg-green-50 text-green-600 border-green-200">Voordelen</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-6">
              Waarom teams kiezen voor Nousu
            </h2>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Stat 1 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-foreground rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-2xl font-bold text-slate-800 mb-2">80%</h3>
              <p className="text-slate-600">Minder wachttijd voor klanten</p>
            </div>
            {/* Stat 2 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold text-slate-800 mb-2">3x</h3>
              <p className="text-slate-600">Meer tickets opgelost per dag</p>
            </div>
            {/* Stat 3 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-slate-800 mb-2">95%</h3>
              <p className="text-slate-600">Klanttevredenheid score</p>
            </div>
            {/* Stat 4 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-2xl font-bold text-slate-800 mb-2">24/7</h3>
              <p className="text-slate-600">Beschikbaarheid zonder pauzes</p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 md:py-20 bg-white">
        <div className="mx-auto max-w-7xl px-6">
          <div className="text-center mb-12 md:mb-16">
            <Badge className="mb-4 bg-yellow-50 text-yellow-600 border-yellow-200">
              Testimonials
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-6">
              Wat onze klanten zeggen
            </h2>
          </div>

          <div className="grid sm:grid-cols-1 md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <Card className="p-6 shadow-lg">
              <div className="flex items-center mb-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-slate-600 mb-4">
                &quot;Nousu heeft ons team getransformeerd. Het voelt echt alsof we een extra
                medewerker hebben die nooit moe wordt.&quot;
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-primary-foreground rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                  <span className="text-primary font-semibold">MJ</span>
                </div>
                <div>
                  <p className="font-semibold text-slate-800">Maria Janssen</p>
                  <p className="text-sm text-slate-600">Customer Success Manager</p>
                </div>
              </div>
            </Card>
            {/* Testimonial 2 */}
            <Card className="p-6 shadow-lg">
              <div className="flex items-center mb-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-slate-600 mb-4">
                &quot;De integratie was naadloos en binnen een week zagen we al resultaten. Onze
                klanten zijn veel tevredener.&quot;
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                  <span className="text-blue-600 font-semibold">PV</span>
                </div>
                <div>
                  <p className="font-semibold text-slate-800">Pieter van Berg</p>
                  <p className="text-sm text-slate-600">Operations Director</p>
                </div>
              </div>
            </Card>
            {/* Testimonial 3 */}
            <Card className="p-6 shadow-lg">
              <div className="flex items-center mb-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-slate-600 mb-4">
                &quot;Eindelijk een AI-tool die daadwerkelijk werk uit handen neemt in plaats van
                alleen antwoorden te geven.&quot;
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                  <span className="text-green-600 font-semibold">SK</span>
                </div>
                <div>
                  <p className="font-semibold text-slate-800">Sophie Koster</p>
                  <p className="text-sm text-slate-600">Head of Customer Service</p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-20 bg-gradient-to-r from-slate-800 to-slate-700 text-white">
        <div className="mx-auto max-w-7xl px-6 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Klaar om jouw nieuwe AI-medewerker te ontmoeten?
          </h2>
          <p className="text-lg md:text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Start vandaag nog met een gratis proefperiode en ervaar het verschil van een echte
            AI-agent.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-gradient-to-r from-primary to-primary/60 text-white shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-0.5 px-8 py-3"
            >
              Gratis proberen - 14 dagen
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="bg-slate-700 hover:bg-slate-600 border-slate-600 hover:border-slate-500 text-white hover:text-white px-8 py-3"
            >
              Demo inplannen
            </Button>
          </div>
          <p className="text-sm text-slate-400 mt-4">
            Geen creditcard vereist • Setup in 5 minuten
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white py-12 md:py-16">
        <div className="mx-auto max-w-7xl px-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 mb-8">
            {/* Column 1: Logo & Slogan */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">N</span>
                </div>
                <span className="text-xl font-semibold">Nousu</span>
              </div>
              <p className="text-sm text-slate-400">
                De AI-agent die daadwerkelijk werkt als een teamlid.
              </p>
            </div>
            {/* Column 2: Product Links */}
            <div>
              <h4 className="font-semibold mb-4 text-slate-200">Product</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Functies
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Integraties
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    API
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Beveiliging
                  </a>
                </li>
              </ul>
            </div>
            {/* Column 3: Bedrijf Links */}
            <div>
              <h4 className="font-semibold mb-4 text-slate-200">Bedrijf</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Over ons
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Carrières
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Contact
                  </a>
                </li>
              </ul>
            </div>
            {/* Column 4: Support Links */}
            <div>
              <h4 className="font-semibold mb-4 text-slate-200">Support</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Documentatie
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Status
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-400 hover:text-white">
                    Privacy
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-800 mt-8 pt-8 text-center text-sm text-slate-400">
            <p>© {new Date().getFullYear()} Nousu. Alle rechten voorbehouden.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
