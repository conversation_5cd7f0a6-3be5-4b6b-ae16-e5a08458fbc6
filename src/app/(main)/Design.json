{"designSystem": {"name": "Modern AI SaaS Platform", "category": "B2B SaaS Landing Page", "version": "1.0", "description": "Clean, modern design system for AI-powered business tools with emphasis on trust, professionalism, and data visualization"}, "colorPalette": {"primary": {"brand": "#FF6B6B", "brandSecondary": "#FF8E8E", "description": "Coral/salmon red for primary actions and brand elements"}, "neutral": {"background": "#F8FAFC", "backgroundSecondary": "#FFFFFF", "backgroundTertiary": "#E2E8F0", "text": "#1E293B", "textSecondary": "#64748B", "textMuted": "#94A3B8", "border": "#E2E8F0", "borderLight": "#F1F5F9"}, "accent": {"blue": "#3B82F6", "blueLight": "#DBEAFE", "purple": "#8B5CF6", "purpleLight": "#EDE9FE", "green": "#10B981", "greenLight": "#D1FAE5", "orange": "#F59E0B", "orangeLight": "#FEF3C7"}, "gradients": {"backgroundGradient": "linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%)", "cardGradient": "linear-gradient(145deg, #FFFFFF 0%, #F8FAFC 100%)", "buttonGradient": "linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)"}}, "typography": {"fontFamilies": {"primary": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "headings": "Inter, -apple-system, BlinkMacSystemFont, sans-serif", "monospace": "'SF Mono', Consolas, monospace"}, "scale": {"hero": {"fontSize": "3.5rem", "lineHeight": "1.1", "fontWeight": "700", "letterSpacing": "-0.02em"}, "h1": {"fontSize": "2.5rem", "lineHeight": "1.2", "fontWeight": "600", "letterSpacing": "-0.01em"}, "h2": {"fontSize": "2rem", "lineHeight": "1.3", "fontWeight": "600"}, "h3": {"fontSize": "1.5rem", "lineHeight": "1.4", "fontWeight": "500"}, "body": {"fontSize": "1rem", "lineHeight": "1.6", "fontWeight": "400"}, "small": {"fontSize": "0.875rem", "lineHeight": "1.5", "fontWeight": "400"}, "caption": {"fontSize": "0.75rem", "lineHeight": "1.4", "fontWeight": "500", "textTransform": "uppercase", "letterSpacing": "0.05em"}}}, "spacing": {"scale": [0, 4, 8, 12, 16, 20, 24, 32, 40, 48, 64, 80, 96, 128, 160, 192, 224, 256], "containerMaxWidth": "1200px", "contentMaxWidth": "800px", "sectionPadding": {"mobile": "48px 16px", "desktop": "96px 24px"}}, "components": {"buttons": {"primary": {"background": "linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)", "color": "#FFFFFF", "padding": "12px 24px", "borderRadius": "8px", "fontWeight": "500", "fontSize": "0.875rem", "border": "none", "boxShadow": "0 2px 4px rgba(255, 107, 107, 0.2)", "hoverTransform": "translateY(-1px)", "hoverBoxShadow": "0 4px 8px rgba(255, 107, 107, 0.3)"}, "secondary": {"background": "#FFFFFF", "color": "#64748B", "padding": "12px 24px", "borderRadius": "8px", "fontWeight": "500", "fontSize": "0.875rem", "border": "1px solid #E2E8F0", "boxShadow": "0 1px 2px rgba(0, 0, 0, 0.05)"}}, "cards": {"primary": {"background": "#FFFFFF", "borderRadius": "12px", "boxShadow": "0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)", "border": "1px solid #F1F5F9", "padding": "24px"}, "feature": {"background": "linear-gradient(145deg, #FFFFFF 0%, #F8FAFC 100%)", "borderRadius": "16px", "boxShadow": "0 8px 25px rgba(0, 0, 0, 0.08)", "border": "1px solid #E2E8F0", "padding": "32px"}, "testimonial": {"background": "#FFFFFF", "borderRadius": "12px", "boxShadow": "0 2px 4px rgba(0, 0, 0, 0.04)", "border": "1px solid #F1F5F9", "padding": "20px"}}, "navigation": {"header": {"background": "rgba(255, 255, 255, 0.8)", "backdropFilter": "blur(10px)", "borderBottom": "1px solid rgba(226, 232, 240, 0.6)", "padding": "16px 24px", "position": "sticky"}, "logo": {"display": "flex", "alignItems": "center", "fontSize": "1.25rem", "fontWeight": "600", "color": "#1E293B"}}, "dataVisualization": {"chartContainer": {"background": "#FFFFFF", "borderRadius": "12px", "padding": "24px", "boxShadow": "0 2px 4px rgba(0, 0, 0, 0.04)", "border": "1px solid #F1F5F9"}, "progressBars": {"background": "#F1F5F9", "borderRadius": "6px", "height": "8px", "fillColors": ["#FF6B6B", "#3B82F6", "#10B981", "#F59E0B", "#8B5CF6"]}, "metrics": {"fontSize": "2rem", "fontWeight": "700", "color": "#1E293B", "label": {"fontSize": "0.875rem", "color": "#64748B", "fontWeight": "500"}}}, "forms": {"input": {"background": "#FFFFFF", "border": "1px solid #E2E8F0", "borderRadius": "8px", "padding": "12px 16px", "fontSize": "0.875rem", "focusBorder": "#FF6B6B", "focusBoxShadow": "0 0 0 3px rgba(255, 107, 107, 0.1)"}, "searchBar": {"background": "#F8FAFC", "border": "2px solid #FF6B6B", "borderRadius": "50px", "padding": "12px 20px", "fontSize": "0.875rem", "placeholder": "#94A3B8"}}}, "layout": {"grid": {"columns": 12, "gap": "24px", "containerPadding": "0 24px"}, "sections": {"hero": {"minHeight": "70vh", "display": "flex", "alignItems": "center", "textAlign": "center", "background": "linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%)"}, "features": {"padding": "96px 0", "background": "#FFFFFF"}, "testimonials": {"padding": "96px 0", "background": "#F8FAFC"}, "cta": {"padding": "80px 0", "background": "linear-gradient(135deg, #1E293B 0%, #334155 100%)", "color": "#FFFFFF"}}}, "animations": {"transitions": {"default": "all 0.2s ease-in-out", "slow": "all 0.3s ease-in-out", "hover": "transform 0.2s ease-in-out"}, "effects": {"fadeIn": "opacity 0.6s ease-in-out", "slideUp": "transform 0.4s ease-out", "cardHover": "transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out"}}, "patterns": {"contentStructure": {"heroSection": {"order": 1, "elements": ["headline", "subheadline", "ctaButtons", "heroVisual"]}, "featuresSection": {"order": 2, "elements": ["sectionTitle", "featureGrid", "dataVisualization"]}, "socialProofSection": {"order": 3, "elements": ["<PERSON><PERSON><PERSON><PERSON>", "testimonialCards", "metrics"]}, "benefitsSection": {"order": 4, "elements": ["benefitCards", "screenshots", "details"]}, "faqSection": {"order": 5, "elements": ["accordionItems", "supportLinks"]}, "ctaSection": {"order": 6, "elements": ["finalCta", "contactForm"]}}, "visualHierarchy": {"emphasis": "Large typography, bright colors, central positioning", "supporting": "Medium sizing, muted colors, consistent spacing", "background": "Subtle gradients, soft shadows, minimal contrast"}, "dataPresentation": {"charts": "Clean line charts and bar charts with branded colors", "metrics": "Large numbers with descriptive labels", "dashboards": "Card-based layout with consistent spacing", "progress": "Circular and linear progress indicators"}}, "brandPersonality": {"tone": "Professional yet approachable", "values": ["Innovation", "Reliability", "Efficiency", "Human-centric"], "visualStyle": "Clean, modern, data-driven, trustworthy", "targetAudience": "B2B professionals, sales teams, data-driven decision makers"}, "responsive": {"breakpoints": {"mobile": "320px", "tablet": "768px", "desktop": "1024px", "wide": "1200px"}, "adaptations": {"mobile": {"typography": "Reduce font sizes by 20%", "spacing": "Reduce padding by 50%", "layout": "Single column, stack elements"}, "tablet": {"typography": "Reduce font sizes by 10%", "spacing": "Reduce padding by 25%", "layout": "Two column grid"}}}}