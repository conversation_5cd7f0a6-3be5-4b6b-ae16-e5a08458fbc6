@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@font-face {
  font-family: "Pogonia Semibold";
  src: url("/fonts/pogonia-semibold.otf") format("opentype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Pogonia Bold";
  src: url("/fonts/pogonia-bold.otf") format("opentype");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "TT Hoves Pro Medium";
  src: url("/fonts/TT Hoves Pro Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@layer base {
  h1 {
    font-family: "TT Hoves Pro Medium", sans-serif;
  }
  h2 {
    font-family: "TT Hoves Pro Medium", sans-serif;
  }
  h3 {
    font-family: "TT Hoves Pro Medium", sans-serif;
  }
}

.invisible-on-load {
  visibility: hidden;
}

:root {
  --background: oklch(0.983 0.004 257.14);
  --foreground: oklch(0.229 0.025 257.14);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.229 0.025 257.14);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.229 0.025 257.14);
  --primary: oklch(0.704 0.191 22.22);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.9294 0.049 76.12);
  --secondary-foreground: oklch(0.51 0.02 257.14);
  --muted: oklch(0.96 0.006 257.14);
  --muted-foreground: oklch(0.678 0.015 257.14);
  --accent: oklch(0.913 0.043 257.14);
  --accent-foreground: oklch(0.627 0.156 257.14);
  --destructive: oklch(0.704 0.191 22.22);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.921 0.009 257.14);
  --input: oklch(0.921 0.009 257.14);
  --ring: oklch(0.704 0.191 22.22);
  --chart-1: oklch(0.704 0.191 22.22);
  --chart-2: oklch(0.627 0.156 257.14);
  --chart-3: oklch(0.713 0.131 161.41);
  --chart-4: oklch(0.754 0.142 84.05);
  --chart-5: oklch(0.67 0.188 288.56);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --font-sans: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: "SF Mono", Consolas, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 2px 0 hsl(0 0% 0% / 0.02);
  --shadow-xs: 0 1px 2px 0 hsl(0 0% 0% / 0.04);
  --shadow-sm: 0 1px 2px 0 hsl(0 0% 0% / 0.05);
  --shadow: 0 2px 4px 0 hsl(0 0% 0% / 0.04);
  --shadow-md: 0 4px 6px 0 hsl(0 0% 0% / 0.05), 0 1px 3px 0 hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 8px 25px 0 hsl(0 0% 0% / 0.08);
  --shadow-xl: 0 12px 30px 0 hsl(0 0% 0% / 0.09);
  --shadow-2xl: 0 20px 50px 0 hsl(0 0% 0% / 0.1);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.269 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.9294 0.049 76.12);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.371 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(0.985 0 0);
  --border: oklch(0.275 0 0);
  --input: oklch(0.325 0 0);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.81 0.1 252);
  --chart-2: oklch(0.62 0.19 260);
  --chart-3: oklch(0.55 0.22 263);
  --chart-4: oklch(0.49 0.22 264);
  --chart-5: oklch(0.42 0.18 266);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.275 0 0);
  --sidebar-ring: oklch(0.439 0 0);
  --font-sans: Poppins, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0 1px 50px 1px hsl(0 0% 0% / 0.02);
  --shadow-xs: 0 1px 50px 1px hsl(0 0% 0% / 0.02);
  --shadow-sm: 0 1px 50px 1px hsl(0 0% 0% / 0.04), 0 1px 2px 0px hsl(0 0% 0% / 0.04);
  --shadow: 0 1px 50px 1px hsl(0 0% 0% / 0.04), 0 1px 2px 0px hsl(0 0% 0% / 0.04);
  --shadow-md: 0 1px 50px 1px hsl(0 0% 0% / 0.04), 0 2px 4px 0px hsl(0 0% 0% / 0.04);
  --shadow-lg: 0 1px 50px 1px hsl(0 0% 0% / 0.04), 0 4px 6px 0px hsl(0 0% 0% / 0.04);
  --shadow-xl: 0 1px 50px 1px hsl(0 0% 0% / 0.04), 0 8px 10px 0px hsl(0 0% 0% / 0.04);
  --shadow-2xl: 0 1px 50px 1px hsl(0 0% 0% / 0.1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations for delightful interactions */
@layer utilities {
  .hover\:scale-102:hover {
    transform: scale(1.02);
  }

  .hover\:scale-101:hover {
    transform: scale(1.01);
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-wiggle {
    animation: wiggle 1s ease-in-out infinite;
  }
}

/* Keyframes for custom animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes wiggle {
  0%,
  100% {
    transform: rotate(-3deg);
  }
  50% {
    transform: rotate(3deg);
  }
}
