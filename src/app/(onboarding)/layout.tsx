import type { Metada<PERSON> } from "next";
import { Toaster } from "react-hot-toast";

export const metadata: Metadata = {
  title: "Onboarding - Nousu",
  description: "Onboarding with your AI agent",
};

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-background">
      <div>
        <Toaster />
      </div>
      {children}
    </div>
  );
}
