"use client";

import type React from "react";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Building } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "react-hot-toast";

export default function OnboardingPage() {
  const [orgName, setOrgName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleCreateOrg = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const response = await fetch("/api/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: orgName }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create organization");
      }

      toast.success("Organisatie succesvol aangemaakt!");

      router.push("/onboarding/site");
    } catch (error) {
      console.error("Organization creation failed:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create organization");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-[url('/images/hero-bg.webp')] bg-cover bg-center bg-no-repeat min-h-screen -mt-20 pt-20 relative transition-all duration-1000 ease-out">
      <div className="absolute bottom-0 left-0 right-0 h-60 bg-gradient-to-t from-white to-transparent"></div>
      <div className="min-h-screen flex items-center justify-center p-6 bg-transparent overflow-hidden">
        <div className="relative w-full max-w-md space-y-8 text-center">
          {/* Glassmorphism container */}
          <div className="backdrop-blur-xl bg-white/10 dark:bg-white/5 border border-gray-200/50 border-2 dark:border-white/10 rounded-3xl p-8 shadow-2xl shadow-black/10 dark:shadow-black/20">
            <div className="space-y-6">
              <div className="space-y-4">
                <h1 className="text-4xl tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                  Welkom bij Nousu
                </h1>
                <p className="text-lg text-muted-foreground/80">
                  Laten we je organisatie instellen
                </p>
              </div>

              <form onSubmit={handleCreateOrg} className="space-y-6">
                <div className="space-y-3 text-left">
                  <Label htmlFor="orgName" className="text-sm font-medium">
                    Organisatie naam
                  </Label>
                  <div className="relative">
                    <Building className="absolute left-4 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground/60" />
                    <Input
                      id="orgName"
                      type="text"
                      value={orgName}
                      onChange={(e) => setOrgName(e.target.value)}
                      className="pl-12 h-12 bg-white/5 border-white/20 backdrop-blur-sm rounded-xl focus:bg-white/10 transition-all duration-200"
                      placeholder="Voer organisatie naam in"
                      required
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-primary/90 hover:bg-primary text-primary-foreground rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-[1.02]"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      Aanmaken...
                    </div>
                  ) : (
                    "Organisatie aanmaken"
                  )}
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
