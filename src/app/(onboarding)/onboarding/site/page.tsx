"use client";

import type React from "react";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Store, Link } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "react-hot-toast";

export default function SiteSetupPage() {
  const [siteName, setSiteName] = useState("");
  const [siteUrl, setSiteUrl] = useState("");
  const [platform, setPlatform] = useState<"shopify" | "woocommerce" | "lightspeed">("shopify");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleCreateSite = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const response = await fetch("/api/sites", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: siteName,
          url: siteUrl,
          platform,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create site");
      }

      // If Shopify, redirect to OAuth
      if (platform === "shopify") {
        window.location.href = `/api/shopify/auth?siteId=${data.id}`;
        return;
      }

      toast.success("Website succesvol aangemaakt!");

      router.push(`/onboarding/agent?siteId=${data.id}`);
    } catch (error) {
      console.error("Site creation failed:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create site");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-[url('/images/hero-bg.webp')] bg-cover bg-center bg-no-repeat min-h-screen -mt-20 pt-20 relative transition-all duration-1000 ease-out">
      <div className="absolute bottom-0 left-0 right-0 h-60 bg-gradient-to-t from-white to-transparent"></div>
      <div className="min-h-screen flex items-center justify-center p-6 bg-transparent overflow-hidden">
        <div className="relative w-full max-w-md space-y-8 text-center">
          {/* Glassmorphism container */}
          <div className="backdrop-blur-xl bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 rounded-3xl p-8 shadow-2xl shadow-black/10 dark:shadow-black/20">
            <div className="space-y-6">
              <div className="space-y-4">
                <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                  Verbind je website
                </h1>
                <p className="text-lg text-muted-foreground/80">Laten we je website verbinden</p>
              </div>

              <form onSubmit={handleCreateSite} className="space-y-6">
                <div className="space-y-3 text-left">
                  <Label htmlFor="siteName" className="text-sm font-medium">
                    Website naam
                  </Label>
                  <div className="relative">
                    <Store className="absolute left-4 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground/60" />
                    <Input
                      id="siteName"
                      type="text"
                      value={siteName}
                      onChange={(e) => setSiteName(e.target.value)}
                      className="pl-12 h-12 bg-white/5 border-white/20 backdrop-blur-sm rounded-xl focus:bg-white/10 transition-all duration-200"
                      placeholder="Voer website naam in"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-3 text-left">
                  <Label htmlFor="siteUrl" className="text-sm font-medium">
                    Website URL
                  </Label>
                  <div className="relative">
                    <Link className="absolute left-4 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground/60" />
                    <Input
                      id="siteUrl"
                      type="url"
                      value={siteUrl}
                      onChange={(e) => setSiteUrl(e.target.value)}
                      className="pl-12 h-12 bg-white/5 border-white/20 backdrop-blur-sm rounded-xl focus:bg-white/10 transition-all duration-200"
                      placeholder="https://jouwwebsite.com"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-3 text-left">
                  <Label htmlFor="platform" className="text-sm font-medium">
                    Platform
                  </Label>
                  <Select
                    value={platform}
                    onValueChange={(value) => setPlatform(value as typeof platform)}
                  >
                    <SelectTrigger className="h-12 bg-white/5 border-white/20 backdrop-blur-sm rounded-xl focus:bg-white/10 transition-all duration-200">
                      <SelectValue placeholder="Selecteer platform" />
                    </SelectTrigger>
                    <SelectContent className="backdrop-blur-xl bg-white/90 dark:bg-gray-900/90 border-white/20 rounded-xl">
                      <SelectItem value="shopify" className="rounded-lg">
                        Shopify
                      </SelectItem>
                      <SelectItem value="woocommerce" disabled className="rounded-lg opacity-50">
                        WooCommerce (Coming soon)
                      </SelectItem>
                      <SelectItem value="lightspeed" disabled className="rounded-lg opacity-50">
                        Lightspeed (Coming soon)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-primary/90 hover:bg-primary text-primary-foreground rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-[1.02]"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      Verbinden...
                    </div>
                  ) : (
                    "Website verbinden"
                  )}
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
