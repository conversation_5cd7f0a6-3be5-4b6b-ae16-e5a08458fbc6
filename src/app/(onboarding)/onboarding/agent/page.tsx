"use client";

import type React from "react";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import {
  PersonStandingIcon as Person,
  HelpCircle,
  Eye,
  Building,
  Sparkles,
  Zap,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface FormData {
  // Step 1 - Basic Info
  name: string;
  language: "nl" | "en";
  mode: "cs_default" | "faq_only" | "vision_return";

  // Step 2 - Personality & Communication
  welcomeText: string;
  tone: "informal" | "neutral" | "formal";
  personalityPrompt: string;

  // Step 3 - AI Options
  reflectionEnabled: boolean;
  primaryModel: string;
  temperature: number;
  visionEnabled: boolean;
  escalationEnabled: boolean;
  maxTurnsBeforeEscalation: number;
}

const INITIAL_FORM_DATA: FormData = {
  name: "",
  language: "nl",
  mode: "cs_default",
  welcomeText: "",
  tone: "neutral",
  personalityPrompt: "",
  reflectionEnabled: true,
  primaryModel: "gpt-4o",
  temperature: 30, // Store as 0-100, convert to 0.0-1.0 later
  visionEnabled: false,
  escalationEnabled: true,
  maxTurnsBeforeEscalation: 5,
};

const USE_CASES = [
  {
    id: "cs_default",
    title: "Allround Klantenservice",
    description: "De volledige Nousu ervaring.",
    icon: Person,
    recommended: true,
    tools: ["lookupOrder", "createReturn", "checkInventory", "escalateToHuman", "assessDamage"],
  },
  {
    id: "faq_only",
    title: "Alleen veelgestelde vragen",
    description: "Beperkt tot het beantwoorden van vooraf gedefinieerde FAQ's",
    icon: HelpCircle,
    recommended: false,
    tools: ["escalateToHuman"],
  },
  {
    id: "vision_return",
    title: "Retourverwerking met beeldherkenning",
    description: "Alleen aanbevolen als je Nousu gebruikt in combinatie met andere tools",
    icon: Eye,
    recommended: false,
    tools: ["lookupOrder", "createReturn", "checkInventory", "escalateToHuman", "assessDamage"],
  },
];

const TONE_OPTIONS = [
  {
    id: "informal",
    title: "Informeel & vriendelijk",
    description: "Gebruikt 'je' en spreekt klanten casual aan",
    example: "Hey! Hoe kan ik je helpen?",
    prompt:
      "Je bent een vriendelijke en informele klantenservice medewerker. Spreek klanten aan met 'je' en gebruik een persoonlijke, toegankelijke toon. Je mag emoji's gebruiken waar gepast.",
  },
  {
    id: "neutral",
    title: "Professioneel & behulpzaam",
    description: "Evenwicht tussen vriendelijk en professioneel",
    example: "Goedemiddag! Waarmee kan ik u van dienst zijn?",
    prompt:
      "Je bent een professionele en behulpzame klantenservice medewerker. Spreek klanten respectvol aan en hanteer een vriendelijke maar zakelijke toon.",
  },
  {
    id: "formal",
    title: "Formeel & beleefd",
    description: "Gebruikt 'u' en houdt afstand",
    example: "Goedendag, waarmee kan ik u van dienst zijn?",
    prompt:
      "Je bent een formele en beleefde klantenservice medewerker. Spreek klanten altijd aan met 'u' en hanteer een respectvolle, professionele toon zonder persoonlijke elementen.",
  },
];

const MODEL_OPTIONS = [
  {
    id: "gpt-4o",
    title: "gpt-4o",
    description: "Nieuwste model, snel en nauwkeurig",
    icon: Sparkles,
    recommended: true,
  },
  {
    id: "gpt-5-mini",
    title: "GPT-5 Mini",
    description: "Sneller en goedkoper, iets minder geavanceerd",
    icon: Zap,
    recommended: false,
  },
  {
    id: "gpt-4-turbo",
    title: "GPT-4 Turbo",
    description: "Bewezen stabiel model / DEPRECATED",
    icon: Building,
    recommended: false,
  },
];

function AgentSetupContent() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>(INITIAL_FORM_DATA);
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();
  const siteId = searchParams.get("siteId");

  const handleInputChange = (field: keyof FormData, value: unknown) => {
    setFormData((prev) => {
      const newData = { ...prev, [field]: value };

      if (field === "tone") {
        const toneOption = TONE_OPTIONS.find((t) => t.id === value);
        if (toneOption) {
          newData.personalityPrompt = toneOption.prompt;
        }
      }

      // If vision is enabled, set mode to vision_return
      if (field === "visionEnabled" && value === true) {
        newData.mode = "vision_return";
      }

      return newData;
    });
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setCurrentStep((prev) => prev - 1);
  };

  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 1:
        if (!formData.name || !formData.language || !formData.mode) {
          toast.error("Vul alle verplichte velden in");
          return false;
        }
        return true;
      case 2:
        if (!formData.welcomeText || !formData.tone) {
          toast.error("Vul alle verplichte velden in");
          return false;
        }
        return true;
      default:
        return true;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log("siteId", siteId);

    if (!siteId) {
      toast.error("Site ID is missing. Please start the onboarding process again.");
      return;
    }

    setIsLoading(true);

    try {
      // Get the selected use case to determine enabled tools
      const selectedUseCase = USE_CASES.find((uc) => uc.id === formData.mode);
      const enabledTools = selectedUseCase?.tools || ["escalateToHuman"];

      // Create the shop policy configuration
      const shopConfigResponse = await fetch("/api/shop-config", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          siteId: siteId,
          config: {
            personalityPrompt: formData.personalityPrompt,
            enabledTools,
            toolPolicies: {
              createReturn: enabledTools.includes("createReturn")
                ? [
                    "Vraag altijd om een reden bij het aanmaken van een retour",
                    "Bij beschadiging, vraag om foto's",
                  ]
                : [],
            },
            maxTurnsBeforeEscalation: formData.maxTurnsBeforeEscalation,
            escalationEnabled: formData.escalationEnabled,
            escalationEmail: "",
            primaryModel: formData.primaryModel,
            fallbackModel: "gpt-5-mini",
            temperature: formData.temperature, // Already stored as 0-100
            reflectionEnabled: formData.reflectionEnabled,
            reflectionThreshold: 40,
            businessHours: {
              timezone: "Europe/Amsterdam",
              schedule: {
                monday: { start: "09:00", end: "17:00", enabled: true },
                tuesday: { start: "09:00", end: "17:00", enabled: true },
                wednesday: { start: "09:00", end: "17:00", enabled: true },
                thursday: { start: "09:00", end: "17:00", enabled: true },
                friday: { start: "09:00", end: "17:00", enabled: true },
                saturday: { start: "10:00", end: "16:00", enabled: false },
                sunday: { start: "10:00", end: "16:00", enabled: false },
              },
            },
          },
        }),
      });

      if (!shopConfigResponse.ok) {
        throw new Error("Failed to create shop configuration");
      }

      // Also create the legacy agent record for compatibility
      const agentResponse = await fetch("/api/agents", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name,
          mode: formData.mode,
          currentModel: formData.primaryModel,
          reflectionEnabled: formData.reflectionEnabled,
          prompts: [
            {
              slot: "system",
              content: formData.personalityPrompt,
              temperature: Math.round(formData.temperature), // Convert to 0-100 integer
              topP: 100, // Default to 100 (1.0)
              active: true,
            },
            {
              slot: "welcome",
              content: formData.welcomeText,
              active: true,
            },
          ],
        }),
      });

      if (!agentResponse.ok) {
        throw new Error("Failed to create agent");
      }

      const agentData = await agentResponse.json();

      toast.success(`AI-agent "${agentData.name}" succesvol aangemaakt!`);

      // Show installation snippet
      setCurrentStep(4);
    } catch (error) {
      console.error("Failed to create agent:", error);
      toast.error("Er ging iets mis bij het aanmaken van de agent");
    } finally {
      setIsLoading(false);
    }
  };

  const getStepTitle = () => {
    if (currentStep === 4) return "Je AI-agent is klaar!";
    return `Stap ${currentStep} van 3: ${
      currentStep === 1
        ? "Basis instellingen"
        : currentStep === 2
          ? "Persoonlijkheid & Communicatie"
          : "Geavanceerde opties"
    }`;
  };

  const getStepDescription = () => {
    if (currentStep === 4) return "";
    return currentStep === 1
      ? "Kies de naam, taal en het hoofddoel van je AI-agent"
      : currentStep === 2
        ? "Bepaal hoe je AI-agent communiceert met klanten"
        : "Stel geavanceerde AI-instellingen in (optioneel)";
  };

  return (
    <div className="bg-[url('/images/hero-bg.webp')] bg-cover bg-center bg-no-repeat min-h-screen -mt-20 pt-20 relative transition-all duration-1000 ease-out">
      <div className="absolute bottom-0 left-0 right-0 h-60 bg-gradient-to-t from-white to-transparent"></div>
      <div className="min-h-screen flex items-center justify-center p-6 bg-transparent overflow-hidden">
        <div className="relative w-full max-w-4xl space-y-8">
          {/* Glassmorphism container */}
          <div className="backdrop-blur-xl bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 rounded-3xl p-8 shadow-2xl shadow-black/10 dark:shadow-black/20">
            <div className="space-y-8">
              {/* Header */}
              <div className="text-center space-y-4">
                <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                  {getStepTitle()}
                </h1>
                {currentStep < 4 && (
                  <p className="text-lg text-muted-foreground/80">{getStepDescription()}</p>
                )}
              </div>

              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Step 1 - Basic Info */}
                {currentStep === 1 && (
                  <div className="space-y-8">
                    <div className="space-y-6">
                      <h3 className="text-xl font-semibold">Basis informatie</h3>

                      <div className="space-y-3">
                        <Label htmlFor="name" className="text-sm font-medium">
                          Naam van je AI-agent *
                        </Label>
                        <Input
                          id="name"
                          type="text"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                          className="h-12 bg-white/5 border-white/20 backdrop-blur-sm rounded-xl focus:bg-white/10 transition-all duration-200"
                          placeholder="Bijv. KlantenService Bot"
                          required
                        />
                      </div>

                      <div className="space-y-3">
                        <Label className="text-sm font-medium">Taal *</Label>
                        <div className="grid grid-cols-2 gap-4">
                          {[
                            { id: "nl", label: "🇳🇱 Nederlands" },
                            { id: "en", label: "🇬🇧 Engels" },
                          ].map((lang) => (
                            <button
                              key={lang.id}
                              type="button"
                              onClick={() => handleInputChange("language", lang.id)}
                              className={`p-4 rounded-xl border-2 transition-all duration-200 backdrop-blur-sm ${
                                formData.language === lang.id
                                  ? "border-primary bg-primary/10 text-primary"
                                  : "border-white/20 bg-white/5 hover:bg-white/10"
                              }`}
                            >
                              <span className="font-medium">{lang.label}</span>
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-6">
                      <h3 className="text-xl font-semibold">
                        Waarvoor wil je de AI-agent gebruiken? *
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {USE_CASES.map((useCase) => {
                          const IconComponent = useCase.icon;
                          return (
                            <button
                              key={useCase.id}
                              type="button"
                              onClick={() => handleInputChange("mode", useCase.id)}
                              className={`p-6 rounded-xl border-2 transition-all duration-200 hover:cursor-pointer backdrop-blur-sm text-left ${
                                formData.mode === useCase.id
                                  ? "border-primary bg-primary/10"
                                  : "border-white/20 bg-white/5 hover:bg-white/10"
                              }`}
                            >
                              <div className="flex items-start gap-4">
                                <IconComponent className="h-6 w-6 mt-1 flex-shrink-0" />
                                <div className="space-y-2">
                                  {useCase.recommended && (
                                    <Badge variant="secondary" className="text-xs">
                                      AANBEVOLEN
                                    </Badge>
                                  )}
                                  <div className="flex items-center gap-2">
                                    <h4 className="font-semibold">{useCase.title}</h4>
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    {useCase.description}
                                  </p>
                                </div>
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2 - Personality & Communication */}
                {currentStep === 2 && (
                  <div className="space-y-8">
                    <div className="space-y-6">
                      <h3 className="text-xl font-semibold">Eerste indruk</h3>

                      <div className="space-y-3">
                        <Label htmlFor="welcomeText" className="text-sm font-medium">
                          Welkomsttekst *
                        </Label>
                        <Textarea
                          id="welcomeText"
                          value={formData.welcomeText}
                          onChange={(e) => handleInputChange("welcomeText", e.target.value)}
                          className="min-h-[100px] bg-white/5 border-white/20 backdrop-blur-sm rounded-xl focus:bg-white/10 transition-all duration-200"
                          placeholder="Hallo! Ik ben je virtuele assistent. Hoe kan ik je vandaag helpen?"
                          maxLength={320}
                          required
                        />
                        <p className="text-xs text-muted-foreground flex items-center gap-2">
                          <HelpCircle className="h-3 w-3" />
                          Dit is het eerste bericht dat klanten zien wanneer ze de chat openen
                        </p>
                      </div>
                    </div>

                    <div className="space-y-6">
                      <h3 className="text-xl font-semibold">
                        Hoe wil je dat je AI-agent communiceert? *
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {TONE_OPTIONS.map((tone) => (
                          <button
                            key={tone.id}
                            type="button"
                            onClick={() => handleInputChange("tone", tone.id)}
                            className={`p-6 rounded-xl border-2 transition-all duration-200 backdrop-blur-sm text-left ${
                              formData.tone === tone.id
                                ? "border-primary bg-primary/10"
                                : "border-white/20 bg-white/5 hover:bg-white/10"
                            }`}
                          >
                            <div className="space-y-3">
                              <h4 className="font-semibold">{tone.title}</h4>
                              <p className="text-sm text-muted-foreground">{tone.description}</p>
                              <p className="text-xs italic text-muted-foreground/80">
                                Voorbeeld: &quot;{tone.example}&quot;
                              </p>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="personalityPrompt" className="text-sm font-medium">
                        Aangepaste persoonlijkheid (optioneel)
                      </Label>
                      <Textarea
                        id="personalityPrompt"
                        value={formData.personalityPrompt}
                        onChange={(e) => handleInputChange("personalityPrompt", e.target.value)}
                        className="min-h-[100px] bg-white/5 border-white/20 backdrop-blur-sm rounded-xl focus:bg-white/10 transition-all duration-200"
                        placeholder="Automatisch ingevuld op basis van je toon-keuze..."
                      />
                      <p className="text-xs text-muted-foreground flex items-center gap-2">
                        <HelpCircle className="h-3 w-3" />
                        Je kunt dit later aanpassen in de instellingen
                      </p>
                    </div>
                  </div>
                )}

                {/* Step 3 - AI Options */}
                {currentStep === 3 && (
                  <div className="space-y-8">
                    <div className="space-y-6">
                      <h3 className="text-xl font-semibold">AI Model & Instellingen</h3>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {MODEL_OPTIONS.map((model) => {
                          const IconComponent = model.icon;
                          return (
                            <button
                              key={model.id}
                              type="button"
                              onClick={() => handleInputChange("primaryModel", model.id)}
                              className={`p-6 rounded-xl border-2 transition-all duration-200 backdrop-blur-sm text-left ${
                                formData.primaryModel === model.id
                                  ? "border-primary bg-primary/10"
                                  : "border-white/20 bg-white/5 hover:bg-white/10"
                              }`}
                            >
                              <div className="flex items-start gap-4">
                                <IconComponent className="h-6 w-6 mt-1 flex-shrink-0" />
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2">
                                    <h4 className="font-semibold">{model.title}</h4>
                                    {model.recommended && (
                                      <Badge variant="secondary" className="text-xs">
                                        AANBEVOLEN
                                      </Badge>
                                    )}
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    {model.description}
                                  </p>
                                </div>
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </div>

                    <div className="space-y-6">
                      <div className="flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/20 backdrop-blur-sm">
                        <div className="space-y-1">
                          <Label className="text-sm font-medium">Slimme antwoord-verificatie</Label>
                          <p className="text-xs text-muted-foreground">
                            Laat de AI dubbelchecken of antwoorden correct en behulpzaam zijn
                            voordat ze verstuurd worden
                          </p>
                        </div>
                        <Switch
                          checked={formData.reflectionEnabled}
                          onCheckedChange={(checked) =>
                            handleInputChange("reflectionEnabled", checked)
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/20 backdrop-blur-sm">
                        <div className="space-y-1">
                          <Label className="text-sm font-medium">
                            Beeldherkenning voor retourverwerking
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            AI-agent kan afbeeldingen van beschadigde producten analyseren en
                            retouren verwerken
                          </p>
                        </div>
                        <Switch
                          checked={formData.visionEnabled}
                          onCheckedChange={(checked) => handleInputChange("visionEnabled", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/20 backdrop-blur-sm">
                        <div className="space-y-1">
                          <Label className="text-sm font-medium">
                            Doorverwijzing naar menselijke agent
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Na {formData.maxTurnsBeforeEscalation} berichten wordt doorverwezen naar
                            een menselijke medewerker
                          </p>
                        </div>
                        <Switch
                          checked={formData.escalationEnabled}
                          onCheckedChange={(checked) =>
                            handleInputChange("escalationEnabled", checked)
                          }
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label>Creativiteit (Temperature: {formData.temperature / 100})</Label>
                      <div className="px-4">
                        <input
                          type="range"
                          min={0}
                          max={100}
                          value={formData.temperature}
                          onChange={(e) =>
                            handleInputChange("temperature", parseInt(e.target.value))
                          }
                          className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
                        />
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Lager = meer voorspelbaar en consistent, hoger = meer creatief en gevarieerd
                      </p>
                    </div>
                  </div>
                )}

                {/* Step 4 - Success */}
                {currentStep === 4 && (
                  <div className="space-y-8 text-center">
                    <div className="space-y-4">
                      <h3 className="text-2xl font-bold">Je AI-agent is succesvol aangemaakt!</h3>
                      <p className="text-muted-foreground">
                        Je kunt nu verdere instellingen aanpassen in het dashboard.
                      </p>
                    </div>

                    <div className="p-6 rounded-xl bg-white/40 border border-white/10 backdrop-blur-sm">
                      <h4 className="font-semibold mb-2">Wat kun je nu doen?</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Pas brand guidelines aan in Instellingen</li>
                        <li>• Test je AI-agent in de chat interface</li>
                        <li>• Nodig teamleden uit om samen te werken</li>
                      </ul>
                    </div>
                  </div>
                )}

                {/* Navigation */}
                <div className="flex justify-between items-center pt-6">
                  {currentStep > 1 && currentStep < 4 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleBack}
                      className="bg-white/5 border-white/20 backdrop-blur-sm hover:bg-white/10"
                    >
                      ← Vorige stap
                    </Button>
                  )}

                  {currentStep < 3 && (
                    <Button
                      type="button"
                      onClick={handleNext}
                      className={`bg-primary/90 hover:bg-primary text-primary-foreground rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-[1.02] ${
                        currentStep === 1 ? "ml-auto" : ""
                      }`}
                    >
                      Volgende stap →
                    </Button>
                  )}

                  {currentStep === 3 && (
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="ml-auto bg-primary/90 hover:bg-primary text-primary-foreground rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-[1.02]"
                    >
                      {isLoading ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                          AI-agent wordt aangemaakt...
                        </div>
                      ) : (
                        "AI-agent aanmaken"
                      )}
                    </Button>
                  )}

                  {currentStep === 4 && (
                    <Button
                      type="button"
                      onClick={() => {
                        window.location.href = siteId ? `/dashboard/${siteId}/home` : "/dashboard";
                      }}
                      className="mx-auto bg-primary/90 hover:bg-primary text-primary-foreground rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-[1.02]"
                    >
                      Naar dashboard
                    </Button>
                  )}
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AgentSetupPage() {
  return (
    <Suspense fallback={<div>Laden...</div>}>
      <AgentSetupContent />
    </Suspense>
  );
}
