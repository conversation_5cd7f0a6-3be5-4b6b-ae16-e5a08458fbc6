import type { <PERSON>ada<PERSON> } from "next";
import { Toaster } from "react-hot-toast";

export const metadata: Metadata = {
  title: "Nousu",
  description: "Nousu",
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-background">
      <div>
        <Toaster />
      </div>
      {children}
    </div>
  );
}
