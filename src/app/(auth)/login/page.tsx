import { GalleryVerticalEnd } from "lucide-react";
import Link from "next/link";

import { LoginForm } from "@/components/login-form";

export default function LoginPage() {
  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
      <div className="flex w-full max-w-sm flex-col gap-6">
        <Link href="/" className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 mx-auto">
            <img
              src="images/nousu-logo.png"
              alt="Nousu Logo"
              className="w-auto h-6 object-cover"
              width={32}
              height={32}
            />
          </div>
        </Link>
        <LoginForm />
      </div>
    </div>
  );
}
