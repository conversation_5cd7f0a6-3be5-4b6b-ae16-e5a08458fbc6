import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { sites } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { queueJob } from "@/lib/workers/job-queue";
import crypto from "crypto";

/**
 * Verify Shopify webhook HMAC signature
 * For apps created after 2024, Shopify uses the app's API secret key
 */
function verifyWebhookSignature(rawBody: string, signature: string): boolean {
  // Use the app's client secret for HMAC verification
  const secret = process.env.SHOPIFY_CLIENT_SECRET;

  if (!secret) {
    console.error("SHOPIFY_CLIENT_SECRET not configured");
    return false;
  }

  const hash = crypto.createHmac("sha256", secret).update(rawBody, "utf8").digest("base64");

  return hash === signature;
}

/**
 * Unified Shopify webhook handler for multi-tenant platform
 * Handles all webhook topics from multiple shops
 *
 * IMPORTANT: This endpoint responds quickly and queues processing
 * to avoid Shopify webhook timeouts
 */
export async function POST(req: NextRequest) {
  const startTime = Date.now();

  try {
    const rawBody = await req.text();
    const signature = req.headers.get("x-shopify-hmac-sha256");
    const topic = req.headers.get("x-shopify-topic");
    const apiVersion = req.headers.get("x-shopify-api-version");
    const shopDomain = req.headers.get("x-shopify-shop-domain");

    if (!signature || !topic || !shopDomain) {
      return NextResponse.json({ error: "Missing required headers" }, { status: 400 });
    }

    // Verify webhook signature
    if (!verifyWebhookSignature(rawBody, signature)) {
      console.error(`Invalid webhook signature for shop ${shopDomain}`);
      return NextResponse.json({ error: "Invalid webhook signature" }, { status: 401 });
    }

    // Multi-tenant routing: Check query param first, then header
    const { searchParams } = new URL(req.url);
    const siteIdFromQuery = searchParams.get("site_id");

    let site;
    if (siteIdFromQuery) {
      // Direct site ID from webhook registration
      site = await db.query.sites.findFirst({
        where: eq(sites.id, siteIdFromQuery),
      });
    } else {
      // Fallback to shop domain lookup
      site = await db.query.sites.findFirst({
        where: and(eq(sites.platform, "shopify"), eq(sites.platformShopId, shopDomain)),
      });
    }

    if (!site) {
      console.error(`No site found for shop domain: ${shopDomain}`);
      return NextResponse.json({ error: "Site not found" }, { status: 404 });
    }

    const payload = JSON.parse(rawBody);

    console.log(`📦 Webhook received: ${topic} for site ${site.id}`);

    // Extract all headers for the async processor
    const headers: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      headers[key] = value;
    });

    // Queue the webhook for async processing
    await queueJob({
      type: "process_shopify_webhook",
      payload: {
        rawBody,
        headers,
        siteId: site.id,
        sessionId: `webhook_${Date.now()}`, // Required field
        agentId: "", // Not needed for webhooks
        jobType: "process_shopify_webhook",
      },
      // Add small delay to prevent thundering herd
      delay: Math.floor(Math.random() * 3), // 0-3 second random delay
    });

    // Log response time
    const responseTime = Date.now() - startTime;
    console.log(`✅ Webhook queued in ${responseTime}ms for ${topic}`);

    // Respond immediately to Shopify
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Webhook processing error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
