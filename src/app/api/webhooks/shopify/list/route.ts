import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { sites } from "@/db/schema";
import { eq } from "drizzle-orm";
import { ShopifyWebhookManager } from "@/lib/shopify/webhook-manager";

/**
 * Debug endpoint to list registered webhooks for a site
 * Usage: GET /api/webhooks/shopify/list?siteId=xxx
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const siteId = searchParams.get("siteId");

    if (!siteId) {
      return NextResponse.json({ error: "siteId parameter required" }, { status: 400 });
    }

    // Get site
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, siteId),
    });

    if (!site || !site.platformToken || !site.platformShopId) {
      return NextResponse.json(
        { error: "Site not found or missing Shopify credentials" },
        { status: 404 },
      );
    }

    // Create webhook manager
    const manager = new ShopifyWebhookManager(site.platformToken, site.platformShopId);

    // List all webhooks
    const webhooks = await manager.listWebhooks();

    return NextResponse.json({
      shop: site.platformShopId,
      totalWebhooks: webhooks.length,
      webhooks: webhooks.map((w) => ({
        id: w.id,
        topic: w.topic,
        endpoint: w.endpoint,
        createdAt: w.createdAt,
      })),
    });
  } catch (error) {
    console.error("Failed to list webhooks:", error);
    return NextResponse.json({ error: "Failed to list webhooks" }, { status: 500 });
  }
}
