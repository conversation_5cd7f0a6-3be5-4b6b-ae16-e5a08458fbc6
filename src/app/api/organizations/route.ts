import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/db";
import { organizations, orgMembers } from "@/db/schema";
import { slugify, generateUUID } from "@/lib/utils";

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name } = await req.json();
    if (!name) {
      return NextResponse.json({ error: "Name is required" }, { status: 400 });
    }

    // Create organization
    const orgId = generateUUID();
    const [org] = await db
      .insert(organizations)
      .values({
        id: orgId,
        name,
        slug: slugify(name),
      })
      .returning();

    // Add user as owner
    await db.insert(orgMembers).values({
      orgId: org.id,
      userId: session.user.id,
      role: "owner",
    });

    return NextResponse.json(org);
  } catch (error) {
    console.error("Failed to create organization:", error);
    return NextResponse.json({ error: "Failed to create organization" }, { status: 500 });
  }
}
