import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { chatMessages, chatSessions } from "@/db/schema";
import { eq, and } from "drizzle-orm";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> },
) {
  try {
    const { sessionId } = await params;

    if (!sessionId) {
      return NextResponse.json({ error: "Session ID is required" }, { status: 400 });
    }

    // Check if session exists
    const session = await db.query.chatSessions.findFirst({
      where: eq(chatSessions.id, sessionId),
    });

    if (!session) {
      return NextResponse.json({ error: "Session not found" }, { status: 404 });
    }

    // Get all messages for this session
    const messages = await db.query.chatMessages.findMany({
      where: eq(chatMessages.sessionId, sessionId),
      orderBy: (chatMessages, { asc }) => [asc(chatMessages.createdAt)],
    });

    return NextResponse.json({
      sessionId,
      agentId: session.agentId,
      messages: messages.map((msg) => ({
        id: msg.id,
        role: msg.role,
        content: msg.content?.split("__STATE_DATA__")[0] || msg.content, // Remove state data because it's not needed for the UI
        timestamp: msg.createdAt,
      })),
    });
  } catch (error) {
    console.error("Error fetching chat history:", error);
    return NextResponse.json({ error: "Failed to fetch chat history" }, { status: 500 });
  }
}
