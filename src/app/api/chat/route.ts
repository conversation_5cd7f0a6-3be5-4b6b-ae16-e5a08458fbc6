import { NextRequest, NextResponse } from "next/server";
import { processMessage } from "@/lib/graphs/index";
import { db } from "@/db";
import { chatMessages, chatSessions, agents, agentPrompts } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { streamText, generateText } from "ai";
import { openai } from "@/lib/clients";
import { ChatOpenAI } from "@langchain/openai";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { AgentStateManager } from "@/lib/upstash";

// Define the expected request body structure
interface ChatRequestBody {
  message: string;
  agentId: string;
  sessionId?: string;
  externalUserId?: string;
}

export async function POST(req: NextRequest) {
  const startTime = Date.now();

  try {
    // Parse the request body
    const body = (await req.json()) as ChatRequestBody;
    const { message, agentId, sessionId: existingSessionId, externalUserId } = body;

    if (!message || !agentId) {
      return NextResponse.json(
        { error: "Missing required fields: message and agentId" },
        { status: 400 },
      );
    }

    // Get agent details from the database
    const agentDetails = await db.query.agents.findFirst({
      where: eq(agents.id, agentId),
    });

    if (!agentDetails) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Get agent prompts from the database
    const systemPrompt = await db.query.agentPrompts.findFirst({
      where: and(
        eq(agentPrompts.agentId, agentId),
        eq(agentPrompts.slot, "system"),
        eq(agentPrompts.active, true),
      ),
    });

    // Create or retrieve a chat session
    let sessionId = existingSessionId;
    if (!sessionId) {
      // Create a new session
      const newSessionId = uuidv4();
      await db.insert(chatSessions).values({
        id: newSessionId,
        agentId,
        externalUserId: externalUserId || null,
        state: "open",
      });
      sessionId = newSessionId;
    } else {
      // Verify that the existing session exists in the database
      const existingSession = await db.query.chatSessions.findFirst({
        where: eq(chatSessions.id, existingSessionId!),
      });

      if (!existingSession) {
        // Session doesn't exist, create a new one with the provided ID
        await db.insert(chatSessions).values({
          id: existingSessionId!,
          agentId,
          externalUserId: externalUserId || null,
          state: "open",
        });
      }
    }

    // Generate a trace ID for LangSmith tracking
    const traceId = `trace_${uuidv4()}`;

    // Store user message and add to state manager in parallel
    const userMessageId = uuidv4();
    await Promise.all([
      db.insert(chatMessages).values({
        id: userMessageId,
        sessionId,
        role: "user",
        content: message,
        traceId,
      }),
      AgentStateManager.addMessage(sessionId, "user", message),
    ]);

    // Determine if we should use LangGraph or direct OpenAI based on agent mode
    if (agentDetails.mode === "cs_default" || agentDetails.mode === "vision_return") {
      // Use LangGraph for complex agent behavior (handled internally by processMessage)

      // Previous message parsing removed in new architecture - conversation history
      // is now handled internally by the AgentStateManager

      // Process through LangGraph using the processMessage function
      // Note: previousState is now ignored in the new architecture
      const { reply, state } = await processMessage(message, agentId, sessionId, "chat");

      // Store the assistant's response in the database
      // Include the state data in a hidden format for future messages
      const responseWithState = reply + (state ? `\n\n__STATE_DATA__${JSON.stringify(state)}` : "");

      await db.insert(chatMessages).values({
        id: uuidv4(),
        sessionId,
        role: "assistant",
        content: responseWithState,
        traceId,
      });

      // Return the response with sessionId for the UI
      return NextResponse.json({
        response: reply,
        sessionId: sessionId,
      });
    } else {
      // Use AI SDK for simpler FAQ-only mode
      // Get previous messages for context (limited to last 5 for simplicity)
      const previousMessages = await db.query.chatMessages.findMany({
        where: eq(chatMessages.sessionId, sessionId),
        orderBy: (chatMessages, { asc }) => [asc(chatMessages.createdAt)],
        limit: 5,
      });

      // Format previous messages for the context
      const messageHistory = previousMessages
        .filter(
          (msg) =>
            msg.role && (msg.role === "user" || msg.role === "assistant" || msg.role === "system"),
        )
        .map((msg) => ({
          role: msg.role as "user" | "assistant" | "system",
          content: msg.content,
        }));

      // Create the system message
      const systemMessage = {
        role: "system" as const,
        content:
          systemPrompt?.content ||
          "You are a helpful customer service agent for an e-commerce store.",
      };

      const messages = [
        systemMessage,
        ...messageHistory,
        {
          role: "user" as const,
          content: message,
        },
      ];

      const modelName = agentDetails.currentModel || "gpt-4o";
      const temperature = systemPrompt?.temperature ? systemPrompt.temperature / 100 : 0.2;

      // Check if this is a request from our UI (it will have a specific header)
      const isFromUI = req.headers.get("x-from-chat-ui") === "true";

      if (isFromUI) {
        // For UI requests, get the full response and return as JSON
        const result = await generateText({
          model: openai(modelName),
          messages,
          temperature,
        });

        const responseContent = result.text || "Sorry, I couldn't generate a response.";

        // Store the assistant's response in the database
        await db.insert(chatMessages).values({
          id: uuidv4(),
          sessionId,
          role: "assistant",
          content: responseContent,
          traceId,
        });

        return NextResponse.json({
          response: responseContent,
          sessionId: sessionId,
        });
      } else {
        // For API consumers, use the streamText function from AI SDK
        const result = await streamText({
          model: openai(modelName),
          messages,
          temperature,
          onFinish: async (result) => {
            // Store the assistant's response in the database
            await db.insert(chatMessages).values({
              id: uuidv4(),
              sessionId,
              role: "assistant",
              content: result.text,
              traceId,
            });
          },
        });

        return result.toDataStreamResponse();
      }
    }
  } catch (error) {
    console.error("Error in chat API:", error);
    const totalTime = Date.now() - startTime;
    console.log(`Error occurred after ${totalTime}ms`);

    return NextResponse.json(
      {
        error: "An error occurred while processing your request",
        ...(process.env.NODE_ENV === "development" && {
          details: (error as Error).message,
          totalTime,
        }),
      },
      { status: 500 },
    );
  }
}
