import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { agentConfigurations } from "@/db/schema-updates/agent-config";
import { eq, and } from "drizzle-orm";
import { auth } from "@/auth";

/**
 * Get all configurations for a site or agent
 * GET /api/agent-config?siteId=xxx&agentId=yyy&configType=returns
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const siteId = searchParams.get("siteId");
    const agentId = searchParams.get("agentId");
    const configType = searchParams.get("configType");

    if (!siteId && !agentId) {
      return NextResponse.json({ error: "Either siteId or agentId is required" }, { status: 400 });
    }

    // Build query conditions
    let conditions = [];
    if (siteId) conditions.push(eq(agentConfigurations.siteId, siteId));
    if (agentId) conditions.push(eq(agentConfigurations.agentId, agentId));
    if (configType) conditions.push(eq(agentConfigurations.configType, configType as any));

    // Get configurations
    const configs = await db.query.agentConfigurations.findMany({
      where: and(...conditions),
    });

    return NextResponse.json({ configurations: configs });
  } catch (error) {
    console.error("Error fetching agent configurations:", error);
    return NextResponse.json({ error: "Failed to fetch agent configurations" }, { status: 500 });
  }
}

/**
 * Create or update a configuration
 * POST /api/agent-config
 *
 * Body:
 * {
 *   siteId: string,
 *   agentId?: string,
 *   configType: "returns" | "orders" | "products" | "customer_service" | "global",
 *   enabled: boolean,
 *   settings: object
 * }
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const { siteId, agentId, configType, enabled, settings } = body;

    if (!siteId || !configType || !settings) {
      return NextResponse.json(
        { error: "Missing required fields: siteId, configType, settings" },
        { status: 400 },
      );
    }

    // Check if configuration already exists
    const conditions = [
      eq(agentConfigurations.siteId, siteId),
      eq(agentConfigurations.configType, configType),
    ];
    if (agentId) {
      conditions.push(eq(agentConfigurations.agentId, agentId));
    }

    const existingConfig = await db.query.agentConfigurations.findFirst({
      where: and(...conditions),
    });

    if (existingConfig) {
      // Update existing configuration
      await db
        .update(agentConfigurations)
        .set({
          enabled: enabled ?? existingConfig.enabled,
          settings: settings,
          updatedAt: new Date(),
        })
        .where(eq(agentConfigurations.id, existingConfig.id));

      return NextResponse.json({
        success: true,
        message: "Configuration updated",
        id: existingConfig.id,
      });
    } else {
      // Create new configuration
      const [result] = await db
        .insert(agentConfigurations)
        .values({
          siteId,
          agentId: agentId || null,
          configType,
          enabled: enabled ?? true,
          settings,
        })
        .returning({ id: agentConfigurations.id });

      return NextResponse.json({
        success: true,
        message: "Configuration created",
        id: result.id,
      });
    }
  } catch (error) {
    console.error("Error saving agent configuration:", error);
    return NextResponse.json({ error: "Failed to save agent configuration" }, { status: 500 });
  }
}

/**
 * Delete a configuration
 * DELETE /api/agent-config?id=xxx
 */
export async function DELETE(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json({ error: "Configuration ID is required" }, { status: 400 });
    }

    await db.delete(agentConfigurations).where(eq(agentConfigurations.id, id));

    return NextResponse.json({
      success: true,
      message: "Configuration deleted",
    });
  } catch (error) {
    console.error("Error deleting agent configuration:", error);
    return NextResponse.json({ error: "Failed to delete agent configuration" }, { status: 500 });
  }
}
