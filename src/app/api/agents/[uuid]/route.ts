import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { agents, agentPrompts } from "@/db/schema";
import { eq, and } from "drizzle-orm";

export async function GET(req: NextRequest, { params }: { params: Promise<{ uuid: string }> }) {
  try {
    const { uuid } = await params;

    // Get agent details
    const agentDetails = await db.query.agents.findFirst({
      where: eq(agents.id, uuid),
    });

    if (!agentDetails) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Get welcome message if available
    const welcomePrompt = await db.query.agentPrompts.findFirst({
      where: and(
        eq(agentPrompts.agentId, uuid),
        eq(agentPrompts.slot, "welcome"),
        eq(agentPrompts.active, true),
      ),
    });

    // Return agent details with welcome message
    return NextResponse.json({
      id: agentDetails.id,
      name: agentDetails.name,
      mode: agentDetails.mode,
      welcomeMessage: welcomePrompt?.content || "Hello! How can I help you today?",
    });
  } catch (error) {
    console.error("Error fetching agent details:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching agent details" },
      { status: 500 },
    );
  }
}
