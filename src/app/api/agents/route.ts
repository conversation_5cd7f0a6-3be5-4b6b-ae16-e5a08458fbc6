import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/db";
import { agents, agentPrompts, sites, orgMembers, organizations } from "@/db/schema";
import { eq, and } from "drizzle-orm";

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const { name, mode, currentModel, reflectionEnabled, prompts } = body;

    // Step 1: Get or create organization for the user
    let orgMember = await db
      .select()
      .from(orgMembers)
      .where(eq(orgMembers.userId, session.user.id))
      .limit(1);

    let orgId: string;
    if (orgMember.length === 0) {
      // Create a unique organization for this user
      const userEmail = session.user.email!;
      const orgName = session.user.name || userEmail.split("@")[0];
      const uniqueSlug = `${orgName.toLowerCase().replace(/[^a-z0-9]/g, "-")}-${Date.now()}`;

      const [newOrg] = await db
        .insert(organizations)
        .values({
          name: `${orgName}'s Webshop`,
          slug: uniqueSlug,
        })
        .returning();

      await db.insert(orgMembers).values({
        orgId: newOrg.id,
        userId: session.user.id,
        role: "owner",
      });

      orgId = newOrg.id;
    } else {
      orgId = orgMember[0].orgId!;
    }

    // Step 2: Get or create site for the organization
    let siteResults = await db.select().from(sites).where(eq(sites.orgId, orgId)).limit(1);

    let site;
    if (siteResults.length === 0) {
      const [newSite] = await db
        .insert(sites)
        .values({
          name: "Main Website",
          url: "https://example.com",
          orgId: orgId,
        })
        .returning();
      site = newSite;
    } else {
      site = siteResults[0];
    }

    // Step 3: Create the agent (no need for unique name check across different organizations!)
    const [agent] = await db
      .insert(agents)
      .values({
        name, // Use original name - each org can have their own "Ivy"
        mode,
        currentModel,
        reflectionEnabled,
        siteId: site.id,
      })
      .returning();

    // Step 4: Create the prompts
    if (prompts && prompts.length > 0) {
      await db.insert(agentPrompts).values(
        prompts.map((prompt: any) => ({
          agentId: agent.id,
          slot: prompt.slot,
          content: prompt.content,
          // Convert temperature from 0.0-1.0 to 0-100 (integer percentage)
          temperature: Math.round((prompt.temperature || 0) * 100),
          // Convert topP from 0.0-1.0 to 0-100 (integer percentage)
          topP: Math.round((prompt.topP || 1) * 100),
          active: prompt.active,
        })),
      );
    }

    return NextResponse.json(agent);
  } catch (error) {
    console.error("Failed to create agent:", error);
    return NextResponse.json({ error: "Failed to create agent" }, { status: 500 });
  }
}
