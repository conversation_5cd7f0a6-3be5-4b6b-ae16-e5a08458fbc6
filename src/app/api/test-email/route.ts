import { NextRequest, NextResponse } from "next/server";
import { sendEmail, scheduleEmail, generateEmailTemplate } from "@/lib/email";

/**
 * Test endpoint to verify email functionality
 * GET /api/test-email?to=<EMAIL>
 */
export async function GET(req: NextRequest) {
  try {
    console.log("🧪 Testing email template with fixes...");

    // Test the email template with the correct variables
    const template = generateEmailTemplate("return_received", {
      orderNumber: "1005",
      reason: "Het t-shirt is een maatje te groot", // Use the actual reason
      customerName: "Bram", // Use proper name instead of "daar"
      shopName: "Demo Webshop", // Use actual shop name instead of "NousuAI"
      items: "Rotterdam QHF - T-Shirt Wit",
      chatSummary: "Test chat summary",
    });

    return NextResponse.json({
      success: true,
      message: "Email template test",
      template: {
        subject: template.subject,
        preview: template.html.substring(0, 500) + "...",
      },
    });
  } catch (error) {
    console.error("❌ Test email error:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * Test endpoint to verify email template functionality
 * POST /api/test-email
 */
export async function POST(req: NextRequest) {
  try {
    const { to, templateType = "return_approved" } = await req.json();

    if (!to) {
      return NextResponse.json({ error: "Missing 'to' field in request body" }, { status: 400 });
    }

    // Test email scheduling with templates
    const emailId = await scheduleEmail(
      to,
      templateType,
      {
        orderNumber: "TEST-12345",
        customerName: "Test Klant",
        reason: "Test reden voor retour",
        staffNotes: "Dit is een test e-mail van het NousuAI systeem",
      },
      {
        sessionId: "test-session-123",
      },
    );

    return NextResponse.json({
      success: true,
      message: `Test template email (${templateType}) scheduled successfully`,
      emailId,
      to,
      templateType,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Test template email failed:", error);
    return NextResponse.json(
      {
        error: "Test template email failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
