import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { returnRequests, emailNotifications, agents, sites } from "@/db/schema";
import { eq, and, inArray } from "drizzle-orm";
import { auth } from "@/auth";
import { enqueueJob } from "@/lib/workers/job-queue";
import { scheduleEmail } from "@/lib/email";

/**
 * GET /api/returns - Get pending returns for staff review
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const status = searchParams.get("status") || "pending";
    const siteId = searchParams.get("siteId");

    if (!siteId) {
      return NextResponse.json({ error: "Site ID required" }, { status: 400 });
    }

    // Get pending returns for this site
    const returns = await db.query.returnRequests.findMany({
      where: and(eq(returnRequests.siteId, siteId), eq(returnRequests.status, status as any)),
      orderBy: [returnRequests.createdAt],
    });

    return NextResponse.json({ returns });
  } catch (error) {
    console.error("Error fetching returns:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 },
    );
  }
}

/**
 * POST /api/returns - Process staff decision on return request
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { returnRequestId, decision, staffNotes, additionalQuestions, denyReason } =
      await req.json();

    if (!returnRequestId || !decision) {
      return NextResponse.json(
        {
          error: "Return request ID and decision are required",
        },
        { status: 400 },
      );
    }

    // Get the return request
    const returnRequest = await db.query.returnRequests.findFirst({
      where: eq(returnRequests.id, returnRequestId),
    });

    if (!returnRequest) {
      return NextResponse.json({ error: "Return request not found" }, { status: 404 });
    }

    // Update return request status
    let newStatus: string;
    switch (decision) {
      case "approve":
        newStatus = "approved";
        break;
      case "deny":
        newStatus = "denied";
        break;
      case "ask_questions":
        newStatus = "reviewing";
        break;
      case "items_received":
        newStatus = "items_received";
        break;
      default:
        return NextResponse.json({ error: "Invalid decision" }, { status: 400 });
    }

    // Update the return request
    await db
      .update(returnRequests)
      .set({
        status: newStatus as any,
        staffNotes,
        reviewedBy: session.user.id,
        reviewedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(returnRequests.id, returnRequestId));

    // Schedule appropriate follow-up action
    if (decision === "approve") {
      // Send approval email to customer
      await scheduleApprovalEmail(returnRequest);

      // Get the actual agent for this site
      const agent = await db.query.agents.findFirst({
        where: eq(agents.siteId, returnRequest.siteId!),
      });

      if (agent) {
        // Enqueue job to create Shopify return
        await enqueueJob("return_create", agent.id, {
          sessionId: returnRequest.sessionId!,
          agentId: agent.id,
          siteId: returnRequest.siteId!,
          returnRequestId,
          stage: "create_shopify_return",
        });
      } else {
        console.error("No agent found for site:", returnRequest.siteId);
      }
    } else if (decision === "deny") {
      // Send denial email to customer
      await scheduleDenialEmail(returnRequest, denyReason || staffNotes);
    } else if (decision === "ask_questions") {
      // Send email with additional questions and chat continuation link
      await scheduleQuestionEmail(returnRequest, additionalQuestions, staffNotes);
    } else if (decision === "items_received") {
      // Items have been received and confirmed by staff - process refund
      const agent = await db.query.agents.findFirst({
        where: eq(agents.siteId, returnRequest.siteId!),
      });

      if (agent) {
        // Enqueue job to process refund
        await enqueueJob("return_refund", agent.id, {
          sessionId: returnRequest.sessionId!,
          agentId: agent.id,
          siteId: returnRequest.siteId!,
          returnRequestId,
          stage: "process_refund",
        });
      } else {
        console.error("No agent found for site:", returnRequest.siteId);
      }

      // Send items received confirmation email to customer
      await scheduleItemsReceivedEmail(returnRequest);
    }

    return NextResponse.json({
      success: true,
      message: `Return request ${decision}d successfully`,
      newStatus,
    });
  } catch (error) {
    console.error("Error processing return decision:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 },
    );
  }
}

/**
 * Schedule approval email
 */
async function scheduleApprovalEmail(returnRequest: any) {
  if (returnRequest.customerEmail) {
    // Get agent and site info for email
    const agent = await db.query.agents.findFirst({
      where: eq(agents.siteId, returnRequest.siteId),
    });
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, returnRequest.siteId),
    });
    const agentName = agent?.name || "AI Agent";
    const shopName = site?.name || "Webshop";

    await scheduleEmail(
      returnRequest.customerEmail,
      "return_approved",
      {
        orderNumber: returnRequest.orderNumber,
        customerName: returnRequest.customerEmail.split("@")[0], // Simple fallback
        staffNotes: returnRequest.staffNotes,
        agentName,
        shopName,
      },
      {
        returnRequestId: returnRequest.id,
        from: `${shopName} <<EMAIL>>`,
      },
    );
  }
}

/**
 * Schedule denial email
 */
async function scheduleDenialEmail(returnRequest: any, reason?: string) {
  if (returnRequest.customerEmail) {
    // Get agent and site info for email
    const agent = await db.query.agents.findFirst({
      where: eq(agents.siteId, returnRequest.siteId),
    });
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, returnRequest.siteId),
    });
    const agentName = agent?.name || "AI Agent";
    const shopName = site?.name || "Webshop";

    await scheduleEmail(
      returnRequest.customerEmail,
      "return_denied",
      {
        orderNumber: returnRequest.orderNumber,
        customerName: returnRequest.customerEmail.split("@")[0], // Simple fallback
        reason: reason,
        staffNotes: reason,
        agentName,
        shopName,
      },
      {
        returnRequestId: returnRequest.id,
        from: `${shopName} <<EMAIL>>`,
      },
    );
  }
}

/**
 * Schedule question email
 */
async function scheduleQuestionEmail(returnRequest: any, questions?: string, notes?: string) {
  if (returnRequest.customerEmail && returnRequest.sessionId) {
    // Get the agent for this site to create the correct chat URL
    const agent = await db.query.agents.findFirst({
      where: eq(agents.siteId, returnRequest.siteId),
    });

    if (!agent) {
      console.error("No agent found for site:", returnRequest.siteId);
      return;
    }

    // Get site info for email sender
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, returnRequest.siteId),
    });
    const shopName = site?.name || "Webshop";

    await scheduleEmail(
      returnRequest.customerEmail,
      "return_questions",
      {
        orderNumber: returnRequest.orderNumber,
        customerName: returnRequest.customerEmail.split("@")[0],
        questions: questions || notes,
        staffNotes: notes,
        agentName: agent.name || "AI Agent",
        shopName,
        chatUrl: `${process.env.NEXT_PUBLIC_APP_URL}/chat/${agent.id}?sessionId=${returnRequest.sessionId}`,
        returnRequestId: returnRequest.id,
      },
      {
        returnRequestId: returnRequest.id,
        from: `${shopName} <<EMAIL>>`,
      },
    );
  }
}

/**
 * Schedule items received confirmation email
 */
async function scheduleItemsReceivedEmail(returnRequest: any) {
  if (returnRequest.customerEmail) {
    // Get agent and site info for email
    const agent = await db.query.agents.findFirst({
      where: eq(agents.siteId, returnRequest.siteId),
    });
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, returnRequest.siteId),
    });
    const agentName = agent?.name || "AI Agent";
    const shopName = site?.name || "Webshop";

    await scheduleEmail(
      returnRequest.customerEmail,
      "return_items_received",
      {
        orderNumber: returnRequest.orderNumber,
        customerName: returnRequest.customerEmail.split("@")[0], // Simple fallback
        returnRequestId: returnRequest.id,
        agentName,
        shopName,
      },
      {
        returnRequestId: returnRequest.id,
        from: `${shopName} <<EMAIL>>`,
      },
    );
  }
}
