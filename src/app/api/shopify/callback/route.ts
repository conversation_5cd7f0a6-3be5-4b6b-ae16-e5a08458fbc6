import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { sites } from "@/db/schema";
import { eq } from "drizzle-orm";
import { registerShopifyWebhooks } from "@/lib/shopify/webhook-manager";
import { queueJob } from "@/lib/workers/job-queue";

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const code = searchParams.get("code");
  const siteId = searchParams.get("state");
  const shop = searchParams.get("shop");

  const baseUrl =
    process.env.NODE_ENV === "development"
      ? "http://localhost:3000"
      : process.env.NEXT_PUBLIC_APP_URL;

  if (!code || !siteId || !shop) {
    return NextResponse.redirect(`${baseUrl}/onboarding/site?error=invalid_callback`);
  }

  try {
    // Exchange the code for an access token
    const tokenResponse = await fetch(`https://${shop}/admin/oauth/access_token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        client_id: process.env.SHOPIFY_CLIENT_ID,
        client_secret: process.env.SHOPIFY_CLIENT_SECRET,
        code,
      }),
    });

    if (!tokenResponse.ok) {
      throw new Error("Failed to get access token");
    }

    const { access_token } = await tokenResponse.json();

    // Update the site with the Shopify details
    await db
      .update(sites)
      .set({
        platformToken: access_token,
        platformShopId: shop,
      })
      .where(eq(sites.id, siteId));

    // Register webhooks for this shop
    try {
      console.log(`🔗 Registering webhooks for site ${siteId}`);
      await registerShopifyWebhooks(siteId);
      console.log(`✅ Webhooks registered successfully`);
    } catch (webhookError) {
      // Log but don't fail the OAuth flow
      console.error("Failed to register webhooks:", webhookError);
    }

    // Queue initial product sync
    try {
      console.log(`📦 Queuing initial product sync for site ${siteId}`);
      await queueJob({
        type: "initial_product_sync" as any,
        payload: {
          siteId,
          sessionId: `oauth_${siteId}`,
          agentId: "",
          jobType: "initial_product_sync",
        },
        delay: 5, // Delay 5 seconds to ensure OAuth is fully processed
      });
    } catch (syncError) {
      console.error("Failed to queue product sync:", syncError);
    }

    return NextResponse.redirect(`${baseUrl}/onboarding/agent?siteId=${siteId}`);
  } catch (error) {
    console.error("Shopify callback error:", error);
    return NextResponse.redirect(`${baseUrl}/onboarding/site?error=callback_failed`);
  }
}
