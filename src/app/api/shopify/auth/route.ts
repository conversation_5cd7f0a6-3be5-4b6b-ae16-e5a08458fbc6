import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/db";
import { sites } from "@/db/schema";
import { eq } from "drizzle-orm";

const SHOPIFY_CLIENT_ID = process.env.SHOPIFY_CLIENT_ID!;
const SHOPIFY_CLIENT_SECRET = process.env.SHOPIFY_CLIENT_SECRET!;
const SHOPIFY_SCOPES = [
  "read_products",
  "write_products",
  "read_orders",
  "write_orders",
  "read_returns",
  "write_returns",
  "read_customers",
  "write_customers",
  "read_inventory",
  "write_inventory",
  "read_fulfillments",
  "write_fulfillments",
].join(",");

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const siteId = searchParams.get("siteId");

  if (!process.env.SHOPIFY_CLIENT_ID) {
    return NextResponse.json({ error: "Shopify client ID not configured" }, { status: 500 });
  }

  if (!siteId) {
    return NextResponse.json({ error: "Site ID is required" }, { status: 400 });
  }

  const redirectUri =
    process.env.NODE_ENV === "development"
      ? "http://localhost:3000/api/shopify/callback"
      : `${process.env.NEXT_PUBLIC_APP_URL}/api/shopify/callback`;

  const shopifyAuthUrl = new URL("https://shopify.com/admin/oauth/authorize");
  shopifyAuthUrl.searchParams.set("client_id", process.env.SHOPIFY_CLIENT_ID);
  shopifyAuthUrl.searchParams.set("scope", SHOPIFY_SCOPES);
  shopifyAuthUrl.searchParams.set("redirect_uri", redirectUri);
  shopifyAuthUrl.searchParams.set("state", siteId);

  return NextResponse.redirect(shopifyAuthUrl.toString());
}
