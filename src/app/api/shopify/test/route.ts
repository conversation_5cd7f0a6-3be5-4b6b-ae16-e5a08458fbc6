import { NextRequest, NextResponse } from "next/server";
import { ShopifyClient } from "@/lib/clients";

/**
 * Test endpoint to verify Shopify API connection and permissions
 * GET /api/shopify/test?siteId=xxx
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const siteId = searchParams.get("siteId");

    if (!siteId) {
      return NextResponse.json({ error: "Site ID is required" }, { status: 400 });
    }

    // Create a Shopify client from the site ID
    const shopifyClient = await ShopifyClient.fromSiteId(siteId);

    // Test the connection
    const testResult = await shopifyClient.testConnection();

    if (!testResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: `Connection test failed: ${testResult.message}`,
        },
        { status: 500 },
      );
    }

    // If connection is successful, try to verify specific permissions
    // by making a small query that requires the necessary scopes
    try {
      // Query to check if we have access to orders (read_orders scope)
      const orderQuery = `
        query {
          orders(first: 1) {
            edges {
              node {
                id
              }
            }
          }
        }
      `;

      await shopifyClient.query(orderQuery);

      return NextResponse.json({
        success: true,
        message: testResult.message,
        permissions: {
          orders: true,
        },
      });
    } catch (error) {
      // If we can connect but can't query orders, we might be missing permissions
      return NextResponse.json({
        success: true,
        message: `Connected to Shopify, but missing some permissions: ${error instanceof Error ? error.message : "Unknown error"}`,
        permissions: {
          orders: false,
        },
      });
    }
  } catch (error) {
    console.error("Shopify test error:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
