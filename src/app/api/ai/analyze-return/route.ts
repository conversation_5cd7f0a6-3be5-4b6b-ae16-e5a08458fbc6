import { NextRequest, NextResponse } from "next/server";
import { analyzeReturn } from "@/lib/ai/return-analysis";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderNumber, orderValue, chatHistory, customerEmail, returnReason } = body;

    if (!orderNumber || !chatHistory || !customerEmail) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    const analysis = await analyzeReturn(
      orderNumber,
      orderValue || 100,
      chatHistory,
      customerEmail,
      returnReason || "No reason provided",
    );

    return NextResponse.json(analysis);
  } catch (error) {
    console.error("Error in analyze-return API:", error);
    return NextResponse.json({ error: "Failed to analyze return" }, { status: 500 });
  }
}
