import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { emailHistory, chatSessions, chatMessages, shopPolicyConfigs, agents } from "@/db/schema";
import { eq, and, desc } from "drizzle-orm";
import { queueJob } from "@/lib/workers/job-queue";
import { findSiteByInboundEmail, getSiteInboundEmailByName } from "@/lib/email/site-emails";

interface ForwardEmailWebhookPayload {
  text: string;
  html?: string;
  messageId: string;
  date: string;
  from: {
    value: Array<{
      address: string;
      name: string;
    }>;
    text: string;
    html: string;
  };
  recipients: string[];
  session: {
    recipient: string;
    sender: string;
    mta: string;
    arrivalDate: string;
    arrivalTime: number;
  };
  headerLines: Array<{
    key: string;
    line: string;
  }>;
  attachments?: Array<{
    filename: string;
    contentType: string;
    size: number;
    content: {
      type: string;
      data: number[];
    };
  }>;
}

/**
 * Get or create chat session for email conversation
 */
async function getOrCreateEmailSession(
  siteId: string,
  customerEmail: string,
  existingThreadId?: string,
): Promise<string> {
  // Look for existing session based on email history or thread ID
  let existingSession = null;

  if (existingThreadId) {
    // Try to find session by message thread
    const threadHistory = await db.query.emailHistory.findFirst({
      where: and(eq(emailHistory.siteId, siteId), eq(emailHistory.messageId, existingThreadId)),
    });

    if (threadHistory?.sessionId) {
      existingSession = await db.query.chatSessions.findFirst({
        where: eq(chatSessions.id, threadHistory.sessionId),
      });
    }
  }

  if (!existingSession) {
    // Look for recent open session with same customer email
    const recentHistory = await db.query.emailHistory.findFirst({
      where: and(eq(emailHistory.siteId, siteId), eq(emailHistory.customerEmail, customerEmail)),
      orderBy: [desc(emailHistory.createdAt)],
    });

    if (recentHistory?.sessionId) {
      existingSession = await db.query.chatSessions.findFirst({
        where: and(eq(chatSessions.id, recentHistory.sessionId), eq(chatSessions.state, "open")),
      });
    }
  }

  if (existingSession) {
    return existingSession.id;
  }

  // Create new session
  const newSession = await db
    .insert(chatSessions)
    .values({
      externalUserId: customerEmail,
      state: "open",
      metadata: {
        channel: "email",
        customerEmail,
      },
    })
    .returning({ id: chatSessions.id });

  return newSession[0].id;
}

/**
 * Get email conversation history for context
 */
async function getEmailHistory(siteId: string, customerEmail: string, limit = 10) {
  return await db.query.emailHistory.findMany({
    where: and(eq(emailHistory.siteId, siteId), eq(emailHistory.customerEmail, customerEmail)),
    orderBy: [desc(emailHistory.createdAt)],
    limit,
  });
}

/**
 * Process inbound email and generate AI response
 */
export async function POST(request: NextRequest) {
  try {
    const payload: ForwardEmailWebhookPayload = await request.json();

    // Extract key information from forwardemail.net webhook
    const toEmail = payload.session.recipient; // The recipient email (e.g., <EMAIL>)
    const fromEmail = payload.session.sender; // The original sender
    const fromAddress = payload.from.value[0]?.address || fromEmail;
    const fromName = payload.from.value[0]?.name || "";

    // Extract subject from headers
    const subjectHeader = payload.headerLines.find((h) => h.key.toLowerCase() === "subject");
    const subject = subjectHeader
      ? subjectHeader.line.replace("Subject: ", "").trim()
      : "No Subject";

    // Extract In-Reply-To header for threading
    const inReplyToHeader = payload.headerLines.find((h) => h.key.toLowerCase() === "in-reply-to");
    const inReplyTo = inReplyToHeader
      ? inReplyToHeader.line.replace("In-Reply-To: ", "").trim()
      : undefined;

    // Find the site based on the "to" email address
    const site = await findSiteByInboundEmail(toEmail);
    if (!site) {
      console.error("❌ No site found for email address:", toEmail);
      return NextResponse.json({ error: "Site not found" }, { status: 404 });
    }

    // Get or create chat session
    const sessionId = await getOrCreateEmailSession(site.id, fromEmail, inReplyTo);

    // Store inbound email in history
    await db.insert(emailHistory).values({
      siteId: site.id,
      sessionId,
      customerEmail: fromEmail,
      direction: "inbound",
      subject: subject,
      content: payload.text,
      messageId: payload.messageId,
      replyToId: inReplyTo || null,
      aiGenerated: false,
    });

    // Add customer message to chat session
    await db.insert(chatMessages).values({
      sessionId,
      role: "user",
      content: payload.text,
    });

    // Get the agent for this site
    const agent = await db.query.agents.findFirst({
      where: eq(agents.siteId, site.id),
    });

    if (!agent) {
      console.error("❌ No agent found for site:", site.name);
      return NextResponse.json({ error: "No agent configured for this site" }, { status: 404 });
    }

    // Get email history for context
    const history = await getEmailHistory(site.id, fromEmail);

    // Get shop policy config
    const policyConfig = await db.query.shopPolicyConfigs.findFirst({
      where: and(eq(shopPolicyConfigs.siteId, site.id), eq(shopPolicyConfigs.active, true)),
    });

    // Calculate human-like delay for email response
    const now = new Date();
    const dutchHour = now.getUTCHours() + 1; // Dutch timezone (UTC+1)
    const isBusinessHours = dutchHour >= 9 && dutchHour < 18;
    const isWeekend = now.getUTCDay() === 0 || now.getUTCDay() === 6;

    let delaySeconds: number;
    if (isWeekend || !isBusinessHours) {
      // Outside business hours or weekend: 10-30 minutes
      delaySeconds = Math.floor(Math.random() * 20 + 10) * 60;
    } else {
      // Business hours: 2-5 minutes
      delaySeconds = Math.floor(Math.random() * 3 + 2) * 60;
    }

    // Queue AI processing job with delay
    await queueJob({
      type: "email_response",
      payload: {
        sessionId,
        siteId: site.id,
        agentId: agent.id,
        customerEmail: fromEmail,
        customerName: fromName,
        inboundSubject: subject,
        inboundContent: payload.text,
        messageId: payload.messageId,
        replyToId: inReplyTo,
        emailHistory: history,
        policyConfig,
        attachments: payload.attachments || [],
      },
      delay: delaySeconds,
    });

    return NextResponse.json({
      success: true,
      sessionId,
      message: "Email received and processing started",
    });
  } catch (error) {
    console.error("❌ Error processing inbound email:", error);
    return NextResponse.json({ error: "Failed to process email" }, { status: 500 });
  }
}
