import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/db";
import { sites, orgMembers } from "@/db/schema";
import { eq } from "drizzle-orm";
import { generateUUID } from "@/lib/utils";

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, platform } = await req.json();
    if (!name || !platform) {
      return NextResponse.json({ error: "Name and platform are required" }, { status: 400 });
    }

    // Get user's organization
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Create site
    const siteId = generateUUID();
    const [site] = await db
      .insert(sites)
      .values({
        id: siteId,
        name,
        platform,
        orgId: userOrg.orgId,
        url: "", // Will be set during OAuth
      })
      .returning();

    return NextResponse.json(site);
  } catch (error) {
    console.error("Failed to create site:", error);
    return NextResponse.json({ error: "Failed to create site" }, { status: 500 });
  }
}
