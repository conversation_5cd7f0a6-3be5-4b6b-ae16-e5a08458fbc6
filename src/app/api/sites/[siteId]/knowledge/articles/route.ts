import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/db";
import { knowledgeSources, sites, orgMembers } from "@/db/schema";
import { eq, and, sql } from "drizzle-orm";
import { queueJob } from "@/lib/workers/job-queue";
import { v4 as uuidv4 } from "uuid";

/**
 * POST /api/sites/[siteId]/knowledge/articles - Create a new knowledge article
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ siteId: string }> }) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId } = await params;
    const { title, content, topicId } = await req.json();

    if (!title || !content) {
      return NextResponse.json({ error: "Title and content are required" }, { status: 400 });
    }

    if (typeof title !== "string" || typeof content !== "string") {
      return NextResponse.json({ error: "Title and content must be strings" }, { status: 400 });
    }

    if (title.length > 120) {
      return NextResponse.json({ error: "Title must be 120 characters or less" }, { status: 400 });
    }

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Create knowledge source entry
    const knowledgeSourceId = uuidv4();
    const [knowledgeSource] = await db
      .insert(knowledgeSources)
      .values({
        id: knowledgeSourceId,
        siteId,
        topicId: topicId || null, // Optional topic association
        kind: "article",
        label: title,
        meta: {
          content,
          createdBy: session.user.id,
          createdAt: new Date().toISOString(),
        },
        status: "processing",
      })
      .returning();

    // Queue background job to process the knowledge source
    await queueJob({
      type: "process_knowledge_source",
      payload: {
        sourceId: knowledgeSourceId,
        siteId,
        kind: "article",
        title,
        content,
      },
    });

    return NextResponse.json(knowledgeSource, { status: 201 });
  } catch (error) {
    console.error("Failed to create knowledge article:", error);
    return NextResponse.json({ error: "Failed to create knowledge article" }, { status: 500 });
  }
}

/**
 * GET /api/sites/[siteId]/knowledge/articles - List all knowledge articles for the site
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ siteId: string }> }) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId } = await params;
    const { searchParams } = new URL(req.url);

    // Parse parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;
    const topicId = searchParams.get("topicId");

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json({ error: "Invalid pagination parameters" }, { status: 400 });
    }

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Build where conditions
    const whereConditions = [
      eq(knowledgeSources.siteId, siteId),
      eq(knowledgeSources.kind, "article"),
    ];

    // Add topic filter if provided
    if (topicId) {
      whereConditions.push(eq(knowledgeSources.topicId, topicId));
    }

    // Get knowledge articles for the site
    const articles = await db.query.knowledgeSources.findMany({
      where: and(...whereConditions),
      orderBy: [knowledgeSources.insertedAt],
      limit,
      offset,
    });

    // Get total count for pagination
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(knowledgeSources)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      articles,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Failed to fetch knowledge articles:", error);
    return NextResponse.json({ error: "Failed to fetch knowledge articles" }, { status: 500 });
  }
}
