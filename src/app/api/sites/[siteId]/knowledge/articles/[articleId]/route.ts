import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/db";
import { knowledgeSources, sites, orgMembers } from "@/db/schema";
import { eq, and, sql } from "drizzle-orm";
import { queueJob } from "@/lib/workers/job-queue";

/**
 * GET /api/sites/[siteId]/knowledge/articles/[articleId] - Get a single knowledge article
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ siteId: string; articleId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId, articleId } = await params;

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Get the specific knowledge article
    const article = await db.query.knowledgeSources.findFirst({
      where: and(
        eq(knowledgeSources.id, articleId),
        eq(knowledgeSources.siteId, siteId),
        eq(knowledgeSources.kind, "article"),
      ),
    });

    if (!article) {
      return NextResponse.json({ error: "Article not found" }, { status: 404 });
    }

    return NextResponse.json(article);
  } catch (error) {
    console.error("Failed to fetch knowledge article:", error);
    return NextResponse.json({ error: "Failed to fetch knowledge article" }, { status: 500 });
  }
}

/**
 * PUT /api/sites/[siteId]/knowledge/articles/[articleId] - Update a knowledge article
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ siteId: string; articleId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId, articleId } = await params;
    const { title, content } = await req.json();

    // Validate required fields
    if (!title || !content) {
      return NextResponse.json({ error: "Title and content are required" }, { status: 400 });
    }

    // Validate input types
    if (typeof title !== "string" || typeof content !== "string") {
      return NextResponse.json({ error: "Title and content must be strings" }, { status: 400 });
    }

    if (title.length > 120) {
      return NextResponse.json({ error: "Title must be 120 characters or less" }, { status: 400 });
    }

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Check if the knowledge source exists and belongs to the site
    const existingArticle = await db.query.knowledgeSources.findFirst({
      where: and(
        eq(knowledgeSources.id, articleId),
        eq(knowledgeSources.siteId, siteId),
        eq(knowledgeSources.kind, "article"),
      ),
    });

    if (!existingArticle) {
      return NextResponse.json({ error: "Article not found or access denied" }, { status: 404 });
    }

    // Update the knowledge source
    const [updatedArticle] = await db
      .update(knowledgeSources)
      .set({
        label: title,
        meta: {
          ...((existingArticle.meta as any) || {}),
          content,
          updatedBy: session.user.id,
          updatedAt: new Date().toISOString(),
        },
        status: "processing", // Set back to processing since content changed
      })
      .where(eq(knowledgeSources.id, articleId))
      .returning();

    // Queue background job to reprocess the article
    await queueJob({
      type: "process_knowledge_source",
      payload: {
        sourceId: articleId,
        siteId,
        kind: "article",
        title,
        content,
      },
    });

    return NextResponse.json(updatedArticle);
  } catch (error) {
    console.error("Failed to update knowledge article:", error);
    return NextResponse.json({ error: "Failed to update knowledge article" }, { status: 500 });
  }
}

/**
 * DELETE /api/sites/[siteId]/knowledge/articles/[articleId] - Delete a knowledge article
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ siteId: string; articleId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId, articleId } = await params;

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Check if the knowledge source exists and belongs to the site
    const existingArticle = await db.query.knowledgeSources.findFirst({
      where: and(
        eq(knowledgeSources.id, articleId),
        eq(knowledgeSources.siteId, siteId),
        eq(knowledgeSources.kind, "article"),
      ),
    });

    if (!existingArticle) {
      return NextResponse.json({ error: "Article not found or access denied" }, { status: 404 });
    }

    // Delete the knowledge source (cascades to chunks)
    await db.delete(knowledgeSources).where(eq(knowledgeSources.id, articleId));

    return NextResponse.json({ success: true, message: "Article deleted successfully" });
  } catch (error) {
    console.error("Failed to delete knowledge article:", error);
    return NextResponse.json({ error: "Failed to delete knowledge article" }, { status: 500 });
  }
}
