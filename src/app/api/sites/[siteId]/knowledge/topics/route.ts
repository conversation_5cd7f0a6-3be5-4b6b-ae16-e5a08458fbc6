import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/db";
import { knowledgeTopics, knowledgeSources, sites, orgMembers } from "@/db/schema";
import { eq, and, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "") // Remove special characters
    .replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
}

/**
 * GET /api/sites/[siteId]/knowledge/topics - List all topics for the site
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ siteId: string }> }) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId } = await params;

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Get all topics for the site, ordered by displayOrder
    const topics = await db.query.knowledgeTopics.findMany({
      where: eq(knowledgeTopics.siteId, siteId),
      orderBy: [knowledgeTopics.displayOrder, knowledgeTopics.name],
    });

    // Get article counts for each topic
    const topicsWithCounts = await Promise.all(
      topics.map(async (topic) => {
        const countResult = await db
          .select({ count: sql<number>`count(*)` })
          .from(knowledgeSources)
          .where(and(eq(knowledgeSources.topicId, topic.id), eq(knowledgeSources.kind, "article")));

        return {
          ...topic,
          articleCount: countResult[0]?.count || 0,
        };
      }),
    );

    return NextResponse.json({ topics: topicsWithCounts });
  } catch (error) {
    console.error("Failed to fetch topics:", error);
    return NextResponse.json({ error: "Failed to fetch topics" }, { status: 500 });
  }
}

/**
 * POST /api/sites/[siteId]/knowledge/topics - Create a new topic
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ siteId: string }> }) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId } = await params;
    const { name, description, icon } = await req.json();

    if (!name) {
      return NextResponse.json({ error: "Name is required" }, { status: 400 });
    }

    if (typeof name !== "string" || name.length > 100) {
      return NextResponse.json(
        { error: "Name must be a string of 100 characters or less" },
        { status: 400 },
      );
    }

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Generate slug from name
    let slug = generateSlug(name);

    // Ensure slug is unique within the site
    let slugSuffix = 1;
    let finalSlug = slug;
    while (true) {
      const existing = await db.query.knowledgeTopics.findFirst({
        where: and(eq(knowledgeTopics.siteId, siteId), eq(knowledgeTopics.slug, finalSlug)),
      });

      if (!existing) break;

      finalSlug = `${slug}-${slugSuffix}`;
      slugSuffix++;
    }

    // Get the highest display order
    const maxOrderResult = await db
      .select({ maxOrder: sql<number>`COALESCE(MAX(display_order), 0)` })
      .from(knowledgeTopics)
      .where(eq(knowledgeTopics.siteId, siteId));

    const nextOrder = (maxOrderResult[0]?.maxOrder || 0) + 1;

    // Create the topic
    const topicId = uuidv4();
    const [topic] = await db
      .insert(knowledgeTopics)
      .values({
        id: topicId,
        siteId,
        slug: finalSlug,
        name,
        description: description || null,
        icon: icon || null,
        displayOrder: nextOrder,
      })
      .returning();

    return NextResponse.json(topic, { status: 201 });
  } catch (error) {
    console.error("Failed to create topic:", error);
    return NextResponse.json({ error: "Failed to create topic" }, { status: 500 });
  }
}
