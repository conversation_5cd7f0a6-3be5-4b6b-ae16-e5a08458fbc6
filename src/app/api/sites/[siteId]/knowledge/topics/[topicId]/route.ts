import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/db";
import { knowledgeTopics, sites, orgMembers, knowledgeSources } from "@/db/schema";
import { eq, and, sql } from "drizzle-orm";

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "") // Remove special characters
    .replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
}

/**
 * GET /api/sites/[siteId]/knowledge/topics/[topicId] - Get a single topic
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ siteId: string; topicId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId, topicId } = await params;

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Get the specific topic
    const topic = await db.query.knowledgeTopics.findFirst({
      where: and(eq(knowledgeTopics.id, topicId), eq(knowledgeTopics.siteId, siteId)),
    });

    if (!topic) {
      return NextResponse.json({ error: "Topic not found" }, { status: 404 });
    }

    // Get article count for this topic
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(knowledgeSources)
      .where(and(eq(knowledgeSources.topicId, topic.id), eq(knowledgeSources.kind, "article")));

    return NextResponse.json({
      ...topic,
      articleCount: countResult[0]?.count || 0,
    });
  } catch (error) {
    console.error("Failed to fetch topic:", error);
    return NextResponse.json({ error: "Failed to fetch topic" }, { status: 500 });
  }
}

/**
 * PUT /api/sites/[siteId]/knowledge/topics/[topicId] - Update a topic
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ siteId: string; topicId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId, topicId } = await params;
    const { name, description, icon, displayOrder } = await req.json();

    // Validate input
    if (name && (typeof name !== "string" || name.length > 100)) {
      return NextResponse.json(
        { error: "Name must be a string of 100 characters or less" },
        { status: 400 },
      );
    }

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Check if the topic exists and belongs to the site
    const existingTopic = await db.query.knowledgeTopics.findFirst({
      where: and(eq(knowledgeTopics.id, topicId), eq(knowledgeTopics.siteId, siteId)),
    });

    if (!existingTopic) {
      return NextResponse.json({ error: "Topic not found or access denied" }, { status: 404 });
    }

    // Build update object
    const updateData: any = {};

    if (name !== undefined) {
      updateData.name = name;

      // Generate new slug if name changed
      let slug = generateSlug(name);
      let slugSuffix = 1;
      let finalSlug = slug;

      // Ensure slug is unique within the site (excluding current topic)
      while (true) {
        const existing = await db.query.knowledgeTopics.findFirst({
          where: and(
            eq(knowledgeTopics.siteId, siteId),
            eq(knowledgeTopics.slug, finalSlug),
            sql`id != ${topicId}`,
          ),
        });

        if (!existing) break;

        finalSlug = `${slug}-${slugSuffix}`;
        slugSuffix++;
      }

      updateData.slug = finalSlug;
    }

    if (description !== undefined) updateData.description = description || null;
    if (icon !== undefined) updateData.icon = icon || null;
    if (displayOrder !== undefined) updateData.displayOrder = displayOrder;

    // Update the topic
    const [updatedTopic] = await db
      .update(knowledgeTopics)
      .set(updateData)
      .where(eq(knowledgeTopics.id, topicId))
      .returning();

    return NextResponse.json(updatedTopic);
  } catch (error) {
    console.error("Failed to update topic:", error);
    return NextResponse.json({ error: "Failed to update topic" }, { status: 500 });
  }
}

/**
 * DELETE /api/sites/[siteId]/knowledge/topics/[topicId] - Delete a topic
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ siteId: string; topicId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { siteId, topicId } = await params;

    // Validate user has access to the site
    const userOrg = await db.query.orgMembers.findFirst({
      where: eq(orgMembers.userId, session.user.id),
    });

    if (!userOrg) {
      return NextResponse.json({ error: "No organization found" }, { status: 404 });
    }

    // Check if site exists and belongs to user's organization
    const site = await db.query.sites.findFirst({
      where: and(eq(sites.id, siteId), eq(sites.orgId, userOrg.orgId)),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found or access denied" }, { status: 404 });
    }

    // Check if the topic exists and belongs to the site
    const existingTopic = await db.query.knowledgeTopics.findFirst({
      where: and(eq(knowledgeTopics.id, topicId), eq(knowledgeTopics.siteId, siteId)),
    });

    if (!existingTopic) {
      return NextResponse.json({ error: "Topic not found or access denied" }, { status: 404 });
    }

    // Check if there are any articles in this topic
    const articleCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(knowledgeSources)
      .where(and(eq(knowledgeSources.topicId, topicId), eq(knowledgeSources.kind, "article")));

    if (articleCount[0]?.count > 0) {
      return NextResponse.json(
        {
          error: "Cannot delete topic with articles. Please delete or move all articles first.",
        },
        { status: 400 },
      );
    }

    // Delete the topic
    await db.delete(knowledgeTopics).where(eq(knowledgeTopics.id, topicId));

    return NextResponse.json({ success: true, message: "Topic deleted successfully" });
  } catch (error) {
    console.error("Failed to delete topic:", error);
    return NextResponse.json({ error: "Failed to delete topic" }, { status: 500 });
  }
}
