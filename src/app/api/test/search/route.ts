import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { catalogProducts, catalogTextEmbeds, catalogImages } from "@/db/schema";
import { eq, sql } from "drizzle-orm";
import { findProductsByText, findProductsByImage } from "@/lib/catalog/retrieval";
import { OpenAI } from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

/**
 * Test search endpoint
 * GET /api/test/search?siteId=xxx&query=text
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const siteId = searchParams.get("siteId");
    const query = searchParams.get("query");

    if (!siteId) {
      return NextResponse.json({ error: "siteId required" }, { status: 400 });
    }

    // First, let's check what products and embeddings we have
    const products = await db
      .select({
        id: catalogProducts.id,
        title: catalogProducts.title,
        hasTextEmbed: sql<boolean>`${catalogTextEmbeds.id} IS NOT NULL`,
        imageCount: sql<number>`COUNT(DISTINCT ${catalogImages.id})`,
      })
      .from(catalogProducts)
      .leftJoin(catalogTextEmbeds, eq(catalogTextEmbeds.productId, catalogProducts.id))
      .leftJoin(catalogImages, eq(catalogImages.productId, catalogProducts.id))
      .where(eq(catalogProducts.siteId, siteId))
      .groupBy(catalogProducts.id, catalogProducts.title, catalogTextEmbeds.id);

    let searchResults = null;

    if (query) {
      // Generate embedding for the search query
      const response = await openai.embeddings.create({
        model: "text-embedding-3-large",
        input: query,
      });

      const queryEmbedding = response.data[0].embedding;

      // Search for similar products
      searchResults = await findProductsByText(siteId, queryEmbedding, 5);
    }

    return NextResponse.json({
      siteId,
      productsInCatalog: products.length,
      products: products.map((p) => ({
        id: p.id,
        title: p.title,
        hasTextEmbedding: p.hasTextEmbed,
        imageCount: Number(p.imageCount),
      })),
      searchQuery: query,
      searchResults,
    });
  } catch (error: any) {
    console.error("Search test error:", error);
    return NextResponse.json(
      {
        error: "Search failed",
        details: error.message,
      },
      { status: 500 },
    );
  }
}
