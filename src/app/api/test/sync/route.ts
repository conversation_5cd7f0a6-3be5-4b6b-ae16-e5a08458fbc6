import { NextRequest, NextResponse } from "next/server";
import { processInitialProductSync } from "@/lib/workers/handlers/initial-sync";

/**
 * Manual trigger for product sync
 * GET /api/test/sync?siteId=xxx
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const siteId = searchParams.get("siteId");

    if (!siteId) {
      return NextResponse.json({ error: "siteId required" }, { status: 400 });
    }

    console.log(`Manual sync triggered for site ${siteId}`);

    const result = await processInitialProductSync({
      siteId,
      sessionId: `manual_${Date.now()}`,
      jobType: "initial_product_sync",
    });

    return NextResponse.json(result);
  } catch (error: any) {
    console.error("Manual sync error:", error);
    return NextResponse.json(
      {
        error: "Sync failed",
        details: error.message,
        stack: error.stack,
      },
      { status: 500 },
    );
  }
}
