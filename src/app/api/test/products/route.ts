import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { sites } from "@/db/schema";
import { eq } from "drizzle-orm";
import { ShopifyClient } from "@/lib/clients";

/**
 * Test endpoint to check if products can be fetched
 * GET /api/test/products?siteId=xxx
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const siteId = searchParams.get("siteId");

    if (!siteId) {
      return NextResponse.json({ error: "siteId required" }, { status: 400 });
    }

    const site = await db.query.sites.findFirst({
      where: eq(sites.id, siteId),
    });

    if (!site || !site.platformToken || !site.platformShopId) {
      return NextResponse.json({ error: "Site not found or missing credentials" }, { status: 404 });
    }

    const client = new ShopifyClient(site.platformToken, site.platformShopId);

    // Try a simple products query
    const query = `
      query {
        products(first: 10) {
          edges {
            node {
              id
              title
              status
            }
          }
          pageInfo {
            hasNextPage
          }
        }
      }
    `;

    console.log("Testing Shopify connection for:", site.platformShopId);

    try {
      const response = await client.query<any>(query);

      // Check the actual structure of the response
      const productsData = response.data?.products || response.products || response;
      const edges = productsData?.edges || [];

      return NextResponse.json({
        shop: site.platformShopId,
        productsFound: edges.length,
        products: edges.map((e: any) => ({
          id: e.node.id,
          title: e.node.title,
          status: e.node.status,
        })),
        hasMore: productsData?.pageInfo?.hasNextPage,
        responseStructure: {
          hasData: !!response.data,
          hasProducts: !!response.products,
          keys: Object.keys(response),
        },
        fullResponse: response,
      });
    } catch (error: any) {
      return NextResponse.json(
        {
          shop: site.platformShopId,
          error: "GraphQL query failed",
          details: error.message,
          response: error.response,
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Test endpoint error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
