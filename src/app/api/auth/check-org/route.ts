import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/db";
import { orgMembers } from "@/db/schema";
import { eq } from "drizzle-orm";

// Force Node.js runtime for pg compatibility
export const runtime = "nodejs";

export async function GET() {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ hasOrg: false });
  }

  const membership = await db.query.orgMembers.findFirst({
    where: eq(orgMembers.userId, session.user.id),
  });

  return NextResponse.json({ hasOrg: !!membership });
}
