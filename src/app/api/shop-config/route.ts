import { NextRequest, NextResponse } from "next/server";
import {
  loadShopPolicyConfig,
  createShopPolicyConfig,
  updateShopPolicyConfig,
  ShopPolicyConfig,
} from "@/lib/config/shop-policy";
import { seedDefaultConfigs, seedSiteConfig } from "@/lib/config/seed-default-configs";
import { auth } from "@/auth";
import { db } from "@/db";
import { shopPolicyConfigs, sites, orgMembers } from "@/db/schema";
import { eq, and } from "drizzle-orm";

/**
 * GET /api/shop-config?siteId=xxx - Get shop policy configuration
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const siteId = searchParams.get("siteId");
    const action = searchParams.get("action");

    // Seed all sites with default configs
    if (action === "seed-all") {
      await seedDefaultConfigs();
      return NextResponse.json({
        success: true,
        message: "Default configurations seeded successfully",
      });
    }

    if (!siteId) {
      return NextResponse.json(
        {
          error: "siteId parameter is required",
        },
        { status: 400 },
      );
    }

    // Seed specific site
    if (action === "seed") {
      const config = await seedSiteConfig(siteId);
      return NextResponse.json({
        success: true,
        config,
        message: "Configuration seeded successfully",
      });
    }

    // Get configuration
    const config = await loadShopPolicyConfig(siteId);
    return NextResponse.json({
      success: true,
      config,
    });
  } catch (error) {
    console.error("Shop config GET error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 },
    );
  }
}

/**
 * POST /api/shop-config - Create new shop policy configuration
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { siteId, config } = body;

    if (!siteId || !config) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Verify user has access to this site
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, siteId),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found" }, { status: 404 });
    }

    // Check if user is member of the organization
    const membership = await db.query.orgMembers.findFirst({
      where: and(eq(orgMembers.orgId, site.orgId!), eq(orgMembers.userId, session.user.id)),
    });

    if (!membership || (membership.role !== "owner" && membership.role !== "admin")) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    // Check if config already exists
    const existingConfig = await db.query.shopPolicyConfigs.findFirst({
      where: and(eq(shopPolicyConfigs.siteId, siteId), eq(shopPolicyConfigs.active, true)),
    });

    if (existingConfig) {
      // Update existing config
      await db
        .update(shopPolicyConfigs)
        .set({
          personalityPrompt: config.personalityPrompt,
          enabledTools: config.enabledTools,
          toolPolicies: config.toolPolicies,
          maxTurnsBeforeEscalation: config.maxTurnsBeforeEscalation,
          escalationEnabled: config.escalationEnabled,
          escalationEmail: config.escalationEmail,
          primaryModel: config.primaryModel,
          fallbackModel: config.fallbackModel,
          temperature: config.temperature,
          reflectionEnabled: config.reflectionEnabled,
          reflectionThreshold: config.reflectionThreshold,
          businessHours: config.businessHours,
          updatedAt: new Date(),
        })
        .where(eq(shopPolicyConfigs.id, existingConfig.id));
    } else {
      // Create new config
      await db.insert(shopPolicyConfigs).values({
        siteId,
        personalityPrompt: config.personalityPrompt,
        enabledTools: config.enabledTools,
        toolPolicies: config.toolPolicies,
        maxTurnsBeforeEscalation: config.maxTurnsBeforeEscalation,
        escalationEnabled: config.escalationEnabled,
        escalationEmail: config.escalationEmail,
        primaryModel: config.primaryModel,
        fallbackModel: config.fallbackModel,
        temperature: config.temperature,
        reflectionEnabled: config.reflectionEnabled,
        reflectionThreshold: config.reflectionThreshold,
        businessHours: config.businessHours,
        active: true,
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Shop config save error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * PUT /api/shop-config - Update shop policy configuration
 */
export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const { siteId, updates } = body;

    if (!siteId) {
      return NextResponse.json(
        {
          error: "siteId is required",
        },
        { status: 400 },
      );
    }

    if (!updates) {
      return NextResponse.json(
        {
          error: "updates object is required",
        },
        { status: 400 },
      );
    }

    const updatedConfig = await updateShopPolicyConfig(siteId, updates);

    return NextResponse.json({
      success: true,
      config: updatedConfig,
      message: "Configuration updated successfully",
    });
  } catch (error) {
    console.error("Shop config PUT error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 },
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { siteId, personalityPrompt } = body;

    if (!siteId || !personalityPrompt) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Verify user has access to this site
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, siteId),
    });

    if (!site) {
      return NextResponse.json({ error: "Site not found" }, { status: 404 });
    }

    // Check if user is member of the organization
    const membership = await db.query.orgMembers.findFirst({
      where: and(eq(orgMembers.orgId, site.orgId!), eq(orgMembers.userId, session.user.id)),
    });

    if (!membership || (membership.role !== "owner" && membership.role !== "admin")) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    // Update only the personality prompt
    const existingConfig = await db.query.shopPolicyConfigs.findFirst({
      where: and(eq(shopPolicyConfigs.siteId, siteId), eq(shopPolicyConfigs.active, true)),
    });

    if (existingConfig) {
      await db
        .update(shopPolicyConfigs)
        .set({
          personalityPrompt,
          updatedAt: new Date(),
        })
        .where(eq(shopPolicyConfigs.id, existingConfig.id));
    } else {
      // Create new config with just the personality prompt and defaults
      await db.insert(shopPolicyConfigs).values({
        siteId,
        personalityPrompt,
        enabledTools: ["lookupOrder", "createReturn", "checkInventory"],
        toolPolicies: {},
        maxTurnsBeforeEscalation: 5,
        escalationEnabled: true,
        escalationEmail: "",
        primaryModel: "gpt-4o",
        fallbackModel: "gpt-5-mini",
        temperature: 20,
        reflectionEnabled: true,
        reflectionThreshold: 40,
        businessHours: {
          timezone: "Europe/Amsterdam",
          schedule: {
            monday: { start: "09:00", end: "17:00", enabled: true },
            tuesday: { start: "09:00", end: "17:00", enabled: true },
            wednesday: { start: "09:00", end: "17:00", enabled: true },
            thursday: { start: "09:00", end: "17:00", enabled: true },
            friday: { start: "09:00", end: "17:00", enabled: true },
            saturday: { start: "10:00", end: "16:00", enabled: false },
            sunday: { start: "10:00", end: "16:00", enabled: false },
          },
        },
        active: true,
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Shop config update error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
