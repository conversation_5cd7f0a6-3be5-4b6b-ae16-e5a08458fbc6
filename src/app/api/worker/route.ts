import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import {
  jobs,
  chatMessages,
  returnRequests,
  emailNotifications,
  sites,
  agents,
  orgMembers,
  users,
  emailHistory as emailHistoryTable,
  knowledgeSources,
  knowledgeChunks,
} from "@/db/schema";
import { eq, and, or, inArray, desc } from "drizzle-orm";
import { ShopifyClient } from "@/lib/clients";
import { enqueueJob } from "@/lib/workers/job-queue";
import { enqueueQStashJob, JobTracker, AgentStateManager, QStashJobPayload } from "@/lib/upstash";
import { v4 as uuidv4 } from "uuid";
import { verifySignatureAppRouter } from "@upstash/qstash/nextjs";
import { processMessage } from "@/lib/graphs/index";
import { processPendingEmails, scheduleEmail, sendEmail } from "@/lib/email";
import { getSiteInboundEmailByName } from "@/lib/email/site-emails";
import { loadShopPolicyConfig } from "@/lib/config/shop-policy";
import { analyzeReturn } from "@/lib/ai/return-analysis";
import { generateSourceEmbeddings } from "@/lib/embeddings";
import { processTextEmbedding } from "@/lib/workers/handlers/text-embed";
import { processImageEmbedding } from "@/lib/workers/handlers/image-embed";
import { processInitialProductSync } from "@/lib/workers/handlers/initial-sync";
import { processShopifyWebhook } from "@/lib/workers/handlers/webhook-processor";
import type {
  ProductTextEmbedPayload,
  ProductImageEmbedPayload,
} from "@/lib/workers/types/catalog";

interface JobPayload {
  sessionId: string;
  agentId?: string; // Make optional since some email jobs might not have it initially
  siteId: string;
  orderNumber?: string;
  orderContext?: string;
  email?: string;
  phone?: string;
  userInfo?: Record<string, any>;
  returnRequestId?: string;
  stage?: string;
  // Email-specific fields
  customerEmail?: string;
  customerName?: string;
  inboundSubject?: string;
  inboundContent?: string;
  messageId?: string;
  replyToId?: string;
  emailHistory?: any[];
  policyConfig?: any;
  attachments?: any[];
  jobType:
    | "order_status"
    | "return_create"
    | "return_request_created" // This is a new job type for the return request creation
    | "email_followup"
    | "damage_check"
    | "return_refund"
    | "email_response"
    | "process_knowledge_source"
    | "generate_embeddings"
    | "product_recommendations";
  sourceId?: string; // For knowledge processing jobs
  [key: string]: any;
}

/**
 * Resolves agentId from payload or fetches from database based on siteId
 * Provides enterprise-grade error handling and type safety
 */
async function resolveAgentId(payload: JobPayload): Promise<string> {
  // If agentId is provided and valid, use it
  if (payload.agentId && typeof payload.agentId === "string" && payload.agentId.trim() !== "") {
    return payload.agentId;
  }

  // If no agentId, try to resolve from siteId
  if (!payload.siteId) {
    throw new Error("Cannot resolve agent: both agentId and siteId are missing");
  }

  try {
    const agent = await db.query.agents.findFirst({
      where: eq(agents.siteId, payload.siteId),
    });

    if (!agent) {
      throw new Error(`No agent found for site: ${payload.siteId}`);
    }

    return agent.id;
  } catch (error) {
    throw new Error(
      `Failed to resolve agent for site ${payload.siteId}: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }
}

/**
 * Type-safe wrapper for enqueueJob that handles agentId resolution
 * Maps internal job types to external job queue types
 */
async function safeEnqueueJob(
  type: JobPayload["jobType"],
  payload: JobPayload,
  runAfter?: Date,
): Promise<string> {
  const resolvedAgentId = await resolveAgentId(payload);

  // Map internal job types to external job queue types
  const mappedType = type === "return_request_created" ? "email_followup" : type;

  // Validate that the mapped type is supported by the job queue
  const validJobTypes = [
    "order_status",
    "return_create",
    "return_refund",
    "email_followup",
    "damage_check",
    "email_response",
    "process_knowledge_source",
    "generate_embeddings",
  ] as const;
  if (!validJobTypes.includes(mappedType as any)) {
    throw new Error(`Unsupported job type: ${type}`);
  }

  // Create a properly typed payload for the job queue
  const jobQueuePayload = {
    ...payload,
    agentId: resolvedAgentId,
  };

  return enqueueJob(
    mappedType as Parameters<typeof enqueueJob>[0],
    resolvedAgentId,
    jobQueuePayload,
    runAfter,
  );
}

async function handler(req: NextRequest) {
  try {
    // Parse the request body
    const requestData = await req.json();

    let payload: any;

    // Check if this is a job from the queue system (has jobId) or direct QStash call
    if (requestData.jobId) {
      // This is a job from our job-queue system
      const job = await db.query.jobs.findFirst({
        where: eq(jobs.id, requestData.jobId),
      });

      if (!job) {
        throw new Error(`Job not found: ${requestData.jobId}`);
      }

      // Extract the payload from the database job
      payload = {
        ...(job.payload as any),
        jobType: job.type as any,
      };

      console.log("🔄 Processing database job", {
        jobId: requestData.jobId,
        jobType: payload.jobType,
        stage: payload.stage,
        sessionId: payload.sessionId,
        mode: process.env.NODE_ENV,
      });

      // Mark job as running
      await db.update(jobs).set({ status: "running" }).where(eq(jobs.id, requestData.jobId));
    } else {
      // This is a direct QStash call
      payload = requestData as JobPayload;

      console.log("🔄 Processing QStash job", {
        jobType: payload.jobType,
        stage: payload.stage,
        sessionId: payload.sessionId,
        mode: process.env.NODE_ENV,
      });
    }

    // Get message ID from headers (QStash) or generate one (development)
    const messageId =
      req.headers.get("upstash-message-id") || requestData.jobId || `direct_${uuidv4()}`;

    // Update job status to running
    await JobTracker.updateJobStatus(messageId, "running");

    let result: any = null;
    let errorMessage: string | null = null;

    try {
      // Process the job based on its type
      switch (payload.jobType) {
        case "order_status":
          result = await processOrderStatusJob(payload);
          break;
        case "return_create":
          result = await processReturnJob(payload);
          break;
        case "return_request_created":
          result = await processInitialReturnRequest(payload);
          break;
        case "email_followup":
          result = await processEmailJob(payload);
          break;
        case "damage_check":
          result = await processDamageCheckJob(payload);
          break;
        case "return_refund":
          result = await processReturnRefund(payload);
          break;
        case "email_response":
          result = await processEmailResponseJob(payload);
          break;
        case "process_knowledge_source":
          result = await processKnowledgeSourceJob(payload);
          break;
        case "generate_embeddings":
          result = await processGenerateEmbeddingsJob(payload);
          break;
        case "product_recommendations":
          result = await processProductRecommendationsJob(payload);
          break;
        case "product_text_embed":
          result = await processTextEmbedding(payload as ProductTextEmbedPayload);
          break;
        case "product_image_embed":
          result = await processImageEmbedding(payload as ProductImageEmbedPayload);
          break;
        case "initial_product_sync":
          result = await processInitialProductSync(payload as any);
          break;
        case "process_shopify_webhook":
          result = await processShopifyWebhook(payload as any);
          break;
        default:
          throw new Error(`Unknown job type: ${payload.jobType}`);
      }

      // Mark job as completed in Redis
      await JobTracker.updateJobStatus(messageId, "completed", result);

      console.log("✅ Job completed", {
        messageId,
        jobType: payload.jobType,
        result: result.status || "success",
      });

      return NextResponse.json({
        success: true,
        result,
        message: "Job completed successfully",
      });
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : "Unknown error";
      console.error(`❌ Job failed:`, error);

      // Mark job as failed in Redis
      await JobTracker.updateJobStatus(messageId, "failed", null, errorMessage);

      // Send fallback response for certain job types
      if (payload.jobType === "order_status") {
        await sendFallbackResponse(payload, errorMessage);
      }

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Worker error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 },
    );
  }
}

// In production, verify QStash signature. In development, allow direct calls.
export const POST =
  process.env.NODE_ENV === "production" ? verifySignatureAppRouter(handler) : handler;

/**
 * Process order status check job
 */
async function processOrderStatusJob(payload: JobPayload) {
  const { sessionId, agentId, siteId, orderNumber, orderContext, userInfo } = payload;

  if (!orderNumber) {
    throw new Error("Order number is required for order status check");
  }

  // Create Shopify client
  const shopifyClient = await ShopifyClient.fromSiteId(siteId);

  // Get order by number
  const order = await shopifyClient.getOrderByNumber(orderNumber);

  if (!order) {
    // Let the agent generate a natural "order not found" response
    const resolvedAgentId = await resolveAgentId(payload);
    const agentPrompt = `You are in a CHAT CONVERSATION. The user asked about order ${orderNumber}, but I couldn't find this order. Generate a brief, helpful chat response in Dutch asking them to check the number. Don't use formal email language.`;
    const agentResponse = await processMessage(agentPrompt, resolvedAgentId, sessionId);
    await sendMessageToSession(sessionId, agentResponse.reply);
    return { found: false, orderNumber };
  }

  // Prepare structured order data for the agent
  const orderData = {
    orderNumber: order.name,
    orderDate: new Date(order.createdAt).toLocaleDateString("nl-NL", {
      day: "numeric",
      month: "long",
      year: "numeric",
    }),
    totalPrice: `€${order.totalPrice} ${order.currencyCode}`,
    status: order.displayFulfillmentStatus,
    items: order.lineItems.edges.map((item: any) => ({
      title: item.node.title,
      variant: item.node.variant.title !== "Default Title" ? item.node.variant.title : null,
      quantity: item.node.quantity,
    })),
    fulfillments: order.fulfillments.map((fulfillment: any) => ({
      status: fulfillment.status,
      trackingInfo: fulfillment.trackingInfo.map((tracking: any) => ({
        number: tracking.number,
        url: tracking.url,
        company: tracking.company,
      })),
    })),
    daysSinceOrder: Math.floor(
      (Date.now() - new Date(order.createdAt).getTime()) / (1000 * 60 * 60 * 24),
    ),
  };

  // Create a context-aware prompt for the agent
  let agentPrompt = `You are responding in a CHAT CONVERSATION, not writing an email. Keep it conversational and friendly.

I found the order details for ${orderNumber}. `;

  // Add context about what the user was asking
  if (orderContext) {
    const isAskingAboutContents = [
      "contents",
      "items",
      "products",
      "besteld",
      "wat",
      "inhoud",
    ].some((keyword) => orderContext.toLowerCase().includes(keyword));

    if (isAskingAboutContents) {
      agentPrompt += `The user wants to know WHAT they ordered (the contents/items). `;
    } else {
      agentPrompt += `The user wants to know about the order STATUS and delivery information. `;
    }
  } else {
    agentPrompt += `The user is asking about their order status. `;
  }

  agentPrompt += `Order details: ${JSON.stringify(orderData)}

IMPORTANT CHAT GUIDELINES:
- This is a CHAT conversation, not an email
- Be conversational and natural in Dutch
- Don't use formal email language or signatures
- Don't say "Met vriendelijke groet" or similar email closings
- Use "je" instead of "u"
- Keep it friendly but concise
- Focus on answering their specific question
- If status is unfulfilled, briefly explain and give timeframe

Generate a helpful, natural chat response.`;

  // Let the agent generate the response
  const resolvedAgentId = await resolveAgentId(payload);
  const agentResponse = await processMessage(agentPrompt, resolvedAgentId, sessionId);
  await sendMessageToSession(sessionId, agentResponse.reply);

  return {
    found: true,
    orderNumber,
    orderContext,
    status: order.displayFulfillmentStatus,
    fulfillments: order.fulfillments,
    message: agentResponse.reply,
  };
}

/**
 * Process return creation job - Multi-stage workflow
 */
async function processReturnJob(payload: JobPayload) {
  const {
    sessionId,
    agentId,
    siteId,
    orderNumber,
    email,
    phone,
    userInfo,
    stage = "initial",
    returnRequestId,
  } = payload;

  try {
    switch (stage) {
      case "initial":
        return await processInitialReturnRequest(payload);
      case "review_decision":
        return await processReviewDecision(payload);
      case "create_shopify_return":
        return await createShopifyReturn(payload);
      case "send_confirmation":
        return await sendReturnConfirmation(payload);
      default:
        throw new Error(`Unknown return stage: ${stage}`);
    }
  } catch (error) {
    console.error(`Return job failed at stage ${stage}:`, error);
    throw error;
  }
}

/**
 * Stage 1: Initial return request processing
 */
async function processInitialReturnRequest(payload: JobPayload): Promise<any> {
  const { sessionId, agentId, siteId, orderNumber, email, phone, userInfo } = payload;

  if (!orderNumber) {
    throw new Error("Order number is required for return processing");
  }

  // Resolve agent ID once at the start
  const resolvedAgentId = await resolveAgentId(payload);

  // 1. Get agent details for personalization
  const agent = await db.query.agents.findFirst({
    where: eq(agents.id, resolvedAgentId),
  });
  const agentName = agent?.name || "AI Agent";

  // 2. Get order details from Shopify
  const shopifyClient = await ShopifyClient.fromSiteId(siteId);
  const order = await shopifyClient.getOrderByNumber(orderNumber);

  if (!order) {
    // Let the agent generate a natural "order not found for return" response
    const agentPrompt = `You are in a CHAT CONVERSATION. The user wants to return order ${orderNumber}, but I couldn't find this order. Generate a brief, helpful chat response in Dutch. Don't use formal email language.`;
    const agentResponse = await processMessage(agentPrompt, resolvedAgentId, sessionId);
    await sendMessageToSession(sessionId, agentResponse.reply);
    return { found: false, orderNumber };
  }

  // 2. Check for existing return requests for this order
  const existingReturns = await db
    .select()
    .from(returnRequests)
    .where(and(eq(returnRequests.siteId, siteId), eq(returnRequests.orderNumber, orderNumber)));

  if (existingReturns.length > 0) {
    const existingReturn = existingReturns[0];

    // If there's a denied return, inform customer
    if (existingReturn.status === "denied") {
      const agentPrompt = `You are in a CHAT CONVERSATION. The user wants to return order ${orderNumber}, but there's already a DENIED return request for this order (denied on ${existingReturn.reviewedAt ? new Date(existingReturn.reviewedAt).toLocaleDateString("nl-NL") : "recent date"}). Generate a helpful but firm chat response in Dutch explaining this situation. Don't use formal email language.`;
      const agentResponse = await processMessage(agentPrompt, resolvedAgentId, sessionId);
      await sendMessageToSession(sessionId, agentResponse.reply);
      return { found: true, orderNumber, status: "denied_previously" };
    }

    // If there's already a pending/approved return, inform customer
    const statusText =
      existingReturn.status === "pending"
        ? "wordt nog beoordeeld"
        : existingReturn.status === "approved"
          ? "al goedgekeurd"
          : existingReturn.status === "processing"
            ? "wordt verwerkt"
            : existingReturn.status === "completed"
              ? "al voltooid"
              : "in behandeling";

    const agentPrompt = `You are in a CHAT CONVERSATION. The user wants to return order ${orderNumber}, but there's already an existing return request for this order that ${statusText}. Generate a helpful chat response in Dutch explaining the current status. Don't use formal email language.`;
    const agentResponse = await processMessage(agentPrompt, resolvedAgentId, sessionId);
    await sendMessageToSession(sessionId, agentResponse.reply);
    return { found: true, orderNumber, status: "already_exists" };
  }

  // 3. Load shop policy config to check auto-approval settings
  const shopConfig = await loadShopPolicyConfig(siteId);

  // Check if auto-approval under €50 is enabled and order qualifies
  const orderValue = parseFloat(order.totalPrice);
  const shouldAutoApprove =
    shopConfig &&
    shopConfig.toolPolicies?.createReturn?.includes("auto_approve_under_50") &&
    orderValue < 50;

  // 4. Generate AI summary of the conversation context
  const chatContext = await generateChatSummary(sessionId, order);

  // 4.5. Analyze the return with AI for staff review
  let analysisData = null;
  try {
    const messages = await db.query.chatMessages.findMany({
      where: eq(chatMessages.sessionId, sessionId),
      orderBy: [chatMessages.createdAt],
      limit: 20,
    });

    const chatHistory = messages.map((msg) => ({
      role: msg.role as "user" | "assistant" | "system",
      content: msg.content,
      createdAt: msg.createdAt,
    }));

    analysisData = await analyzeReturn(
      orderNumber,
      orderValue,
      chatHistory,
      email || "",
      userInfo?.reason || userInfo?.returnReason || "No reason provided",
    );

    console.log("✅ Return analysis completed:", analysisData.recommendation);
  } catch (error) {
    console.error("❌ Return analysis failed:", error);
    // Continue without analysis - this shouldn't block return creation
  }

  // 5. Create return request record
  const initialStatus = shouldAutoApprove ? "approved" : "pending";
  const returnRequest = await db
    .insert(returnRequests)
    .values({
      siteId,
      sessionId,
      orderNumber,
      orderShopifyId: order.id,
      customerEmail: email,
      customerPhone: phone,
      reason: userInfo?.reason || userInfo?.returnReason || "Size/fit issue (details in chat)",
      status: initialStatus,
      requestedItems: order.lineItems.edges.map((item: any) => ({
        lineItemId: item.node.id || `${item.node.title}-${item.node.variant.title}`,
        title: item.node.title,
        variant: item.node.variant.title,
        quantity: item.node.quantity,
        reason: userInfo?.reason || userInfo?.returnReason || "Size/fit issue",
      })),
      chatContext,
      analysisData: analysisData as any,
      staffNotes: shouldAutoApprove ? "Automatisch goedgekeurd - onder €50" : null,
      reviewedAt: shouldAutoApprove ? new Date() : null,
    })
    .returning({ id: returnRequests.id });

  const returnRequestId = returnRequest[0].id;

  // 6. Let the agent generate a natural confirmation response
  const orderData = {
    orderNumber: order.name,
    totalPrice: `€${order.totalPrice} ${order.currencyCode}`,
    items: order.lineItems.edges.map((item: any) => item.node.title).join(", "),
    returnReason: userInfo?.reason || userInfo?.returnReason || "size/fit issue",
    autoApproved: shouldAutoApprove,
  };

  const agentPrompt = shouldAutoApprove
    ? `You are responding in a CHAT CONVERSATION, not writing an email.

I successfully created and AUTOMATICALLY APPROVED a return request for the user because the order is under €50 and the shop has auto-approval enabled. Order details: ${JSON.stringify(orderData)}

CHAT GUIDELINES:
- This is a CHAT conversation, not an email
- Be conversational and natural in Dutch
- Don't use formal email language or signatures
- Use "je" instead of "u"
- Keep it brief and reassuring

Generate a natural confirmation that:
- Confirms the return is APPROVED immediately
- Mentions they'll get an email with return instructions
- Sounds like a friendly chat response`
    : `You are responding in a CHAT CONVERSATION, not writing an email.

I successfully created a return request for the user. Order details: ${JSON.stringify(orderData)}

CHAT GUIDELINES:
- This is a CHAT conversation, not an email
- Be conversational and natural in Dutch
- Don't use formal email language or signatures
- Use "je" instead of "u"
- Keep it brief and reassuring

Generate a natural confirmation that:
- Confirms the return request is received
- Mentions they'll get email within 24 hours
- Sounds like a friendly chat response`;

  const agentResponse = await processMessage(agentPrompt, resolvedAgentId, sessionId);
  await sendMessageToSession(sessionId, agentResponse.reply);

  // 6. Handle email notifications based on approval status
  if (email) {
    // Get shop name from database
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, payload.siteId),
    });

    if (shouldAutoApprove) {
      // Send immediate approval email
      await scheduleReturnEmail(returnRequestId, email, "return_approved", {
        orderNumber,
        reason: userInfo?.reason || "Niet opgegeven",
        customerName: userInfo?.name || "klant",
        shopName: site?.name || "NousuAI",
        agentName,
        items: order.lineItems.edges.map((item) => item.node.title).join(", "),
        autoApproved: true,
      });

      // Enqueue Shopify return creation job
      await enqueueQStashJob({
        jobType: "return_create",
        sessionId,
        agentId: resolvedAgentId,
        siteId,
        returnRequestId,
        stage: "create_shopify_return",
      });
    } else {
      // Send confirmation email for manual review
      await scheduleReturnEmail(returnRequestId, email, "return_received", {
        orderNumber,
        reason: userInfo?.reason || "Niet opgegeven",
        customerName: userInfo?.name || "klant",
        shopName: site?.name || "NousuAI",
        agentName,
        items: order.lineItems.edges.map((item) => item.node.title).join(", "),
        chatSummary: chatContext,
      });
    }
  }

  // 7. Schedule notification to staff (only if manual review needed)
  if (!shouldAutoApprove) {
    await scheduleStaffNotification(returnRequestId, {
      orderNumber,
      customerEmail: email,
      orderValue: `€${order.totalPrice} ${order.currencyCode}`,
      reason: userInfo?.reason || "Niet opgegeven",
      chatContext,
      orderDate: order.createdAt,
      agentName,
      siteId,
    });

    // Schedule reminder email (12 hours later) in case no staff response
    const reminderTime = new Date(Date.now() + 12 * 60 * 60 * 1000);
    await safeEnqueueJob(
      "email_followup",
      {
        ...payload,
        returnRequestId,
        stage: "staff_reminder",
        emailType: "staff_reminder",
      },
      reminderTime,
    );
  }

  return {
    status: "return_request_created",
    returnRequestId,
    orderNumber,
    pendingReview: true,
  };
}

/**
 * Generate AI summary of the chat conversation for staff review
 */
async function generateChatSummary(sessionId: string, order: any): Promise<string> {
  // Get recent chat messages for this session
  const messages = await db.query.chatMessages.findMany({
    where: eq(chatMessages.sessionId, sessionId),
    orderBy: [chatMessages.createdAt],
    limit: 20,
  });

  // Create a conversational summary
  const conversation = messages
    .filter((msg) => msg.role !== "system")
    .map((msg) => `${msg.role === "user" ? "Klant" : "Agent"}: ${msg.content}`)
    .join("\n");

  return `Bestelling: ${order.name} (€${order.totalPrice} ${order.currencyCode})
Besteldatum: ${new Date(order.createdAt).toLocaleDateString("nl-NL")}
Producten: ${order.lineItems.edges.map((item: any) => `${item.node.title} (${item.node.quantity}x)`).join(", ")}

Gespreksamenvatting:
${conversation}

Automatisch gegenereerd op ${new Date().toLocaleString("nl-NL")}`;
}

/**
 * Schedule an email notification for returns
 */
async function scheduleReturnEmail(
  returnRequestId: string,
  to: string,
  templateType: string,
  variables: Record<string, any>,
  scheduledFor?: Date,
) {
  // Use shop name for customer emails
  const shopName = variables.shopName || "Webshop";
  const fromEmail = `${shopName} <<EMAIL>>`;

  // Use the new email service
  await scheduleEmail(to, templateType, variables, {
    returnRequestId,
    scheduledFor,
    from: fromEmail,
  });

  console.log(`📧 Email scheduled: ${templateType} to ${to} from ${fromEmail}`);
}

/**
 * Schedule staff notification for new return request
 */
async function scheduleStaffNotification(returnRequestId: string, details: Record<string, any>) {
  try {
    // Get the site to find the organization
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, details.siteId),
    });

    if (!site || !site.orgId) {
      console.error(`Site ${details.siteId} not found or has no organization`);
      return;
    }

    // Get all users in the organization with appropriate roles
    const orgUsers = await db
      .select({
        email: users.email,
        name: users.name,
        role: orgMembers.role,
      })
      .from(orgMembers)
      .innerJoin(users, eq(orgMembers.userId, users.id))
      .where(
        and(
          eq(orgMembers.orgId, site.orgId),
          // Only notify owners and admins, not agents
          inArray(orgMembers.role, ["owner", "admin"]),
        ),
      );

    if (orgUsers.length === 0) {
      console.error(`No staff users found for organization ${site.orgId}`);
      return;
    }

    // Generate chat summary for staff
    const chatSummary = details.chatContext
      ? `Laatste berichten uit het gesprek:\n${details.chatContext.substring(0, 300)}...`
      : "Geen chat geschiedenis beschikbaar";

    // Send notification to each staff member
    for (const user of orgUsers) {
      await scheduleEmail(
        user.email,
        "staff_return_notification",
        {
          orderNumber: details.orderNumber,
          customerEmail: details.customerEmail || "Onbekend",
          orderValue: details.orderValue,
          returnReason: details.reason,
          chatSummary,
          orderDate: details.orderDate,
          returnRequestId,
          dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/${details.siteId}/returns?filter=pending`,
          agentName: details.agentName || "AI Agent",
          staffName: user.name || user.email.split("@")[0],
        },
        {
          returnRequestId,
          from: `${details.agentName || "AI Agent"} <<EMAIL>>`,
        },
      );
    }

    console.log(
      `📧 Staff notifications scheduled for return ${returnRequestId} to ${orgUsers.length} users`,
    );
  } catch (error) {
    console.error("Failed to schedule staff notification:", error);
    // Don't throw error - staff notification failure shouldn't break return creation
  }
}

/**
 * Generate AI email content based on template type and variables
 */
async function generateEmailContent(templateType: string, variables: Record<string, any>) {
  // In production, this would use OpenAI to generate personalized emails
  // For now, we'll use templates

  switch (templateType) {
    case "return_received":
      return {
        subject: `Retour ontvangen - Bestelling ${variables.orderNumber}`,
        content: `Hoi ${variables.customerName},

Bedankt voor je retourverzoek voor bestelling ${variables.orderNumber}.

Onze klantenservice bekijkt je aanvraag en neemt binnen 24 uur contact met je op via e-mail.

Je wilde de volgende items retourneren:
${variables.items}

Heb je nog vragen? Antwoord dan op deze e-mail.

Met vriendelijke groet,
Het ${variables.shopName || "webshop"} team`,
      };

    case "return_approved":
      return {
        subject: `Retour goedgekeurd - Bestelling ${variables.orderNumber}`,
        content: `Hoi ${variables.customerName},

Goed nieuws! Je retour voor bestelling ${variables.orderNumber} is goedgekeurd.

${variables.returnInstructions || "Je ontvangt binnenkort een retourlabel op je e-mailadres."}

${variables.additionalInstructions || ""}

Met vriendelijke groet,
Het ${variables.shopName || "webshop"} team`,
      };

    default:
      return {
        subject: `Update over je retour - Bestelling ${variables.orderNumber}`,
        content: `Hoi ${variables.customerName},

Er is een update over je retourverzoek voor bestelling ${variables.orderNumber}.

${variables.message || "Ons team neemt binnenkort contact met je op."}

Met vriendelijke groet,
Het ${variables.shopName || "webshop"} team`,
      };
  }
}

/**
 * Process email follow-up job
 */
async function processEmailJob(payload: JobPayload) {
  const { email, sessionId, agentId, emailType, emailVariables } = payload;

  // Process pending emails in the queue
  const result = await processPendingEmails();

  // If specific email requested, schedule it
  if (emailType && emailVariables && email) {
    await scheduleEmail(email, emailType, emailVariables, { sessionId });
  }

  if (!agentId) {
    throw new Error("Agent ID is missing");
  }

  // Let the agent generate a natural email confirmation message
  const agentPrompt = `You are in a CHAT CONVERSATION. I've processed email notifications (${result.processed} sent successfully). Generate a brief, natural chat response in Dutch confirming this. Don't use formal email language.`;
  const agentResponse = await processMessage(agentPrompt, agentId, sessionId);
  await sendMessageToSession(sessionId, agentResponse.reply);

  return {
    status: "email_processed",
    processed: result.processed,
    failed: result.failed,
    email,
  };
}

/**
 * Process email response job - generates AI response to inbound email
 */
async function processEmailResponseJob(payload: JobPayload) {
  const {
    sessionId,
    siteId,
    agentId,
    customerEmail,
    inboundSubject,
    inboundContent,
    messageId,
    emailHistory,
    policyConfig,
  } = payload;

  if (!agentId) {
    throw new Error("Agent ID is required for email response processing");
  }

  if (!customerEmail) {
    throw new Error("Customer email is required for email response processing");
  }

  if (!inboundContent) {
    throw new Error("Email content is required for email response processing");
  }

  if (!siteId) {
    throw new Error("Site ID is required for email response processing");
  }

  try {
    // Get site information
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, siteId),
    });

    if (!site) {
      throw new Error(`Site not found: ${siteId}`);
    }

    // Get agent information
    const agent = await db.query.agents.findFirst({
      where: eq(agents.id, agentId),
    });

    const agentName = agent?.name || "Klantenservice";

    // Build context for AI agent
    const emailContext =
      emailHistory && emailHistory.length > 0
        ? emailHistory
            .map((h: any) => `${h.direction === "inbound" ? "Klant" : "AI"}: ${h.content}`)
            .join("\n\n")
        : "Geen eerdere email geschiedenis";

    const agentPrompt = `
Je bent een Nederlandse klantenservice AI agent voor ${site.name}.

${policyConfig?.personalityPrompt || "Wees vriendelijk, behulpzaam en professioneel. Gebruik informele toon (je/jij)."}

Email geschiedenis:
${emailContext}

Nieuw bericht van klant (${customerEmail}):
Onderwerp: ${inboundSubject}
Inhoud: ${inboundContent}

Genereer een professioneel Nederlands email antwoord. Wees behulpzaam en probeer de vraag direct te beantwoorden.
`;

    // Process the message to see what the agent wants to do
    const aiResponse = await processMessage(agentPrompt, agentId, sessionId, "email");

    // Check if this looks like a processing/intermediate message
    if (
      aiResponse.reply.includes("opzoeken") ||
      aiResponse.reply.includes("moment geduld") ||
      aiResponse.reply.includes("wachten") ||
      aiResponse.reply === "PROCESSING_PLACEHOLDER_DO_NOT_SEND"
    ) {
      let toolResponse = null;
      let attempts = 0;
      const maxAttempts = 12;

      while (!toolResponse && attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 2500));
        attempts++;

        const latestMessages = await db.query.chatMessages.findMany({
          where: eq(chatMessages.sessionId, sessionId),
          orderBy: [desc(chatMessages.createdAt)],
          limit: 10,
        });

        toolResponse = latestMessages.find(
          (m) =>
            m.role === "assistant" &&
            m.content !== "PROCESSING_PLACEHOLDER_DO_NOT_SEND" &&
            !m.content?.includes("opzoeken") &&
            !m.content?.includes("moment geduld") &&
            !m.content?.includes("wachten") &&
            m.createdAt &&
            m.createdAt > new Date(Date.now() - 60000),
        );

        if (toolResponse) {
          aiResponse.reply = toolResponse.content || aiResponse.reply;
          break;
        }
      }

      if (!toolResponse) {
        aiResponse.reply =
          "Bedankt voor je bericht. Ik heb geprobeerd je bestelling op te zoeken, maar er lijkt een technisch probleem te zijn. Kun je je bestelnummer nog eens controleren? Dan help ik je graag verder.";
      }
    }

    const emailSubject = inboundSubject?.startsWith("Re:")
      ? inboundSubject
      : `Re: ${inboundSubject || "Uw bericht"}`;

    // Generate a proper email response (not chat response)
    const emailBodyPrompt = `
Je bent ${agentName}, een echte klantenservice medewerker van ${site.name}. Je schrijft emails zoals je normaal zou doen - natuurlijk, persoonlijk en behulpzaam.

${policyConfig?.personalityPrompt || "Wees vriendelijk, behulpzaam en persoonlijk. Gebruik informele toon (je/jij) en schrijf zoals een echte medewerker."}

Email geschiedenis:
${emailContext}

Nieuw bericht van klant (${customerEmail}):
Onderwerp: ${inboundSubject}
Inhoud: ${inboundContent}

Je hebt de bestelling opgezocht en de volgende informatie gevonden:
${aiResponse.reply}

BELANGRIJKE INSTRUCTIE VOOR PERSONALISATIE:
- Probeer de naam van de klant te herkennen uit hun email (kijk naar de afsluiting zoals "Groetjes, Sarah" of "Met vriendelijke groet, Peter")
- Als je een naam kunt herkennen, gebruik dan een persoonlijke begroeting zoals "Hoi Sarah," of "Beste Peter,"
- Als je geen duidelijke naam kunt vinden, gebruik dan "Hoi," of "Beste klant,"

Schrijf nu een COMPLETE email (inclusief begroeting) in Nederlands. Schrijf zoals je als echte medewerker zou doen:
- Begin met een persoonlijke begroeting (probeer de naam uit de email te halen)
- Wees oprecht en persoonlijk in je toon
- Gebruik natuurlijke overgangen en zinnen
- Toon empathie en begrip waar dat passend is  
- Geef de informatie op een menselijke, toegankelijke manier
- Gebruik een vriendelijke, natuurlijke schrijfstijl
- Je mag gerust wat persoonlijkheid en warmte tonen
- Maak het persoonlijk en niet template-achtig
- Eindig op een natuurlijke, behulpzame manier
- Onderteken met je naam: ${agentName}

Schrijf de COMPLETE email content (inclusief begroeting en ondertekening).
`;

    const emailBodyModel = new (await import("@langchain/openai")).ChatOpenAI({
      model: "gpt-4o",
      temperature: 0.3,
    });

    const emailBodyResponse = await emailBodyModel.invoke(emailBodyPrompt);
    const cleanEmailBody = emailBodyResponse.content.toString();

    // Create HTML email content with the proper email body
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; max-width: 600px; margin: 0 auto;">
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
          ${cleanEmailBody
            .split("\n")
            .filter((line: string) => line.trim())
            .map((paragraph: string) => `<p>${paragraph.trim()}</p>`)
            .join("")}
        </div>
      </div>
    `;

    // Store outbound email in history

    await db.insert(emailHistoryTable).values({
      siteId,
      sessionId,
      customerEmail,
      direction: "outbound",
      subject: emailSubject,
      content: aiResponse.reply,
      replyToId: messageId,
      aiGenerated: true,
    });

    // Send the email with proper threading headers
    const emailResult = await sendEmail({
      to: customerEmail,
      from: `${agentName} - ${site.name} <<EMAIL>>`,
      subject: emailSubject,
      html: emailHtml,
      replyTo: getSiteInboundEmailByName(site.name),
      inReplyTo: messageId, // Reply to the original message
      references: messageId, // Reference chain for threading
    });

    if (!emailResult.success) {
      throw new Error(`Failed to send email: ${emailResult.error}`);
    }

    // Add assistant message to chat session
    await db.insert(chatMessages).values({
      sessionId,
      role: "assistant",
      content: `Email verzonden naar ${customerEmail}: ${aiResponse.reply}`,
    });

    return {
      status: "email_response_sent",
      customerEmail,
      subject: emailSubject,
      messageId: emailResult.messageId,
    };
  } catch (error) {
    console.error("❌ Error processing email response:", error);
    throw error;
  }
}

/**
 * Send a message back to the chat session
 */
async function sendMessageToSession(sessionId: string, message: string) {
  const messageId = uuidv4();
  const traceId = `worker_${uuidv4()}`;

  await db.insert(chatMessages).values({
    id: messageId,
    sessionId,
    role: "assistant",
    content: message,
    traceId,
  });
}

/**
 * Send a fallback response when a job fails
 */
async function sendFallbackResponse(payload: JobPayload, error: string) {
  const { sessionId, agentId } = payload;

  // Let the agent generate a natural error response
  const agentPrompt = `You are in a CHAT CONVERSATION. There was a technical error: ${error}. Generate a brief, apologetic chat response in Dutch. Don't use formal email language.`;
  try {
    if (!agentId) {
      throw new Error("Agent ID is missing");
    }

    const agentResponse = await processMessage(agentPrompt, agentId, sessionId);
    await sendMessageToSession(sessionId, agentResponse.reply);
  } catch (agentError) {
    // If the agent also fails, fall back to a simple message
    const message = "Sorry, er ging iets mis. Probeer het later opnieuw!";
    await sendMessageToSession(sessionId, message);
  }
}

/**
 * Stage 2: Process staff review decision (placeholder)
 */
async function processReviewDecision(payload: JobPayload): Promise<any> {
  // This would be called when staff approves/denies the return
  const { returnRequestId, decision, staffNotes } = payload;

  console.log(`Processing review decision for return ${returnRequestId}: ${decision}`);

  return { status: "review_processed", decision };
}

/**
 * Stage 3: Create actual Shopify return
 */
async function createShopifyReturn(payload: JobPayload): Promise<any> {
  const { returnRequestId, siteId } = payload;

  console.log(`Creating Shopify return for ${returnRequestId}`);

  try {
    // Get the return request from database
    const returnRequest = await db.query.returnRequests.findFirst({
      where: eq(returnRequests.id, returnRequestId!),
    });

    if (!returnRequest) {
      throw new Error(`Return request not found: ${returnRequestId}`);
    }

    // Create Shopify client
    const shopifyClient = await ShopifyClient.fromSiteId(siteId);

    // Get order with fulfillment details
    const order = await shopifyClient.getOrderWithFulfillments(returnRequest.orderNumber);

    if (!order) {
      throw new Error(`Order not found: ${returnRequest.orderNumber}`);
    }

    // Build return line items from fulfillment line items
    const returnLineItems: Array<{
      fulfillmentLineItemId: string;
      quantity: number;
      returnReason?: string;
      returnReasonNote?: string;
    }> = [];

    // Log order fulfillment status for debugging
    console.log(`Order ${order.name} fulfillment status: ${order.displayFulfillmentStatus}`);
    console.log(`Order has ${order.fulfillments?.length || 0} fulfillments`);
    console.log(`Order fulfillments structure:`, JSON.stringify(order.fulfillments, null, 2));

    // For unfulfilled orders, we'll handle them with direct refunds below

    // Get all fulfillment line items for the order (if any fulfillments exist)
    if (order.fulfillments && order.fulfillments.length > 0) {
      for (const fulfillment of order.fulfillments) {
        console.log(`Processing fulfillment ${fulfillment.id} with status: ${fulfillment.status}`);
        console.log(
          `Fulfillment has ${fulfillment.fulfillmentLineItems?.edges?.length || 0} line items`,
        );

        // Process fulfillments that are fulfilled or partially fulfilled
        // Shopify uses "SUCCESS" for fulfilled orders, not "fulfilled"
        if (!["SUCCESS", "fulfilled", "partial"].includes(fulfillment.status)) {
          console.log(`Skipping fulfillment with status: ${fulfillment.status}`);
          continue;
        }

        if (
          !fulfillment.fulfillmentLineItems?.edges ||
          fulfillment.fulfillmentLineItems.edges.length === 0
        ) {
          console.log(`No fulfillment line items found for fulfillment ${fulfillment.id}`);
          continue;
        }

        for (const fulfillmentLineItemEdge of fulfillment.fulfillmentLineItems.edges) {
          const fulfillmentLineItem = fulfillmentLineItemEdge.node;
          console.log(`Processing fulfillment line item:`, {
            id: fulfillmentLineItem.id,
            quantity: fulfillmentLineItem.quantity,
            lineItemTitle: fulfillmentLineItem.lineItem?.title,
          });

          // Ensure quantity is valid (must be > 0 and <= original quantity)
          const returnQuantity = fulfillmentLineItem.quantity;

          if (returnQuantity > 0) {
            returnLineItems.push({
              fulfillmentLineItemId: fulfillmentLineItem.id,
              quantity: returnQuantity,
              returnReason: "OTHER",
              returnReasonNote:
                (returnRequest.reason || "Customer requested return") + " - Nousu AI",
            });
            console.log(`Added return line item with quantity ${returnQuantity}`);
          } else {
            console.log(`Skipping item with quantity ${returnQuantity}`);
          }
        }
      }
    }

    // If no fulfillments are available, try to create return line items from order line items
    if (returnLineItems.length === 0 && order.lineItems.edges.length > 0) {
      console.log("No fulfillments found, attempting to create return from order line items");

      // For unfulfilled orders, we'll create a simple refund instead of a return
      console.log("Creating direct refund for unfulfilled order");

      try {
        const refundLineItems = order.lineItems.edges.map((lineItemEdge) => ({
          lineItemId: lineItemEdge.node.id,
          quantity: lineItemEdge.node.quantity,
          restockType: "NO_RESTOCK" as const, // Don't restock since items weren't shipped
        }));

        const refundResult = await shopifyClient.createRefund({
          orderId: order.id,
          note: `Refund for return request: ${returnRequest.reason || "Customer requested return"} - Processed by Nousu AI (unfulfilled order)`,
          notify: true,
          refundLineItems,
        });

        if (
          refundResult.refundCreate.userErrors &&
          refundResult.refundCreate.userErrors.length > 0
        ) {
          throw new Error(
            `Shopify refund creation failed: ${refundResult.refundCreate.userErrors.map((e) => e.message).join(", ")}`,
          );
        }

        if (!refundResult.refundCreate.refund) {
          throw new Error("Shopify refund creation failed: No refund object returned");
        }

        // Update the return request with completed status
        await db
          .update(returnRequests)
          .set({
            status: "completed",
            staffNotes: `Direct refund created for unfulfilled order. Refund ID: ${refundResult.refundCreate.refund.id}`,
            updatedAt: new Date(),
          })
          .where(eq(returnRequests.id, returnRequestId!));

        console.log(
          `✅ Direct refund created for unfulfilled order: ${refundResult.refundCreate.refund.id}`,
        );

        return {
          status: "direct_refund_created",
          refundId: refundResult.refundCreate.refund.id,
          orderNumber: returnRequest.orderNumber,
        };
      } catch (refundError) {
        console.error("Direct refund creation failed:", refundError);
        throw new Error(
          `Cannot process return: Order not fulfilled and direct refund failed: ${refundError instanceof Error ? refundError.message : "Unknown error"}`,
        );
      }
    }

    if (returnLineItems.length === 0) {
      throw new Error("No returnable items found - order may not be fulfilled yet");
    }

    // Create the return in Shopify
    const result = await shopifyClient.createReturn({
      orderId: order.id,
      returnLineItems,
    });

    // Check for errors
    if (result.returnCreate.userErrors && result.returnCreate.userErrors.length > 0) {
      throw new Error(
        `Shopify return creation failed: ${result.returnCreate.userErrors.map((e) => e.message).join(", ")}`,
      );
    }

    if (!result.returnCreate.return) {
      throw new Error("Shopify return creation failed: No return object returned");
    }

    // Update the return request with Shopify return ID
    await db
      .update(returnRequests)
      .set({
        shopifyReturnId: result.returnCreate.return.id,
        status: "processing",
        updatedAt: new Date(),
      })
      .where(eq(returnRequests.id, returnRequestId!));

    console.log(
      `✅ Shopify return created: ${result.returnCreate.return.id} - Waiting for items to be received before processing refund`,
    );

    return {
      status: "return_created",
      returnId: result.returnCreate.return.id,
      orderNumber: returnRequest.orderNumber,
    };
  } catch (error) {
    console.error("❌ Shopify return creation failed:", error);

    // Update return request with error
    await db
      .update(returnRequests)
      .set({
        status: "denied",
        staffNotes: `Return creation failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        updatedAt: new Date(),
      })
      .where(eq(returnRequests.id, returnRequestId!));

    throw error;
  }
}

/**
 * Process refund after items have been received and confirmed by staff
 */
async function processReturnRefund(payload: JobPayload): Promise<any> {
  const { returnRequestId, siteId } = payload;

  console.log(`Processing refund for return ${returnRequestId} after items received`);

  try {
    // Get the return request from database
    const returnRequest = await db.query.returnRequests.findFirst({
      where: eq(returnRequests.id, returnRequestId!),
    });

    if (!returnRequest) {
      throw new Error(`Return request not found: ${returnRequestId}`);
    }

    if (returnRequest.status !== "items_received") {
      throw new Error(
        `Return request status must be 'items_received' to process refund, current status: ${returnRequest.status}`,
      );
    }

    // Create Shopify client
    const shopifyClient = await ShopifyClient.fromSiteId(siteId);

    // Get order with fulfillment details
    const order = await shopifyClient.getOrderWithFulfillments(returnRequest.orderNumber);

    if (!order) {
      throw new Error(`Order not found: ${returnRequest.orderNumber}`);
    }

    // Build return line items from the existing return request
    const returnLineItems: Array<{
      fulfillmentLineItemId: string;
      quantity: number;
      returnReason?: string;
      returnReasonNote?: string;
    }> = [];

    // Get all fulfillment line items for the order (if any fulfillments exist)
    if (order.fulfillments && order.fulfillments.length > 0) {
      for (const fulfillment of order.fulfillments) {
        console.log(`Processing fulfillment ${fulfillment.id} for refund`);

        if (!["SUCCESS", "fulfilled", "partial"].includes(fulfillment.status)) {
          console.log(`Skipping fulfillment with status: ${fulfillment.status}`);
          continue;
        }

        if (
          !fulfillment.fulfillmentLineItems?.edges ||
          fulfillment.fulfillmentLineItems.edges.length === 0
        ) {
          console.log(`No fulfillment line items found for fulfillment ${fulfillment.id}`);
          continue;
        }

        for (const fulfillmentLineItemEdge of fulfillment.fulfillmentLineItems.edges) {
          const fulfillmentLineItem = fulfillmentLineItemEdge.node;
          const returnQuantity = fulfillmentLineItem.quantity;

          if (returnQuantity > 0) {
            returnLineItems.push({
              fulfillmentLineItemId: fulfillmentLineItem.id,
              quantity: returnQuantity,
              returnReason: "OTHER",
              returnReasonNote:
                (returnRequest.reason || "Klant vraagt om retour") +
                " - Items ontvangen en verwerkt door Nousu AI",
            });
          }
        }
      }
    }

    if (returnLineItems.length === 0) {
      throw new Error("No returnable items found for refund processing");
    }

    // Map fulfillment line items to order line items for refund
    const refundLineItems = returnLineItems
      .map((returnItem) => {
        // Find the corresponding line item from the fulfillment
        for (const fulfillment of order.fulfillments) {
          for (const fulfillmentLineItemEdge of fulfillment.fulfillmentLineItems.edges) {
            const fulfillmentLineItem = fulfillmentLineItemEdge.node;
            if (fulfillmentLineItem.id === returnItem.fulfillmentLineItemId) {
              return {
                lineItemId: fulfillmentLineItem.lineItem.id,
                quantity: returnItem.quantity,
                restockType: "RETURN" as const,
              };
            }
          }
        }
        return null;
      })
      .filter(Boolean) as Array<{
      lineItemId: string;
      quantity: number;
      restockType: "RETURN" | "NO_RESTOCK";
    }>;

    // Get the primary location for restocking
    const primaryLocation = await shopifyClient.getPrimaryLocation();
    if (!primaryLocation) {
      console.warn("No primary location found, using NO_RESTOCK for refund");
      // If no location is available, don't restock the items
      refundLineItems.forEach((item) => {
        item.restockType = "NO_RESTOCK" as const;
      });
    }

    const refundResult = await shopifyClient.createRefund({
      orderId: order.id,
      note: `Terugbetaling voor retour: ${returnRequest.reason || "Customer requested return"} - Items bekeken en verwerkt door Nousu AI`,
      notify: true,
      locationId: primaryLocation?.id,
      refundLineItems,
    });

    if (refundResult.refundCreate.userErrors && refundResult.refundCreate.userErrors.length > 0) {
      throw new Error(
        `Shopify refund creation failed: ${refundResult.refundCreate.userErrors.map((e) => e.message).join(", ")}`,
      );
    }

    if (!refundResult.refundCreate.refund) {
      throw new Error("Shopify refund creation failed: No refund object returned");
    }

    console.log(
      `✅ Refund created: ${refundResult.refundCreate.refund.id} for ${refundResult.refundCreate.refund.totalRefunded.amount} ${refundResult.refundCreate.refund.totalRefunded.currencyCode}`,
    );

    // Update return request with refund completion
    await db
      .update(returnRequests)
      .set({
        status: "completed",
        staffNotes: `Items received and refund processed. Refund ID: ${refundResult.refundCreate.refund.id}`,
        updatedAt: new Date(),
      })
      .where(eq(returnRequests.id, returnRequestId!));

    // Get agent and site info for email
    const resolvedAgentId = await resolveAgentId(payload);
    const agent = await db.query.agents.findFirst({
      where: eq(agents.id, resolvedAgentId),
    });
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, siteId),
    });
    const agentName = agent?.name || "AI Agent";
    const shopName = site?.name || "Webshop";

    // Schedule completion email to customer
    await scheduleReturnEmail(returnRequestId!, returnRequest.customerEmail!, "return_completed", {
      orderNumber: returnRequest.orderNumber,
      refundAmount: `${refundResult.refundCreate.refund.totalRefunded.amount} ${refundResult.refundCreate.refund.totalRefunded.currencyCode}`,
      customerName: "klant", // Could be improved with actual customer name
      agentName,
      shopName,
    });

    return {
      status: "refund_completed",
      refundId: refundResult.refundCreate.refund.id,
      orderNumber: returnRequest.orderNumber,
      refundAmount: refundResult.refundCreate.refund.totalRefunded,
    };
  } catch (error) {
    console.error("❌ Refund processing failed:", error);

    // Update return request with error status
    await db
      .update(returnRequests)
      .set({
        status: "items_received", // Keep status so staff can retry
        staffNotes: `Refund processing failed: ${error instanceof Error ? error.message : "Unknown error"}. Please retry or process manually.`,
        updatedAt: new Date(),
      })
      .where(eq(returnRequests.id, returnRequestId!));

    throw error;
  }
}

/**
 * Stage 4: Send final confirmation (placeholder)
 */
async function sendReturnConfirmation(payload: JobPayload): Promise<any> {
  // This would send the final confirmation email with return instructions
  const { email } = payload;

  console.log(`Sending return confirmation to ${email}`);

  return { status: "confirmation_sent" };
}

/**
 * Process damage check job - placeholder for vision assessment
 */
async function processDamageCheckJob(payload: JobPayload) {
  const { sessionId, userInfo } = payload;

  // This will be implemented when we add vision assessment
  console.log("🔍 Processing damage check job", { sessionId, userInfo });

  const message =
    "Ik bekijk de foto's die je hebt gestuurd. Dit kan even duren, ik laat je weten wat ik vind.";
  await sendMessageToSession(sessionId, message);

  return { status: "damage_check_initiated" };
}

/**
 * Process knowledge source - chunk content, generate embeddings, and analyze chunks
 */
async function processKnowledgeSourceJob(payload: JobPayload) {
  const { sourceId, siteId } = payload;

  if (!sourceId) {
    throw new Error("Source ID is required for knowledge processing");
  }

  if (!siteId) {
    throw new Error("Site ID is required for knowledge processing");
  }

  console.log("🧠 Processing knowledge source", { sourceId, siteId });

  try {
    // Get the knowledge source record with security filtering
    const knowledgeSource = await db.query.knowledgeSources.findFirst({
      where: and(eq(knowledgeSources.id, sourceId), eq(knowledgeSources.siteId, siteId)),
    });

    if (!knowledgeSource) {
      throw new Error(`Knowledge source not found: ${sourceId} for site: ${siteId}`);
    }

    // Only skip if already successfully processed
    if (knowledgeSource.status === "ready") {
      console.log(
        `Knowledge source ${sourceId} already processed (status: ${knowledgeSource.status})`,
      );
      return { status: "already_processed", sourceId };
    }

    // Ensure status is set to processing (in case it's "new" or "error")
    if (knowledgeSource.status !== "processing") {
      await db
        .update(knowledgeSources)
        .set({ status: "processing" })
        .where(eq(knowledgeSources.id, sourceId));
    }

    // Extract content from metadata
    const metadata = knowledgeSource.meta as any;
    const content = metadata?.content || metadata?.text || "";

    if (!content || typeof content !== "string" || content.trim().length === 0) {
      throw new Error("No content found in knowledge source metadata");
    }

    console.log(`Processing content: ${content.length} characters`);

    // Chunk the content into semantic pieces
    const chunks = chunkText(content, 200, 500);
    console.log(`Created ${chunks.length} chunks`);

    // Initialize OpenAI for embeddings and analysis
    const { ChatOpenAI } = await import("@langchain/openai");
    const analysisModel = new ChatOpenAI({
      model: "gpt-5-mini", // Use mini for analysis to save costs
      temperature: 0.1,
    });

    const processedChunks = [];

    // Process each chunk
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      console.log(`Processing chunk ${i + 1}/${chunks.length}`);

      try {
        // Generate embedding using OpenAI
        const embeddingResponse = await fetch("https://api.openai.com/v1/embeddings", {
          method: "POST",
          headers: {
            Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            model: "text-embedding-3-small",
            input: chunk,
            encoding_format: "float",
          }),
        });

        if (!embeddingResponse.ok) {
          throw new Error(`OpenAI embedding API error: ${embeddingResponse.statusText}`);
        }

        const embeddingData = await embeddingResponse.json();
        const embedding = embeddingData.data[0].embedding;

        // Count tokens (rough estimate: ~4 chars per token for English)
        const tokenCount = Math.ceil(chunk.length / 4);

        // Generate scores using AI analysis
        const scores = await analyzeChunkWithAI(chunk, analysisModel);

        // Store the chunk
        const [savedChunk] = await db
          .insert(knowledgeChunks)
          .values({
            sourceId,
            content: chunk,
            embedding: embedding, // Store as number array for pgvector
            tokenCount,
            criticalityScore: scores.criticalityScore,
            clarityScore: scores.clarityScore,
            actionabilityScore: scores.actionabilityScore,
            expectedFrequencyScore: scores.expectedFrequencyScore,
          })
          .returning({ id: knowledgeChunks.id });

        processedChunks.push(savedChunk.id);

        // Add a small delay to avoid rate limiting
        if (i < chunks.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      } catch (chunkError) {
        console.error(`Error processing chunk ${i + 1}:`, chunkError);
        // Continue with other chunks, but log the error
      }
    }

    if (processedChunks.length === 0) {
      throw new Error("Failed to process any chunks successfully");
    }

    // Update source status to ready
    await db
      .update(knowledgeSources)
      .set({
        status: "ready",
        processedAt: new Date(),
      })
      .where(eq(knowledgeSources.id, sourceId));

    console.log(`✅ Knowledge source processed: ${processedChunks.length} chunks created`);

    // Queue embedding generation job
    try {
      await enqueueQStashJob({
        jobType: "generate_embeddings",
        sourceId,
        siteId,
        sessionId: payload.sessionId || "system",
        agentId: payload.agentId || "system",
      });
      console.log(`📤 Queued embedding generation for source ${sourceId}`);
    } catch (error) {
      console.error("Failed to queue embedding generation:", error);
      // Don't fail the whole job if embedding queue fails
    }

    return {
      status: "knowledge_processed",
      sourceId,
      chunksCreated: processedChunks.length,
      chunkIds: processedChunks,
    };
  } catch (error) {
    console.error("❌ Knowledge source processing failed:", error);

    // Update source status to error
    await db
      .update(knowledgeSources)
      .set({ status: "error" })
      .where(eq(knowledgeSources.id, sourceId));

    throw error;
  }
}

/**
 * Chunk text into semantic pieces of 200-500 words
 */
function chunkText(text: string, minWords: number = 200, maxWords: number = 500): string[] {
  // Split into paragraphs first
  const paragraphs = text.split(/\n\s*\n/).filter((p) => p.trim().length > 0);

  const chunks: string[] = [];
  let currentChunk = "";
  let currentWordCount = 0;

  for (const paragraph of paragraphs) {
    const words = paragraph.trim().split(/\s+/);
    const paragraphWordCount = words.length;

    // If adding this paragraph would exceed max words, start a new chunk
    if (currentWordCount > 0 && currentWordCount + paragraphWordCount > maxWords) {
      // Only add chunk if it meets minimum word count
      if (currentWordCount >= minWords) {
        chunks.push(currentChunk.trim());
        currentChunk = paragraph;
        currentWordCount = paragraphWordCount;
      } else {
        // Current chunk is too small, add the paragraph anyway
        currentChunk += "\n\n" + paragraph;
        currentWordCount += paragraphWordCount;
      }
    } else {
      // Add paragraph to current chunk
      if (currentChunk) {
        currentChunk += "\n\n" + paragraph;
      } else {
        currentChunk = paragraph;
      }
      currentWordCount += paragraphWordCount;
    }
  }

  // Add the last chunk if it exists
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  // If no chunks were created (very short text), create one chunk
  if (chunks.length === 0 && text.trim()) {
    chunks.push(text.trim());
  }

  return chunks;
}

/**
 * Analyze a chunk with AI to generate scores
 */
async function analyzeChunkWithAI(
  chunk: string,
  model: any,
): Promise<{
  criticalityScore: number;
  clarityScore: number;
  actionabilityScore: number;
  expectedFrequencyScore: number;
}> {
  const analysisPrompt = `Analyze this knowledge base content and rate it on 4 dimensions (scale 1-100):

Content to analyze:
"""
${chunk}
"""

Please provide scores for:

1. CRITICALITY (1-100): How critical/important is this information for customer service?
   - 90-100: Essential information (policies, legal requirements, core product info)
   - 70-89: Important information (processes, common issues)
   - 50-69: Useful information (tips, context)
   - 30-49: Nice-to-have information (background, general info)
   - 1-29: Low importance (fluff, redundant info)

2. CLARITY (1-100): How clear and well-written is this content?
   - 90-100: Crystal clear, perfectly written
   - 70-89: Clear and well-structured
   - 50-69: Generally clear with minor issues
   - 30-49: Somewhat unclear or confusing
   - 1-29: Very unclear or poorly written

3. ACTIONABILITY (1-100): How actionable is this information for customer service agents?
   - 90-100: Direct instructions, specific steps to take
   - 70-89: Clear guidance on what to do
   - 50-69: Some actionable elements
   - 30-49: Mostly informational, limited actionability
   - 1-29: Pure information, no actions to take

4. EXPECTED_FREQUENCY (1-100): How often will customer service agents need this information?
   - 90-100: Daily use (common questions, basic processes)
   - 70-89: Weekly use (regular but not daily)
   - 50-69: Monthly use (occasional reference)
   - 30-49: Quarterly use (rare situations)
   - 1-29: Yearly or less (very rare edge cases)

Respond with ONLY a JSON object in this exact format:
{
  "criticalityScore": 85,
  "clarityScore": 92,
  "actionabilityScore": 78,
  "expectedFrequencyScore": 65
}`;

  try {
    const response = await model.invoke(analysisPrompt);
    const content = response.content.toString();

    // Extract JSON from the response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("No JSON found in AI response");
    }

    const scores = JSON.parse(jsonMatch[0]);

    // Validate scores are numbers between 1-100
    const validatedScores = {
      criticalityScore: Math.max(1, Math.min(100, Math.round(scores.criticalityScore || 50))),
      clarityScore: Math.max(1, Math.min(100, Math.round(scores.clarityScore || 50))),
      actionabilityScore: Math.max(1, Math.min(100, Math.round(scores.actionabilityScore || 50))),
      expectedFrequencyScore: Math.max(
        1,
        Math.min(100, Math.round(scores.expectedFrequencyScore || 50)),
      ),
    };

    return validatedScores;
  } catch (error) {
    console.error("Error analyzing chunk with AI:", error);
    // Return default scores if AI analysis fails
    return {
      criticalityScore: 50,
      clarityScore: 50,
      actionabilityScore: 50,
      expectedFrequencyScore: 50,
    };
  }
}

/**
 * Process generate embeddings job for knowledge sources
 */
async function processGenerateEmbeddingsJob(payload: JobPayload) {
  const { sourceId, siteId } = payload;

  if (!sourceId) {
    throw new Error("Source ID is required for embedding generation");
  }

  if (!siteId) {
    throw new Error("Site ID is required for embedding generation");
  }

  console.log("🤖 Generating embeddings for knowledge source", { sourceId, siteId });

  try {
    // Verify the source belongs to the site
    const source = await db.query.knowledgeSources.findFirst({
      where: and(eq(knowledgeSources.id, sourceId), eq(knowledgeSources.siteId, siteId)),
    });

    if (!source) {
      throw new Error(`Knowledge source not found: ${sourceId} for site: ${siteId}`);
    }

    // Generate embeddings for all chunks of this source
    await generateSourceEmbeddings(sourceId);

    console.log(`✅ Successfully generated embeddings for source ${sourceId}`);

    return {
      status: "success",
      sourceId,
      message: "Embeddings generated successfully",
    };
  } catch (error) {
    console.error("Error generating embeddings:", error);
    throw error;
  }
}

/**
 * Process product recommendations job
 */
async function processProductRecommendationsJob(payload: JobPayload) {
  const { sessionId, siteId, userInfo } = payload;

  if (!sessionId || !siteId || !userInfo?.query) {
    throw new Error("Missing required parameters for product recommendations");
  }

  try {
    const { getProductRecommendations } = await import("@/lib/embeddings/products");

    // Get product recommendations
    const recommendations = await getProductRecommendations(userInfo.query, siteId, {
      limit: userInfo.limit || 5,
      includeReasoning: true,
    });

    // Generate natural response when no products found
    if (recommendations.products.length === 0) {
      // Let the agent generate a contextual response based on the shop and situation
      const { shopifyTools } = await import("@/lib/graphs/platforms/shopify/tools");
      
      const noResultsResponse = await shopifyTools.answerGeneralQuestion({
        question: `The customer searched for "${userInfo.query}" but no products were found. Generate a helpful, conversational response that acknowledges this and guides them to be more specific or ask about what they're looking for, without being robotic.`,
        conversationHistory: [], // We don't have full context here, but that's OK
      });
      
      await sendMessageToSession(sessionId, noResultsResponse);
    } else {
      // Use the shopify tool to generate a natural response
      const { shopifyTools } = await import("@/lib/graphs/platforms/shopify/tools");

      // Format products for natural response generation
      const formattedProducts = recommendations.products.map((product: any, index: number) => ({
        title: product.title,
        price: product.price ? `€${product.price.toFixed(2)}` : "Prijs op aanvraag",
        vendor: product.vendor || "",
        description: product.description || "",
        reasoning: product.reasoning || "",
        relevanceScore: (product.relevanceScore * 100).toFixed(0),
      }));

      // Generate natural response (similar to what the tool was doing)
      const { ChatOpenAI } = await import("@langchain/openai");
      const { ChatPromptTemplate } = await import("@langchain/core/prompts");
      const { StringOutputParser } = await import("@langchain/core/output_parsers");

      const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });
      const prompt = ChatPromptTemplate.fromTemplate(`
Je bent een behulpzame klantenservice agent voor een Nederlandse webshop.

Een klant vroeg: "{query}"

Je hebt de volgende productaanbevelingen gevonden:
{products}

AI-analyse: {reasoning}

Genereer een natuurlijke, vriendelijke reactie in het Nederlands die:
- De klant bedankt voor hun vraag
- De gevonden producten presenteert op een natuurlijke manier (niet als lijst)
- Highlights waarom deze producten geschikt zijn
- Vraagt of ze meer informatie willen over een specifiek product
- Gebruikt "je/jij" in plaats van "u"
- Klinkt menselijk en persoonlijk, niet robotachtig

Voorbeelden van goede reacties:
"Leuk dat je op zoek bent naar... Ik heb een paar mooie opties voor je gevonden!"
"Voor jouw vraag naar... kan ik je deze producten aanraden:"
"Ik denk dat deze producten perfect bij je wensen passen:"
`);

      const productsStr = formattedProducts
        .map(
          (p: any, i: number) =>
            `${i + 1}. ${p.title} - ${p.price}${p.vendor ? ` (${p.vendor})` : ""}${p.reasoning ? `\n   ${p.reasoning}` : ""}`,
        )
        .join("\n\n");

      const response = await prompt.pipe(model).pipe(new StringOutputParser()).invoke({
        query: userInfo.query,
        products: productsStr,
        reasoning: recommendations.reasoning,
      });

      await sendMessageToSession(sessionId, response);
    }

    return {
      status: "success",
      query: userInfo.query,
      productsFound: recommendations.products.length,
      message: "Product recommendations processed successfully",
    };
  } catch (error) {
    console.error("Error processing product recommendations:", error);

    // Send fallback message to user
    await sendMessageToSession(
      sessionId,
      "Sorry, er ging iets mis bij het zoeken naar producten. Probeer het later opnieuw.",
    );

    throw error;
  }
}
