import { NextRequest, NextResponse } from "next/server";
import { redis, enqueueQStashJob, JobTracker } from "@/lib/upstash";
import { processPendingEmails } from "@/lib/email";

/**
 * Vercel Cron endpoint for monitoring and retrying failed jobs
 * This runs every 5 minutes to check for failed jobs and retry them
 */
export async function GET(req: NextRequest) {
  try {
    console.log("🔄 Running job monitoring cron...");

    // Get all job keys from Redis
    const jobKeys = await redis.keys("job:*");

    let failedJobs = 0;
    let retriedJobs = 0;
    let cleanedJobs = 0;

    for (const jobKey of jobKeys) {
      const jobData = await redis.hgetall(jobKey);

      if (!jobData || Object.keys(jobData).length === 0) continue;

      const messageId = jobKey.replace("job:", "");
      const status = jobData.status as string;
      const updatedAt = parseInt(jobData.updatedAt as string);
      const now = Date.now();

      // Clean up old completed jobs (older than 2 hours)
      if (status === "completed" && now - updatedAt > 2 * 60 * 60 * 1000) {
        await redis.del(jobKey);
        cleanedJobs++;
        continue;
      }

      // Retry failed jobs (but not too old ones)
      if (status === "failed" && now - updatedAt < 4 * 60 * 60 * 1000) {
        try {
          // Get the original job payload from the error details
          const originalPayload = jobData.originalPayload;

          if (originalPayload) {
            const payload = JSON.parse(originalPayload as string);

            // Retry the job with a 30-second delay
            await enqueueQStashJob(payload, 30);

            // Mark as retried
            await JobTracker.updateJobStatus(messageId, "queued");

            retriedJobs++;
            console.log(`♻️  Retried failed job: ${messageId}`);
          }
        } catch (retryError) {
          console.error(`Failed to retry job ${messageId}:`, retryError);
        }

        failedJobs++;
      }

      // Check for stuck running jobs (older than 15 minutes)
      if (status === "running" && now - updatedAt > 15 * 60 * 1000) {
        try {
          // Mark as failed so it can be retried
          await JobTracker.updateJobStatus(
            messageId,
            "failed",
            null,
            "Job stuck in running state, marked for retry",
          );

          console.log(`⚠️  Marked stuck job as failed: ${messageId}`);
        } catch (error) {
          console.error(`Failed to mark stuck job as failed ${messageId}:`, error);
        }
      }
    }

    // Clean up old conversation history (older than 7 days)
    const conversationKeys = await redis.keys("conversation:*");
    let cleanedConversations = 0;

    for (const key of conversationKeys) {
      const ttl = await redis.ttl(key);

      // If TTL is -1 (never expires) or very large, set proper expiration
      if (ttl === -1 || ttl > 7 * 24 * 60 * 60) {
        await redis.expire(key, 7 * 24 * 60 * 60); // 7 days
        cleanedConversations++;
      }
    }

    // Clean up old agent states (older than 24 hours)
    const stateKeys = await redis.keys("agent_state:*");
    let cleanedStates = 0;

    for (const key of stateKeys) {
      const ttl = await redis.ttl(key);

      // If TTL is -1 or very large, set proper expiration
      if (ttl === -1 || ttl > 24 * 60 * 60) {
        await redis.expire(key, 24 * 60 * 60); // 24 hours
        cleanedStates++;
      }
    }

    // Process pending emails
    let emailResult = { processed: 0, failed: 0 };
    try {
      emailResult = await processPendingEmails();
      console.log(`📧 Processed ${emailResult.processed} emails, ${emailResult.failed} failed`);
    } catch (emailError) {
      console.error("❌ Email processing failed during cron:", emailError);
    }

    console.log("✅ Job monitoring completed", {
      totalJobs: jobKeys.length,
      failedJobs,
      retriedJobs,
      cleanedJobs,
      cleanedConversations,
      cleanedStates,
      emailsProcessed: emailResult.processed,
      emailsFailed: emailResult.failed,
    });

    return NextResponse.json({
      success: true,
      stats: {
        totalJobs: jobKeys.length,
        failedJobs,
        retriedJobs,
        cleanedJobs,
        cleanedConversations,
        cleanedStates,
        emailsProcessed: emailResult.processed,
        emailsFailed: emailResult.failed,
      },
    });
  } catch (error) {
    console.error("❌ Job monitoring cron failed:", error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * Manual trigger endpoint for testing
 */
export async function POST(req: NextRequest) {
  // Allow manual triggering for testing
  return GET(req);
}
