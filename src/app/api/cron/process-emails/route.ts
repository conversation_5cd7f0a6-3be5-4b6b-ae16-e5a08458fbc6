import { NextRequest, NextResponse } from "next/server";
import { processPendingEmails } from "@/lib/email";

/**
 * <PERSON><PERSON> job to process pending emails
 * This endpoint should be called regularly to send queued emails
 */
export async function GET(req: NextRequest) {
  try {
    console.log("🔄 Processing pending emails...");

    const result = await processPendingEmails();

    console.log(`✅ Email processing completed: ${result.processed} sent, ${result.failed} failed`);

    return NextResponse.json({
      success: true,
      processed: result.processed,
      failed: result.failed,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Error in email processing cron:", error);
    return NextResponse.json(
      {
        error: "Email processing failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * Manual trigger for email processing (POST)
 */
export async function POST(req: NextRequest) {
  try {
    console.log("🔄 Manual email processing triggered...");

    const result = await processPendingEmails();

    return NextResponse.json({
      success: true,
      processed: result.processed,
      failed: result.failed,
      message: `Successfully processed ${result.processed} emails, ${result.failed} failed`,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Error in manual email processing:", error);
    return NextResponse.json(
      {
        error: "Email processing failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
