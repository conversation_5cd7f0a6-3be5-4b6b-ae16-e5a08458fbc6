import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { sites, catalogProducts } from "@/db/schema";
import { eq, and, isNotNull, lt } from "drizzle-orm";
import { ShopifyClient } from "@/lib/clients";
import { queueJob } from "@/lib/workers/job-queue";
import crypto from "crypto";

/**
 * Periodic sync job for Shopify products
 * Runs as a fallback in case webhooks fail or are missed
 * Can be triggered by cron job (Vercel cron, GitHub Actions, etc.)
 */
export async function GET(req: NextRequest) {
  try {
    // Verify cron secret if configured
    const authHeader = req.headers.get("authorization");
    if (process.env.CRON_SECRET && authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("🔄 Starting periodic product sync");

    // Get all Shopify sites that have credentials
    const shopifySites = await db
      .select()
      .from(sites)
      .where(
        and(
          eq(sites.platform, "shopify"),
          isNotNull(sites.platformToken),
          isNotNull(sites.platformShopId),
        ),
      );

    console.log(`Found ${shopifySites.length} Shopify sites to sync`);

    const results = await Promise.allSettled(shopifySites.map((site) => syncSiteProducts(site)));

    const successful = results.filter((r) => r.status === "fulfilled").length;
    const failed = results.filter((r) => r.status === "rejected");

    if (failed.length > 0) {
      console.error("Failed syncs:", failed);
    }

    return NextResponse.json({
      success: true,
      sites: shopifySites.length,
      successful,
      failed: failed.length,
    });
  } catch (error) {
    console.error("Product sync cron error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Sync products for a single site
 */
async function syncSiteProducts(site: any): Promise<void> {
  try {
    console.log(`Syncing products for site ${site.id} (${site.name})`);

    const client = new ShopifyClient(site.platformToken, site.platformShopId);

    // Get products updated in the last 24 hours
    const since = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

    const query = `
      query ($since: DateTime!) {
        products(first: 250, query: "updated_at:>$since") {
          edges {
            node {
              id
              title
              descriptionHtml
              vendor
              tags
              updatedAt
              priceRange {
                minVariantPrice {
                  amount
                }
              }
              images(first: 10) {
                edges {
                  node {
                    url
                  }
                }
              }
            }
          }
        }
      }
    `;

    const response = await client.query<any>(query, { since });
    const products = response.data?.products?.edges || [];

    console.log(`Found ${products.length} products updated since ${since}`);

    for (const edge of products) {
      const product = edge.node;
      const shopifyId = product.id.split("/").pop();

      // Check if product exists and when it was last updated
      const existingProduct = await db.query.catalogProducts.findFirst({
        where: and(
          eq(catalogProducts.siteId, site.id),
          eq(catalogProducts.shopifyProductId, shopifyId),
        ),
      });

      const productUpdatedAt = new Date(product.updatedAt);
      const existingUpdatedAt = existingProduct?.updatedAt
        ? new Date(existingProduct.updatedAt)
        : null;

      // Skip if product hasn't changed
      if (existingUpdatedAt && existingUpdatedAt >= productUpdatedAt) {
        continue;
      }

      // Upsert product
      const catalogProduct = {
        siteId: site.id,
        shopifyProductId: shopifyId,
        title: product.title,
        description: product.descriptionHtml,
        price: product.priceRange?.minVariantPrice?.amount || "0",
        vendor: product.vendor || "",
        tags: product.tags.join(", "),
        updatedAt: productUpdatedAt,
      };

      await db
        .insert(catalogProducts)
        .values(catalogProduct)
        .onConflictDoUpdate({
          target: [catalogProducts.siteId, catalogProducts.shopifyProductId],
          set: catalogProduct,
        });

      const savedProduct = await db.query.catalogProducts.findFirst({
        where: and(
          eq(catalogProducts.siteId, site.id),
          eq(catalogProducts.shopifyProductId, shopifyId),
        ),
      });

      if (!savedProduct) continue;

      // Queue embedding jobs
      await queueJob({
        type: "product_text_embed",
        payload: {
          productId: savedProduct.id,
          siteId: site.id,
          content: `${product.title} ${product.descriptionHtml} ${product.tags.join(" ")}`,
          checksum: crypto
            .createHash("md5")
            .update(`${product.title}${product.descriptionHtml}${product.tags.join("")}`)
            .digest("hex"),
          jobType: "product_text_embed",
        },
      });

      // Queue image embeddings
      const images = product.images.edges;
      for (let i = 0; i < images.length; i++) {
        await queueJob({
          type: "product_image_embed",
          payload: {
            productId: savedProduct.id,
            siteId: site.id,
            imageUrl: images[i].node.url,
            position: i,
            checksum: crypto.createHash("md5").update(images[i].node.url).digest("hex"),
            jobType: "product_image_embed",
          },
          delay: i * 2,
        });
      }
    }

    console.log(`✅ Synced ${products.length} products for site ${site.id}`);
  } catch (error) {
    console.error(`Failed to sync products for site ${site.id}:`, error);
    throw error;
  }
}
