import { redirect } from "next/navigation";
import { auth } from "@/auth";
import { db } from "@/db";
import { sites, orgMembers } from "@/db/schema";
import { eq, inArray } from "drizzle-orm";

export default async function DashboardPage() {
  const session = await auth();

  if (!session?.user?.id) {
    redirect("/login");
  }

  // Get all organizations the user is a member of
  const memberships = await db.query.orgMembers.findMany({
    where: eq(orgMembers.userId, session.user.id),
  });

  if (memberships.length === 0) {
    // User has no organization memberships, redirect to onboarding
    redirect("/onboarding");
  }

  // Get the first site from the user's organizations
  const orgIds = memberships.map((m) => m.orgId).filter((id): id is string => id !== null);

  const userSites = await db.query.sites.findMany({
    where: inArray(sites.orgId, orgIds),
    limit: 1,
  });

  if (userSites.length === 0) {
    // User's organizations have no sites, redirect to site creation
    redirect("/onboarding/site");
  }

  // Redirect to the first site's dashboard
  redirect(`/dashboard/${userSites[0].id}/home`);
}
