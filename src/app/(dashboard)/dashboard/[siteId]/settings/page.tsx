import Link from "next/link";
import { redirect } from "next/navigation";
import { auth } from "@/auth";
import { db } from "@/db";
import { sites, shopPolicyConfigs, orgMembers, organizations } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { AgentSettings } from "@/components/settings/agent-settings";
import { BrandGuidelines } from "@/components/settings/brand-guidelines";
import { IntegrationsSettings } from "@/components/settings/integrations-settings";
import { TeamSettings } from "@/components/settings/team-settings";
import { BillingSettings } from "@/components/settings/billing-settings";

/**
 * Settings page with **sticky** vertical sub‑sidebar.
 * Sidebar remains fixed; only main content scrolls.
 */
interface SettingsPageProps {
  params: Promise<{ siteId: string }>;
  searchParams: Promise<{ section?: string }>;
}

export default async function SettingsPage({ params, searchParams }: SettingsPageProps) {
  const session = await auth();
  if (!session?.user?.id) redirect("/login");

  const { siteId } = await params;
  const { section } = await searchParams;

  /* 1. Authorisation ---------------------------------------------------- */
  const site = await db.query.sites.findFirst({ where: eq(sites.id, siteId) });
  if (!site) redirect("/dashboard");

  const membership = await db.query.orgMembers.findFirst({
    where: and(eq(orgMembers.orgId, site.orgId!), eq(orgMembers.userId, session.user.id)),
  });
  if (!membership || !membership.role) redirect("/dashboard");

  const organization = await db.query.organizations.findFirst({
    where: eq(organizations.id, site.orgId!),
  });

  /* 2. Config fetch ----------------------------------------------------- */
  const shopConfig = await db.query.shopPolicyConfigs.findFirst({
    where: and(eq(shopPolicyConfigs.siteId, siteId), eq(shopPolicyConfigs.active, true)),
  });

  const defaultConfig = {
    personalityPrompt:
      "Je bent een vriendelijke en behulpzame klantenservice medewerker. Spreek klanten aan met 'je'.",
    enabledTools: ["lookupOrder", "createReturn", "checkInventory"],
    toolPolicies: {
      createReturn: [
        "Vraag altijd om een reden bij het aanmaken van een retour",
        "Bij beschadiging, vraag om foto's",
      ],
    },
    maxTurnsBeforeEscalation: 5,
    escalationEnabled: true,
    escalationEmail: "",
    primaryModel: "gpt-4o",
    fallbackModel: "gpt-5-mini",
    temperature: 20,
    reflectionEnabled: true,
    reflectionThreshold: 40,
    businessHours: {
      timezone: "Europe/Amsterdam",
      schedule: {
        monday: { start: "09:00", end: "17:00", enabled: true },
        tuesday: { start: "09:00", end: "17:00", enabled: true },
        wednesday: { start: "09:00", end: "17:00", enabled: true },
        thursday: { start: "09:00", end: "17:00", enabled: true },
        friday: { start: "09:00", end: "17:00", enabled: true },
        saturday: { start: "10:00", end: "16:00", enabled: false },
        sunday: { start: "10:00", end: "16:00", enabled: false },
      },
    },
  };

  // Transform database config to component-compatible config
  const config = shopConfig
    ? {
        personalityPrompt: shopConfig.personalityPrompt,
        enabledTools: shopConfig.enabledTools || defaultConfig.enabledTools,
        toolPolicies: shopConfig.toolPolicies || defaultConfig.toolPolicies,
        maxTurnsBeforeEscalation:
          shopConfig.maxTurnsBeforeEscalation ?? defaultConfig.maxTurnsBeforeEscalation,
        escalationEnabled: shopConfig.escalationEnabled ?? defaultConfig.escalationEnabled,
        escalationEmail: shopConfig.escalationEmail || defaultConfig.escalationEmail,
        primaryModel: shopConfig.primaryModel || defaultConfig.primaryModel,
        fallbackModel: shopConfig.fallbackModel || defaultConfig.fallbackModel,
        temperature: shopConfig.temperature ?? defaultConfig.temperature,
        reflectionEnabled: shopConfig.reflectionEnabled ?? defaultConfig.reflectionEnabled,
        reflectionThreshold: shopConfig.reflectionThreshold ?? defaultConfig.reflectionThreshold,
        businessHours: shopConfig.businessHours || defaultConfig.businessHours,
      }
    : defaultConfig;

  /* 3. UI helpers ------------------------------------------------------- */
  const SECTION = section ?? "agent";
  const navItems = [
    { key: "agent", label: "Agent & Tools" },
    { key: "brand", label: "Brand Voice" },
    { key: "integrations", label: "Integraties" },
    { key: "team", label: "Teamleden" },
    { key: "billing", label: "Billing" },
  ];
  const linkTo = (key: string) => `/dashboard/${siteId}/settings?section=${key}`;

  /* 4. Render ----------------------------------------------------------- */
  return (
    <div className="flex h-full gap-6 px-6 py-8">
      {/* Sticky sub‑sidebar   */}
      <aside className="sticky top-24 h-max w-48 shrink-0 self-start">
        <nav className="flex flex-col gap-1">
          {navItems.map(({ key, label }) => (
            <Link
              key={key}
              href={linkTo(key)}
              className={cn(
                "rounded-lg px-4 py-2 text-sm font-medium hover:bg-muted",
                SECTION === key && "bg-muted text-primary",
              )}
            >
              {label}
            </Link>
          ))}
        </nav>
      </aside>

      {/* Scrollable content */}
      <main className="flex-1 space-y-6 overflow-y-auto pb-10">
        <h1 className="text-3xl font-bold">Instellingen – {site.name}</h1>

        {SECTION === "agent" && (
          <SettingsCard
            title="Agent Configuratie"
            desc="Configureer technische mogelijkheden, tools en gedragsregels van je AI‑agent."
          >
            <AgentSettings siteId={siteId} config={config} hasExistingConfig={!!shopConfig} />
          </SettingsCard>
        )}

        {SECTION === "brand" && (
          <SettingsCard
            title="Brand Guidelines"
            desc="Definieer hoe je AI‑agent communiceert en je merk vertegenwoordigt."
          >
            <BrandGuidelines siteId={siteId} currentPrompt={config.personalityPrompt} />
          </SettingsCard>
        )}

        {SECTION === "integrations" && (
          <SettingsCard title="Integraties" desc="Beheer platform‑integraties en webhooks.">
            <IntegrationsSettings
              site={site}
              userRole={membership.role as "owner" | "admin" | "agent"}
            />
          </SettingsCard>
        )}

        {SECTION === "team" && (
          <SettingsCard title="Teamleden" desc="Beheer toegangsrechten.">
            <TeamSettings
              organizationId={site.orgId!}
              userRole={membership.role as "owner" | "admin" | "agent"}
            />
          </SettingsCard>
        )}

        {SECTION === "billing" && (
          <SettingsCard title="Billing & Abonnement" desc="Bekijk en wijzig je plan.">
            <BillingSettings
              organization={organization!}
              userRole={membership.role as "owner" | "admin" | "agent"}
            />
          </SettingsCard>
        )}
      </main>
    </div>
  );
}

/* -------------------- helpers -------------------- */
function SettingsCard({
  title,
  desc,
  children,
}: { title: string; desc: string; children: React.ReactNode }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{desc}</CardDescription>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}
