"use client";

import { useEffect, useState } from "react";
import { useRout<PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Link from "next/link";
import {
  ArrowLeft,
  Brain,
  Save,
  Lightbulb,
  AlertTriangle,
  Sparkles,
  FileText,
  CheckCircle,
  Zap,
  Loader2,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  <PERSON><PERSON>D<PERSON>og,
  AlertD<PERSON>ogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { useArticle, useUpdateArticle } from "@/hooks/use-knowledge";

const articleSchema = z.object({
  title: z
    .string()
    .min(3, "Titel moet minimaal 3 karakters lang zijn")
    .max(120, "Titel mag maximaal 120 karakters lang zijn"),
  content: z
    .string()
    .min(10, "Content moet minimaal 10 karakters lang zijn")
    .max(50000, "Content mag maximaal 50.000 karakters lang zijn"),
});

type ArticleFormData = z.infer<typeof articleSchema>;

interface EditKnowledgeArticlePageProps {
  params: Promise<{
    siteId: string;
    articleId: string;
  }>;
}

export default function EditKnowledgeArticlePage({ params }: EditKnowledgeArticlePageProps) {
  const router = useRouter();
  const [siteId, setSiteId] = useState<string>("");
  const [articleId, setArticleId] = useState<string>("");

  // Resolve params
  useEffect(() => {
    params.then(({ siteId, articleId }) => {
      setSiteId(siteId);
      setArticleId(articleId);
    });
  }, [params]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);

  // Hooks (only initialize when we have the IDs)
  const {
    article,
    isLoading: isFetching,
    error: fetchError,
    fetchArticle,
  } = useArticle(siteId, articleId);
  const { updateArticle, isUpdating } = useUpdateArticle(siteId, articleId);

  // Form setup
  const form = useForm<ArticleFormData>({
    resolver: zodResolver(articleSchema),
    defaultValues: {
      title: "",
      content: "",
    },
  });

  const watchedValues = form.watch();

  // Load article data
  useEffect(() => {
    if (siteId && articleId) {
      fetchArticle();
    }
  }, [siteId, articleId, fetchArticle]);

  // Populate form when article loads
  useEffect(() => {
    if (article) {
      const content = (article.meta as any)?.content || "";
      form.setValue("title", article.label);
      form.setValue("content", content);
      form.reset({
        title: article.label,
        content,
      });
    }
  }, [article, form]);

  // Track unsaved changes
  useEffect(() => {
    if (!article) return;

    const originalContent = (article.meta as any)?.content || "";
    const hasChanges =
      watchedValues.title !== article.label || watchedValues.content !== originalContent;

    setHasUnsavedChanges(hasChanges);
  }, [watchedValues, article]);

  // Handle navigation with unsaved changes warning
  const handleNavigation = (url: string) => {
    if (hasUnsavedChanges) {
      setPendingNavigation(url);
      setShowUnsavedDialog(true);
    } else {
      router.push(url);
    }
  };

  const confirmNavigation = () => {
    if (pendingNavigation) {
      router.push(pendingNavigation);
    }
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  };

  // Handle form submission
  const onSubmit = async (data: ArticleFormData) => {
    try {
      await updateArticle(data);
      setHasUnsavedChanges(false);
      router.push(`/dashboard/${siteId}/knowledge`);
    } catch (error) {
      // Error is handled by the hook
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "s") {
        e.preventDefault();
        if (!isUpdating && hasUnsavedChanges) {
          form.handleSubmit(onSubmit)();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [form, isUpdating, hasUnsavedChanges, onSubmit]);

  if (fetchError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/${siteId}/knowledge`)}
          >
            <ArrowLeft className="size-4" />
            Terug naar kennisbank
          </Button>
        </div>

        <Card className="border-destructive">
          <CardContent className="flex flex-col items-center justify-center py-16 text-center">
            <AlertTriangle className="size-16 text-destructive mb-4" />
            <h3 className="text-xl font-semibold mb-2">Artikel niet gevonden</h3>
            <p className="text-muted-foreground mb-6 max-w-md">
              {fetchError || "Het artikel dat je probeert te bewerken bestaat niet meer."}
            </p>
            <Button asChild>
              <Link href={`/dashboard/${siteId}/knowledge`}>Terug naar kennisbank</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleNavigation(`/dashboard/${siteId}/knowledge`)}
            disabled={isUpdating}
          >
            <ArrowLeft className="size-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              Artikel bewerken
            </h1>
            <p className="text-muted-foreground">
              Pas je artikel aan en maak je AI-agent nog slimmer!
            </p>
          </div>
        </div>

        {hasUnsavedChanges && (
          <div className="flex items-center gap-2 text-sm text-amber-600 bg-amber-50 dark:bg-amber-950/20 px-3 py-1 rounded-full border border-amber-200 dark:border-amber-800">
            <AlertTriangle className="size-3" />
            Niet opgeslagen wijzigingen
          </div>
        )}
      </div>

      {/* Main content */}
      <div className="max-w-4xl">
        {isFetching ? (
          // Loading state
          <div className="space-y-6">
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-96" />
            </div>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-full" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-10 w-32" />
              </CardContent>
            </Card>
          </div>
        ) : (
          <>
            {/* Form */}
            <Card>
              <CardContent className="pt-6">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    {/* Title Field */}
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Titel <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Bijv: Hoe werkt ons retourbeleid?"
                              disabled={isUpdating}
                              className="max-w-2xl transition-all duration-200 hover:shadow-md focus:shadow-lg"
                            />
                          </FormControl>
                          <FormDescription className="flex items-center gap-1">
                            Een pakkende titel helpt je AI-agent de juiste kennis te vinden.
                            Maximaal 120 karakters.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Content Field */}
                    <FormField
                      control={form.control}
                      name="content"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Inhoud <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder="Schrijf hier alles wat je AI-agent moet weten!

Bijvoorbeeld:
• Productinfo en specificaties
• Retour- en omruilbeleid  
• Veelgestelde klantvragen
• Servicerichtlijnen
• Interne processen

Hoe meer details, hoe slimmer je agent wordt!"
                              disabled={isUpdating}
                              className="min-h-[400px] resize-y transition-all duration-200 hover:shadow-md focus:shadow-lg"
                            />
                          </FormControl>
                          <FormDescription className="flex items-center gap-1">
                            <Brain className="size-3 text-primary" />
                            Deze kennis wordt direct toegevoegd aan je AI-agent.
                            <span className="text-xs text-muted-foreground ml-1">
                              (10-50.000 tekens)
                            </span>
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Character Counter */}
                    <div className="flex justify-between text-sm text-muted-foreground p-3">
                      <div className="flex items-center gap-1">
                        <FileText className="size-3" />
                        Titel: {form.watch("title").length}/120
                        {form.watch("title").length > 100 && (
                          <Zap className="size-3 text-amber-500" />
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        <Brain className="size-3" />
                        Inhoud: {form.watch("content").length.toLocaleString()}/50.000
                        {form.watch("content").length > 1000 && (
                          <CheckCircle className="size-3 text-green-500" />
                        )}
                      </div>
                    </div>

                    {/* Form Actions */}
                    <div className="flex items-center justify-end gap-3 pt-6 border-t">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => handleNavigation(`/dashboard/${siteId}/knowledge`)}
                        disabled={isUpdating}
                        className="hover:scale-105 transition-all duration-200"
                      >
                        Annuleren
                      </Button>
                      <Button
                        type="submit"
                        disabled={isUpdating || !hasUnsavedChanges}
                        className="group hover:scale-105 transition-all duration-200 hover:shadow-lg"
                      >
                        {isUpdating ? (
                          <>
                            <Loader2 className="size-4 animate-spin" />
                            <span className="animate-pulse">Wijzigingen worden opgeslagen...</span>
                          </>
                        ) : (
                          <>
                            <Save className="size-4 transition-colors" />
                            Wijzigingen opslaan
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>

            <Card className="mt-6 border border-border/50 bg-muted/30">
              <CardContent className="pt-6">
                <div className="flex items-start gap-4">
                  <div className="rounded-lg bg-muted p-2.5 border">
                    <Brain className="size-5 text-foreground/70" />
                  </div>
                  <div className="space-y-3 flex-1">
                    <h3 className="font-semibold text-foreground">
                      Wat gebeurt er na het opslaan?
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Je artikel wordt opnieuw verwerkt en bijgewerkt in de kennisbank van je
                      AI-agent.
                    </p>
                    <ul className="text-sm text-muted-foreground space-y-2.5">
                      <li className="flex items-start gap-3">
                        <span>- Wijzigingen worden geanalyseerd door onze AI</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span>- De bijgewerkte kennis wordt toegevoegd aan het agent-brein</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span>- Klanten krijgen direct de nieuwste informatie</span>
                      </li>
                    </ul>
                    <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-950/20 rounded-md border border-amber-200/50 dark:border-amber-800/50">
                      <p className="text-xs text-amber-800 dark:text-amber-200 flex items-start gap-2">
                        <Lightbulb className="size-3 mt-0.5 flex-shrink-0" />
                        <strong>Tip:</strong> Gebruik{" "}
                        <kbd className="px-1 bg-amber-100 dark:bg-amber-900 rounded text-xs">
                          Cmd/Ctrl + S
                        </kbd>{" "}
                        om snel op te slaan
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Unsaved changes dialog */}
      <AlertDialog open={showUnsavedDialog} onOpenChange={setShowUnsavedDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="size-5 text-amber-500" />
              Niet opgeslagen wijzigingen
            </AlertDialogTitle>
            <AlertDialogDescription>
              Je hebt wijzigingen gemaakt die nog niet zijn opgeslagen. Als je nu weggaat, gaan deze
              verloren.
              <br />
              <br />
              Wil je je wijzigingen eerst opslaan?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowUnsavedDialog(false)}>
              Blijf bewerken
            </AlertDialogCancel>
            <Button
              onClick={form.handleSubmit(onSubmit)}
              disabled={isUpdating}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isUpdating ? "Opslaan..." : "Opslaan & doorgaan"}
            </Button>
            <AlertDialogAction onClick={confirmNavigation} className="bg-red-600 hover:bg-red-700">
              Weggaan zonder opslaan
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
