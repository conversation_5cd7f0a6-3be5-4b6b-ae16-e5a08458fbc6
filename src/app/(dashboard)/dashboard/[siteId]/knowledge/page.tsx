"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import {
  Plus,
  Search,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Trash2,
  Edit3,
  Folder,
  FolderPlus,
  Save,
  Eye,
  Archive,
} from "lucide-react";
import { format } from "date-fns";
import { nl } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertD<PERSON>ogDescription,
  <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON>er,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import {
  useKnowledgeArticles,
  useKnowledgeTopics,
  type KnowledgeArticle,
} from "@/hooks/use-knowledge";

// Status badge configuration
const statusConfig = {
  new: {
    label: "Nieuw",
    variant: "secondary" as const,
    icon: Clock,
    description: "Artikel is zojuist aangemaakt",
  },
  processing: {
    label: "Verwerken",
    variant: "default" as const,
    icon: AlertCircle,
    description: "AI maakt het artikel slimmer",
  },
  ready: {
    label: "Klaar",
    variant: "default" as const,
    icon: CheckCircle,
    description: "Artikel is klaar voor gebruik",
  },
  error: {
    label: "Fout",
    variant: "destructive" as const,
    icon: XCircle,
    description: "Er ging iets mis bij de verwerking",
  },
};

export default function KnowledgeBasePage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const siteId = params.siteId as string;

  // State management
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [selectedArticle, setSelectedArticle] = useState<KnowledgeArticle | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState("");
  const [editTitle, setEditTitle] = useState("");
  const [newTopicName, setNewTopicName] = useState("");
  const [newTopicDescription, setNewTopicDescription] = useState("");
  const [isCreateTopicOpen, setIsCreateTopicOpen] = useState(false);
  const [showLoadingSkeleton, setShowLoadingSkeleton] = useState(false);
  const [showTopicsLoadingSkeleton, setShowTopicsLoadingSkeleton] = useState(false);

  // Hooks
  const {
    topics,
    isLoading: topicsLoading,
    fetchTopics,
    createTopic,
    deleteTopic,
  } = useKnowledgeTopics(siteId);
  const {
    articles,
    isLoading: articlesLoading,
    fetchArticles,
    deleteArticle,
  } = useKnowledgeArticles(siteId);

  // Load data on mount
  useEffect(() => {
    const loadInitialData = async () => {
      // Start timer for topics skeleton after 200ms
      const topicsTimer = setTimeout(() => {
        setShowTopicsLoadingSkeleton(true);
      }, 200);

      try {
        await fetchTopics();
      } finally {
        clearTimeout(topicsTimer);
        setShowTopicsLoadingSkeleton(false);
      }
    };

    loadInitialData();
    fetchArticles({ topicId: selectedTopic || undefined });
  }, [fetchTopics, fetchArticles, selectedTopic]);

  // Filter articles based on search
  const filteredArticles = articles.filter(
    (article) =>
      article.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.meta.content.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  // Handle topic selection
  const handleTopicSelect = async (topicId: string | null) => {
    setSelectedTopic(topicId);
    setSelectedArticle(null);
    setIsEditing(false);

    // Start a timer for showing loading skeleton after 200ms
    const loadingTimer = setTimeout(() => {
      setShowLoadingSkeleton(true);
    }, 200);

    try {
      await fetchArticles({ topicId: topicId || undefined });
    } finally {
      // Clear the timer and hide skeleton
      clearTimeout(loadingTimer);
      setShowLoadingSkeleton(false);
    }
  };

  // Handle article selection
  const handleArticleSelect = (article: KnowledgeArticle) => {
    setSelectedArticle(article);
    setEditTitle(article.label);
    setEditContent(article.meta.content);
    setIsEditing(false);
  };

  // Handle create new topic
  const handleCreateTopic = async () => {
    if (!newTopicName.trim()) return;

    try {
      await createTopic({
        name: newTopicName,
        description: newTopicDescription || undefined,
      });
      setNewTopicName("");
      setNewTopicDescription("");
      setIsCreateTopicOpen(false);
    } catch (error) {
      // Error already handled by hook
    }
  };

  const handleCreateArticle = () => {
    const url = selectedTopic
      ? `/dashboard/${siteId}/knowledge/new?topicId=${selectedTopic}`
      : `/dashboard/${siteId}/knowledge/new`;
    router.push(url);
  };

  const handleSaveArticle = () => {
    setIsEditing(false);
  };

  // Get current topic name
  const currentTopicName = selectedTopic
    ? topics.find((t) => t.id === selectedTopic)?.name
    : "Alle Artikelen";

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-background px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <img
              src="/nousu-icons/kennisbank.svg"
              alt="Kennisbank icon"
              className="h-16 w-16 mt-4"
              width={32}
              height={32}
            />
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Kennisbank</h1>
              <p className="text-sm text-muted-foreground">
                Organiseer je kennis in categorieën en artikelen
              </p>
            </div>
          </div>
          <Button onClick={handleCreateArticle}>
            <Plus className="size-4 mr-2" />
            Nieuw Artikel
          </Button>
        </div>
      </div>

      {/* Three-column layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Topics */}
        <div className="w-64 border-r bg-muted/30 flex flex-col">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Categorieën
              </h2>
              <Dialog open={isCreateTopicOpen} onOpenChange={setIsCreateTopicOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon" className="size-7">
                    <FolderPlus className="size-3" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Nieuwe Categorie</DialogTitle>
                    <DialogDescription>
                      Maak een nieuwe categorie om je artikelen te organiseren.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="topic-name" className="mb-2">
                        Naam
                      </Label>
                      <Input
                        id="topic-name"
                        value={newTopicName}
                        onChange={(e) => setNewTopicName(e.target.value)}
                        placeholder="Bijv. Retourbeleid"
                      />
                    </div>
                    <div>
                      <Label htmlFor="topic-description" className="mb-2">
                        Beschrijving (optioneel)
                      </Label>
                      <Textarea
                        id="topic-description"
                        value={newTopicDescription}
                        onChange={(e) => setNewTopicDescription(e.target.value)}
                        placeholder="Korte beschrijving van deze categorie..."
                        rows={3}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsCreateTopicOpen(false)}>
                      Annuleren
                    </Button>
                    <Button onClick={handleCreateTopic} disabled={!newTopicName.trim()}>
                      <Folder className="size-4 mr-2" />
                      Aanmaken
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 size-3 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Zoek artikelen..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 h-8 text-xs"
              />
            </div>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-2">
              {/* All Articles */}
              <button
                onClick={() => handleTopicSelect(null)}
                className={cn(
                  "w-full flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors text-left",
                  selectedTopic === null ? "bg-primary text-primary-foreground" : "hover:bg-muted",
                )}
              >
                <Archive className="size-4" />
                <span>Alle Artikelen</span>
                <span className="ml-auto text-xs opacity-70">{articles.length}</span>
              </button>

              <Separator className="my-2" />

              {/* Topics */}
              {showTopicsLoadingSkeleton ? (
                <div className="space-y-1">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-2 px-3 py-2">
                      <Skeleton className="size-4 rounded" />
                      <Skeleton className="h-4 flex-1" />
                      <Skeleton className="h-3 w-6 rounded-full" />
                    </div>
                  ))}
                </div>
              ) : topics.length === 0 ? (
                <div className="text-center py-8">
                  <Folder className="size-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-xs text-muted-foreground">Nog geen categorieën</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsCreateTopicOpen(true)}
                    className="mt-2"
                  >
                    <FolderPlus className="size-3 mr-1" />
                    Maak je eerste categorie
                  </Button>
                </div>
              ) : (
                <div
                  className={cn(
                    "space-y-1 transition-opacity duration-200",
                    topicsLoading ? "opacity-50" : "opacity-100",
                  )}
                >
                  {topics.map((topic) => (
                    <button
                      key={topic.id}
                      onClick={() => handleTopicSelect(topic.id)}
                      className={cn(
                        "w-full flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors text-left",
                        selectedTopic === topic.id
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-muted",
                      )}
                    >
                      <Folder className="size-4" />
                      <span className="flex-1 truncate">{topic.name}</span>
                      <span className="text-xs opacity-70">{topic.articleCount || 0}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Middle Column - Articles List */}
        <div className="w-80 border-r flex flex-col">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h2 className="font-semibold">{currentTopicName}</h2>
              <span className="text-xs text-muted-foreground">
                {filteredArticles.length} artikel{filteredArticles.length !== 1 ? "en" : ""}
              </span>
            </div>
          </div>

          <ScrollArea className="flex-1">
            {showLoadingSkeleton ? (
              <div className="p-4 space-y-3">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-5 w-16 rounded-full" />
                      </div>
                      <Skeleton className="h-3 w-full mb-1" />
                      <Skeleton className="h-3 w-2/3 mb-3" />
                      <div className="flex items-center justify-between">
                        <Skeleton className="h-3 w-20" />
                        <div className="flex gap-1">
                          <Skeleton className="size-6 rounded" />
                          <Skeleton className="size-6 rounded" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredArticles.length === 0 ? (
              <div className="p-8 text-center">
                <img
                  src="/nousu-icons/empty.svg"
                  alt="Empy state kennisbank icon"
                  className="h-16 w-16 mt-4 mx-auto"
                  width={32}
                  height={32}
                />
                {searchQuery ? (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium mb-2">Geen resultaten</h3>
                    <p className="text-xs text-muted-foreground">
                      Geen artikelen gevonden voor &ldquo;{searchQuery}&rdquo;
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium mb-2">Nog geen artikelen</h3>
                    <p className="text-xs text-muted-foreground mb-4">
                      Begin met het toevoegen van je eerste artikel
                    </p>
                    <Button size="sm" onClick={handleCreateArticle}>
                      <Plus className="size-3 mr-1" />
                      Nieuw Artikel
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div
                className={cn(
                  "p-2 space-y-2 transition-opacity duration-200",
                  articlesLoading ? "opacity-50" : "opacity-100",
                )}
              >
                {filteredArticles.map((article) => {
                  const statusInfo = statusConfig[article.status];
                  const StatusIcon = statusInfo.icon;
                  const createdAt = new Date(article.insertedAt);

                  return (
                    <Card
                      key={article.id}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:shadow-md border max-w-80",
                        selectedArticle?.id === article.id
                          ? "ring-2 ring-primary border-primary shadow-md"
                          : "hover:border-primary/30",
                      )}
                      onClick={() => handleArticleSelect(article)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between gap-2 mb-2">
                          <h3 className="font-medium text-sm truncate flex-1">{article.label}</h3>
                          <Badge variant={statusInfo.variant} className="text-xs">
                            <StatusIcon className="size-3 mr-1" />
                            {statusInfo.label}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                          {article.meta.content.substring(0, 150)}...
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">
                            {format(createdAt, "d MMM", { locale: nl })}
                          </span>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="size-6 hover:bg-blue-100 hover:text-blue-600"
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/dashboard/${siteId}/knowledge/${article.id}/edit`);
                              }}
                            >
                              <Edit3 className="size-3" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="size-6 text-destructive hover:bg-red-100 hover:text-red-600"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <Trash2 className="size-3" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Artikel verwijderen</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Weet je zeker dat je &ldquo;{article.label}&rdquo; wilt
                                    verwijderen?
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Annuleren</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => deleteArticle(article.id)}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                  >
                                    Verwijderen
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </ScrollArea>
        </div>

        {/* Right Column - Editor/Preview */}
        <div className="flex-1 flex flex-col">
          {selectedArticle ? (
            <>
              {/* Article Header */}
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <h2 className="font-semibold">{selectedArticle.label}</h2>
                    <Badge
                      variant={statusConfig[selectedArticle.status].variant}
                      className="text-xs"
                    >
                      {statusConfig[selectedArticle.status].label}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" onClick={() => setIsEditing(!isEditing)}>
                      {isEditing ? (
                        <>
                          <Eye className="size-4 mr-2" />
                          Preview
                        </>
                      ) : (
                        <>
                          <Edit3 className="size-4 mr-2" />
                          Bewerken
                        </>
                      )}
                    </Button>
                    {isEditing && (
                      <Button size="sm" onClick={handleSaveArticle}>
                        <Save className="size-4 mr-2" />
                        Opslaan
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-hidden">
                {isEditing ? (
                  <div className="h-full flex flex-col p-4 space-y-4">
                    <div>
                      <Label htmlFor="edit-title">Titel</Label>
                      <Input
                        id="edit-title"
                        value={editTitle}
                        onChange={(e) => setEditTitle(e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="flex-1">
                      <Label htmlFor="edit-content">Inhoud</Label>
                      <Textarea
                        id="edit-content"
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        className="mt-1 h-full resize-none"
                        placeholder="Schrijf je artikel hier..."
                      />
                    </div>
                  </div>
                ) : (
                  <ScrollArea className="h-full">
                    <div className="p-6 prose prose-sm max-w-none">
                      <div className="whitespace-pre-wrap">{selectedArticle.meta.content}</div>
                    </div>
                  </ScrollArea>
                )}
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <FileText className="size-16 text-muted-foreground mx-auto mb-6" />
                <h3 className="text-lg font-medium mb-2">Selecteer een artikel</h3>
                <p className="text-muted-foreground mb-6">
                  Kies een artikel uit de lijst om te bekijken of bewerken
                </p>
                {/*<Button onClick={handleCreateArticle}>
                  <Plus className="size-4 mr-2" />
                  Nieuw Artikel
                </Button>*/}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
