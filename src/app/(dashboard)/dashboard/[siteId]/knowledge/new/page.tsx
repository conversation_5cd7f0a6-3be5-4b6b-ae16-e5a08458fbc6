"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeft,
  FileText,
  Loader2,
  Sparkles,
  Brain,
  Lightbulb,
  Zap,
  CheckCircle,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useCreateArticle, useKnowledgeTopics } from "@/hooks/use-knowledge";

// Form validation schema
const createArticleSchema = z.object({
  title: z
    .string()
    .min(1, "Titel is verplicht")
    .max(120, "Titel mag maximaal 120 karakters bevatten")
    .trim(),
  content: z
    .string()
    .min(10, "Inhoud moet minimaal 10 karakters bevatten")
    .max(50000, "Inhoud mag maximaal 50.000 karakters bevatten")
    .trim(),
});

type CreateArticleFormData = z.infer<typeof createArticleSchema>;

export default function NewKnowledgeArticlePage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const siteId = params.siteId as string;

  // Get topicId from URL if provided
  const topicId = searchParams.get("topicId");

  const { createArticle, isCreating } = useCreateArticle(siteId);
  const { topics, fetchTopics } = useKnowledgeTopics(siteId);

  // Get topic name for display
  const selectedTopicName = topicId ? topics.find((t) => t.id === topicId)?.name : null;

  // Load topics on mount
  useEffect(() => {
    fetchTopics();
  }, [fetchTopics]);

  // Form setup with react-hook-form and zod validation
  const form = useForm<CreateArticleFormData>({
    resolver: zodResolver(createArticleSchema),
    defaultValues: {
      title: "",
      content: "",
    },
  });

  // Handle form submission
  const onSubmit = async (data: CreateArticleFormData) => {
    try {
      await createArticle({
        ...data,
        topicId: topicId || undefined,
      });
      // Redirect back to knowledge base on success
      router.push(`/dashboard/${siteId}/knowledge`);
    } catch (error) {
      // Error is already handled by the hook (toast shown)
      console.error("Failed to create article:", error);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    // Check if form has unsaved changes
    const hasChanges = form.watch("title") || form.watch("content");

    if (hasChanges) {
      const confirmed = window.confirm(
        "Je hebt nog niet-opgeslagen wijzigingen 📝. Weet je zeker dat je wilt stoppen? Je AI-agent mist dan deze kennis! 🤔",
      );
      if (!confirmed) return;
    }

    router.push(`/dashboard/${siteId}/knowledge`);
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={handleCancel} disabled={isCreating}>
          <ArrowLeft className="size-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            Nieuw Artikel
            {selectedTopicName && (
              <span className="text-lg font-normal text-muted-foreground">
                • {selectedTopicName}
              </span>
            )}
          </h1>
          <p className="text-muted-foreground">
            {selectedTopicName
              ? `Dit artikel wordt toegevoegd aan de categorie "${selectedTopicName}"`
              : "Tijd om je AI-agent nog slimmer te maken!"}
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-4xl">
        <Card>
          <CardContent className="pt-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Title Field */}
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Titel <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Bijv: Hoe werkt ons retourbeleid?"
                          disabled={isCreating}
                          className="max-w-2xl transition-all duration-200 hover:shadow-md focus:shadow-lg"
                        />
                      </FormControl>
                      <FormDescription className="flex items-center gap-1">
                        Een pakkende titel helpt je AI-agent de juiste kennis te vinden. Maximaal
                        120 karakters.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Content Field */}
                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Inhoud <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Schrijf hier alles wat je AI-agent moet weten!

Bijvoorbeeld:
• Productinfo en specificaties
• Retour- en omruilbeleid  
• Veelgestelde klantvragen
• Servicerichtlijnen
• Interne processen

Hoe meer details, hoe slimmer je agent wordt!"
                          disabled={isCreating}
                          className="min-h-[400px] resize-y transition-all duration-200 hover:shadow-md focus:shadow-lg"
                        />
                      </FormControl>
                      <FormDescription className="flex items-center gap-1">
                        <Brain className="size-3 text-primary" />
                        Deze kennis wordt direct toegevoegd aan je AI-agent.
                        <span className="text-xs text-muted-foreground ml-1">
                          (10-50.000 tekens)
                        </span>
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Character Counter */}
                <div className="flex justify-between text-sm text-muted-foreground p-3">
                  <div className="flex items-center gap-1">
                    <FileText className="size-3" />
                    Titel: {form.watch("title").length}/120
                    {form.watch("title").length > 100 && <Zap className="size-3 text-amber-500" />}
                  </div>
                  <div className="flex items-center gap-1">
                    <Brain className="size-3" />
                    Inhoud: {form.watch("content").length.toLocaleString()}/50.000
                    {form.watch("content").length > 1000 && (
                      <CheckCircle className="size-3 text-green-500" />
                    )}
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex items-center justify-end gap-3 pt-6 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isCreating}
                    className="hover:scale-105 transition-all duration-200"
                  >
                    Annuleren
                  </Button>
                  <Button
                    type="submit"
                    disabled={isCreating || !form.formState.isValid}
                    className="group hover:scale-105 transition-all duration-200 hover:shadow-lg"
                  >
                    {isCreating ? (
                      <>
                        <Loader2 className="size-4 animate-spin" />
                        <span className="animate-pulse">Toevoegen...</span>
                      </>
                    ) : (
                      <>
                        <Sparkles className="size-4 transition-colors" />
                        Kennis toevoegen
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        <Card className="mt-6 border border-border/50 bg-muted/30">
          <CardContent className="pt-6">
            <div className="flex items-start gap-4">
              <div className="rounded-lg bg-muted p-2.5 border">
                <Brain className="size-5 text-foreground/70" />
              </div>
              <div className="space-y-3 flex-1">
                <h3 className="font-semibold text-foreground">Hoe werkt het proces?</h3>
                <p className="text-sm text-muted-foreground">
                  Zodra je op &ldquo;Kennis toevoegen&rdquo; klikt, wordt je artikel automatisch
                  verwerkt en toegevoegd aan de kennisbank.
                </p>
                <ul className="text-sm text-muted-foreground space-y-2.5">
                  <li className="flex items-start gap-3">
                    <span>- Je artikel wordt geanalyseerd door onze AI</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span>- De kennis wordt toegevoegd aan het agent-brein</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span>- Klanten krijgen direct betere antwoorden</span>
                  </li>
                </ul>
                <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-950/20 rounded-md border border-amber-200/50 dark:border-amber-800/50">
                  <p className="text-xs text-amber-800 dark:text-amber-200 flex items-start gap-2">
                    <Lightbulb className="size-3 mt-0.5 flex-shrink-0" />
                    <span>
                      <strong>Tip:</strong> Schrijf alsof je een collega helpt - gebruik voorbeelden
                      en concrete situaties voor de beste resultaten.
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
