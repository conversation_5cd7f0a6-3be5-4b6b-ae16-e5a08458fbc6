import type React from "react";
import { redirect } from "next/navigation";
import { auth } from "@/auth";
import { db } from "@/db";
import { sites, organizations, orgMembers } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { TopBar } from "@/components/dashboard/top-bar";

interface SiteDashboardLayoutProps {
  children: React.ReactNode;
  params: {
    siteId: string;
  };
}

export default async function SiteDashboardLayout({ children, params }: SiteDashboardLayoutProps) {
  const session = await auth();

  if (!session?.user?.id) {
    redirect("/login");
  }

  // Get the current site
  const { siteId } = await params;
  const site = await db.query.sites.findFirst({
    where: eq(sites.id, siteId),
  });

  if (!site) {
    redirect("/dashboard");
  }

  // Get the organization for this site
  const organization = await db.query.organizations.findFirst({
    where: eq(organizations.id, site.orgId!),
  });

  if (!organization) {
    redirect("/dashboard");
  }

  // Verify user has access to this site's organization
  const membership = await db.query.orgMembers.findFirst({
    where: and(eq(orgMembers.orgId, site.orgId!), eq(orgMembers.userId, session.user.id)),
  });

  if (!membership) {
    redirect("/dashboard");
  }

  // Get all sites for this organization
  const orgSites = await db.query.sites.findMany({
    where: eq(sites.orgId, site.orgId!),
  });

  // Get all organizations for this user
  const userOrgMemberships = await db.query.orgMembers.findMany({
    where: eq(orgMembers.userId, session.user.id),
  });

  // Get the organization details for each membership
  const userOrganizations = await Promise.all(
    userOrgMemberships.map(async (membership) => {
      const org = await db.query.organizations.findFirst({
        where: eq(organizations.id, membership.orgId!),
      });
      return org;
    }),
  );

  const validOrganizations = userOrganizations.filter(
    (org): org is NonNullable<typeof org> => org !== undefined,
  );

  // TODO: Get real-time stats
  const stats = {
    activeChats: 0,
    pendingReturns: 0,
  };

  return (
    <div className="h-screen w-screen overflow-x-hidden">
      <SidebarProvider
        style={
          {
            "--sidebar-width": "16rem",
            "--sidebar-width-icon": "3rem",
          } as React.CSSProperties
        }
      >
        <AppSidebar
          siteId={siteId}
          user={{
            name: session.user.name || "User",
            email: session.user.email || "",
            avatar: session.user.image || undefined,
          }}
          stats={stats}
        />
        <SidebarInset className="flex flex-col overflow-hidden">
          <TopBar
            currentSite={{
              id: site.id,
              name: site.name,
              url: site.url,
              platform: site.platform,
              orgId: site.orgId,
            }}
            currentOrg={{
              id: organization.id,
              name: organization.name,
              slug: organization.slug,
              createdAt: organization.createdAt,
            }}
            sites={orgSites.map((s) => ({
              id: s.id,
              name: s.name,
              url: s.url,
              platform: s.platform,
              orgId: s.orgId,
            }))}
            organizations={validOrganizations.map((o) => ({
              id: o.id,
              name: o.name,
              slug: o.slug,
              createdAt: o.createdAt,
            }))}
            user={{
              name: session.user.name || "User",
              email: session.user.email || "",
              avatar: session.user.image || undefined,
            }}
          />
          <main className="flex-1 overflow-auto w-full">
            <div className="max-w-full">{children}</div>
          </main>
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}
