import { db } from "@/db";
import { sites, chatSessions, returnRequests } from "@/db/schema";
import { eq, and, desc, gte } from "drizzle-orm";
import { KPITiles } from "@/components/dashboard/kpi-tiles";
import { ActivityFeed } from "@/components/dashboard/activity-feed";
import { ActionCards } from "@/components/dashboard/action-cards";

interface DashboardHomePageProps {
  params: Promise<{
    siteId: string;
  }>;
}

export default async function DashboardHomePage({ params }: DashboardHomePageProps) {
  // Get site info
  const { siteId } = await params;
  const site = await db.query.sites.findFirst({
    where: eq(sites.id, siteId),
  });

  if (!site) {
    return <div>Site not found</div>;
  }

  // Get stats for KPI tiles
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Count open chats
  const openChats = await db.query.chatSessions.findMany({
    where: and(
      eq(chatSessions.state, "open"),
      // We need to get chats for this site through agents
    ),
  });

  // Count pending returns
  const pendingReturns = await db.query.returnRequests.findMany({
    where: and(eq(returnRequests.siteId, siteId), eq(returnRequests.status, "pending")),
  });

  // Get recent activity
  const recentReturns = await db.query.returnRequests.findMany({
    where: eq(returnRequests.siteId, siteId),
    orderBy: [desc(returnRequests.createdAt)],
    limit: 10,
  });

  // Mock data for now - in production, this would come from real queries
  const kpiData = {
    openChats: openChats.length,
    pendingReturns: pendingReturns.length,
    avgResponseTime: "2m 34s",
    csat: 4.7,
  };

  const activities = recentReturns.map((r) => ({
    id: r.id,
    type:
      r.status === "approved"
        ? ("return_approved" as const)
        : r.status === "denied"
          ? ("return_denied" as const)
          : ("return_created" as const),
    timestamp: r.createdAt!,
    title: `Retour #${r.orderNumber}`,
    description: r.reason || "Geen reden opgegeven",
    metadata: {
      customerEmail: r.customerEmail,
      status: r.status,
    },
  }));

  // Mock action cards - in production, these would be based on real conditions
  const actionCards = [
    {
      id: "1",
      priority: "urgent" as const,
      type: "vision_review" as const,
      title: "Foto beoordeling vereist",
      description: "3 retouren wachten op visuele inspectie",
      action: {
        label: "Bekijk foto's",
        href: `/dashboard/${siteId}/returns?filter=awaiting_vision`,
      },
    },
  ];

  return (
    <div className="space-y-8 p-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground mt-2">
          Welkom terug! Hier is een overzicht van {site.name}.
        </p>
      </div>

      {/* KPI Tiles */}
      <KPITiles data={kpiData} siteId={siteId} />

      {/* Action Cards */}
      {actionCards.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Acties vereist</h2>
          <ActionCards cards={actionCards} />
        </div>
      )}

      {/* Activity Feed */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Recente activiteit</h2>
        <ActivityFeed activities={activities} />
      </div>
    </div>
  );
}
