import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { db } from "@/db";
import { chatMessages, sites, orgMembers } from "@/db/schema";
import { eq, and, desc, or } from "drizzle-orm";
import { ReturnsManagement } from "@/components/returns/returns-management";
import { returnRequests } from "@/db/schema";
import { NextPage } from "next";

interface ChatMessage {
  role: "user" | "assistant" | "system";
  content: string;
  createdAt: Date | null;
}

interface ExtendedReturnRequest {
  id: string;
  siteId: string;
  orderNumber: string;
  orderShopifyId: string | null;
  customerEmail: string | null;
  customerPhone: string | null;
  reason: string | null;
  status:
    | "pending"
    | "reviewing"
    | "waiting_on_customer"
    | "waiting_on_staff"
    | "approved"
    | "denied"
    | "processing"
    | "items_received"
    | "completed"
    | "cancelled";
  requestedItems: unknown;
  staffNotes: string | null;
  shopifyReturnId: string | null;
  reviewedBy: string | null;
  reviewedAt: Date | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  sessionId: string | null;
  chatContext?: ChatMessage[] | null;
}

interface ReturnsPageParams {
  siteId: string;
}

interface ReturnsPageSearchParams {
  filter?: string;
}

interface ReturnsPageProps {
  params: Promise<ReturnsPageParams>;
  searchParams: Promise<ReturnsPageSearchParams>;
}

const ReturnsPage: NextPage<ReturnsPageProps> = async ({ params, searchParams }) => {
  const session = await auth();
  if (!session?.user?.id) redirect("/login");

  const { siteId } = await params;
  const resolvedSearchParams = await searchParams;
  const filter = resolvedSearchParams?.filter || "all";

  const site = await db.query.sites.findFirst({
    where: eq(sites.id, siteId),
  });

  if (!site) redirect("/dashboard");

  const membership = await db.query.orgMembers.findFirst({
    where: and(eq(orgMembers.orgId, site.orgId!), eq(orgMembers.userId, session.user.id)),
  });

  if (!membership) redirect("/dashboard");

  const baseCondition = eq(returnRequests.siteId, siteId);
  let whereCondition;

  if (filter === "pending") {
    whereCondition = and(baseCondition, eq(returnRequests.status, "pending"));
  } else if (filter === "reviewing") {
    whereCondition = and(
      baseCondition,
      or(
        eq(returnRequests.status, "reviewing"),
        eq(returnRequests.status, "waiting_on_customer"),
        eq(returnRequests.status, "waiting_on_staff"),
      ),
    );
  } else if (filter === "approved") {
    whereCondition = and(baseCondition, eq(returnRequests.status, "approved"));
  } else if (filter === "denied") {
    whereCondition = and(baseCondition, eq(returnRequests.status, "denied"));
  } else {
    whereCondition = baseCondition;
  }

  const returns = await db.query.returnRequests.findMany({
    where: whereCondition,
    orderBy: [desc(returnRequests.createdAt)],
    limit: 50,
  });

  const returnsWithContext: ExtendedReturnRequest[] = await Promise.all(
    returns
      .filter((returnReq) => returnReq.siteId && returnReq.status)
      .map(async (returnReq) => {
        let chatContext: ChatMessage[] | null = null;
        if (returnReq.sessionId) {
          const messages = await db.query.chatMessages.findMany({
            where: eq(chatMessages.sessionId, returnReq.sessionId),
            orderBy: [chatMessages.createdAt],
            limit: 20,
          });
          chatContext = messages.map((msg) => ({
            role: msg.role as "user" | "assistant" | "system",
            content: msg.content,
            createdAt: msg.createdAt,
          }));
        }

        return {
          ...returnReq,
          siteId: returnReq.siteId!,
          status: returnReq.status!,
          chatContext,
        };
      }),
  );

  const stats = {
    total: returns.length,
    pending: returns.filter((r) => r.status === "pending").length,
    reviewing: returns.filter(
      (r) =>
        r.status === "reviewing" ||
        r.status === "waiting_on_customer" ||
        r.status === "waiting_on_staff",
    ).length,
    approved: returns.filter((r) => r.status === "approved").length,
    denied: returns.filter((r) => r.status === "denied").length,
    processing: returns.filter((r) => r.status === "processing").length,
    items_received: returns.filter((r) => r.status === "items_received").length,
    completed: returns.filter((r) => r.status === "completed").length,
  };

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Retouren</h1>
        <p className="text-muted-foreground">Beheer en beoordeel klantretouren voor {site.name}</p>
      </div>

      <ReturnsManagement
        siteId={siteId}
        returns={returnsWithContext}
        currentFilter={filter}
        stats={stats}
        userRole={membership.role as "owner" | "admin" | "agent"}
      />
    </div>
  );
};

export default ReturnsPage;
