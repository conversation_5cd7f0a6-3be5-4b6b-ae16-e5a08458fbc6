import { notFound, redirect } from "next/navigation";
import { db } from "@/db";
import { eq, and, asc } from "drizzle-orm";
import { returnRequests, agents, chatMessages } from "@/db/schema";
import { auth } from "@/auth";
import { ReturnReviewPage } from "@/components/returns/return-review-page";

interface PageProps {
  params: Promise<{
    siteId: string;
    returnId: string;
  }>;
}

export default async function ReturnReviewPageRoute({ params }: PageProps) {
  const { siteId, returnId } = await params;
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  // Fetch return request
  const returnRequest = await db.query.returnRequests.findFirst({
    where: and(eq(returnRequests.id, returnId), eq(returnRequests.siteId, siteId)),
  });

  if (!returnRequest) {
    notFound();
  }

  // Fetch chat messages if sessionId exists
  let chatContext = null;
  if (returnRequest.sessionId) {
    const messages = await db.query.chatMessages.findMany({
      where: eq(chatMessages.sessionId, returnRequest.sessionId),
      orderBy: asc(chatMessages.createdAt),
    });
    chatContext = messages;
  }

  // Get agent name for the return
  const agent = await db.query.agents.findFirst({
    where: eq(agents.siteId, siteId),
  });

  return (
    <ReturnReviewPage
      returnRequest={{
        ...returnRequest,
        chatContext,
        status: returnRequest.status ?? "pending",
        analysisData: returnRequest.analysisData as any,
      }}
      siteId={siteId}
      agentName={agent?.name || "AI Agent"}
    />
  );
}
