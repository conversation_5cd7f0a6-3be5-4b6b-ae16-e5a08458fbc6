"use client";

import type React from "react";
import { useState, useEffect, useRef } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Loader2, Send, Sparkles } from "lucide-react";
import { motion, AnimatePresence, LayoutGroup } from "motion/react";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
}

export default function ChatPage() {
  const { uuid } = useParams();
  const searchParams = useSearchParams();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [agentId, setAgentId] = useState<string>("");
  const [agentDetails, setAgentDetails] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize agent ID from URL parameter and get/create session
  useEffect(() => {
    if (typeof uuid === "string") {
      setAgentId(uuid);
      // Check for sessionId in URL query parameters first (for return continuations)
      const urlSessionId = searchParams.get("sessionId");
      if (urlSessionId) {
        console.log("Bestaande sessie voortzetten vanaf URL:", urlSessionId);
        setSessionId(urlSessionId);
        // Also store it in localStorage for future use
        localStorage.setItem(`chat-session-${uuid}`, urlSessionId);
      } else {
        // Fall back to localStorage
        const storageKey = `chat-session-${uuid}`;
        const existingSessionId = localStorage.getItem(storageKey);
        if (existingSessionId) {
          setSessionId(existingSessionId);
        } else {
          // Session will be created on first message
          setSessionId(null);
        }
      }
    }
  }, [uuid, searchParams]);

  // Fetch agent details and chat history in parallel
  useEffect(() => {
    const fetchData = async () => {
      if (!agentId) return;

      try {
        // Always fetch agent details
        const agentResponse = await fetch(`/api/agents/${agentId}`);
        let historyResponse = null;

        if (sessionId) {
          // Only fetch history if we have a session ID
          historyResponse = await fetch(`/api/chat/${sessionId}`).catch(() => null);
        }

        // Handle agent details
        if (agentResponse.ok) {
          const agentData = await agentResponse.json();
          setAgentDetails(agentData);

          // Handle chat history
          if (historyResponse?.ok) {
            const historyData = await historyResponse.json();
            if (historyData.messages && historyData.messages.length > 0) {
              const formattedMessages = historyData.messages.map((msg: any) => ({
                id: msg.id,
                role: msg.role,
                content: msg.content,
                timestamp: new Date(msg.timestamp),
              }));
              setMessages(formattedMessages);
            } else {
              // Add welcome message if no history but agent has welcome message
              if (agentData.welcomeMessage) {
                setMessages([
                  {
                    id: "welcome",
                    role: "assistant",
                    content: agentData.welcomeMessage,
                    timestamp: new Date(),
                  },
                ]);
              }
            }
          } else {
            // Add welcome message if no session or no history exists
            if (agentData.welcomeMessage) {
              setMessages([
                {
                  id: "welcome",
                  role: "assistant",
                  content: agentData.welcomeMessage,
                  timestamp: new Date(),
                },
              ]);
            }
          }
        } else {
          console.error("Kon agent details niet ophalen");
        }
      } catch (error) {
        console.error("Fout bij ophalen data:", error);
      } finally {
        setInitialLoading(false);
      }
    };

    fetchData();
  }, [agentId, sessionId]);

  // Smooth scroll to bottom when messages change
  useEffect(() => {
    const scrollToBottom = () => {
      messagesEndRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    };

    // Small delay to ensure DOM is updated
    const timeoutId = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timeoutId);
  }, [messages]);

  // Poll for new messages from background workers
  useEffect(() => {
    if (!sessionId) return;

    const pollForMessages = async () => {
      try {
        const response = await fetch(`/api/chat/${sessionId}`);
        if (response.ok) {
          const data = await response.json();
          if (data.messages && data.messages.length > 0) {
            const formattedMessages = data.messages.map((msg: any) => ({
              id: msg.id,
              role: msg.role,
              content: msg.content,
              timestamp: new Date(msg.timestamp),
            }));

            // Only update if we have new messages
            if (formattedMessages.length > messages.length) {
              setMessages(formattedMessages);
            }
          }
        }
      } catch (error) {
        // Silently fail - don't spam console with polling errors
      }
    };

    // Poll every 2 seconds
    const interval = setInterval(pollForMessages, 2000);
    return () => clearInterval(interval);
  }, [sessionId, messages.length]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || loading) return;

    const userMessage = {
      id: `user-${Date.now()}`,
      role: "user" as const,
      content: input,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setLoading(true);

    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-from-chat-ui": "true",
        },
        body: JSON.stringify({
          message: input,
          agentId: agentId,
          sessionId: sessionId,
        }),
      });

      if (response.ok) {
        const data = await response.json();

        // Save the session ID for future messages and persist to localStorage
        if (data.sessionId && !sessionId) {
          setSessionId(data.sessionId);
          localStorage.setItem(`chat-session-${agentId}`, data.sessionId);
        }

        const assistantMessage = {
          id: `assistant-${Date.now()}`,
          role: "assistant" as const,
          content: data.response,
          timestamp: new Date(),
        };

        setMessages((prev) => [...prev, assistantMessage]);
      } else {
        console.error("Kon bericht niet verzenden");
        // Add error message
        setMessages((prev) => [
          ...prev,
          {
            id: `error-${Date.now()}`,
            role: "system" as const,
            content: "Sorry, er is een fout opgetreden bij het verwerken van je verzoek.",
            timestamp: new Date(),
          },
        ]);
      }
    } catch (error) {
      console.error("Fout bij verzenden bericht:", error);
      // Add error message
      setMessages((prev) => [
        ...prev,
        {
          id: `error-${Date.now()}`,
          role: "system" as const,
          content: "Sorry, er is een fout opgetreden bij het verbinden met de server.",
          timestamp: new Date(),
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="h-screen w-screen bg-gradient-to-br from-slate-50 via-white to-slate-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex flex-col items-center gap-6"
        >
          <div className="relative">
            <div className="w-12 h-12 border-3 border-slate-200 border-t-primary rounded-full animate-spin" />
            <div
              className="absolute inset-0 w-12 h-12 border-3 border-transparent border-r-primary rounded-full animate-spin animate-reverse"
              style={{ animationDuration: "1.5s" }}
            />
          </div>
          <div className="text-center">
            <p className="text-slate-700 font-medium">Chat wordt geladen...</p>
            <p className="text-slate-500 text-sm mt-1">Even geduld alsjeblieft</p>
          </div>
        </motion.div>
      </div>
    );
  }

  const getAgentInitials = (name: string) => {
    return (
      name
        ?.split(" ")
        .map((word) => word[0])
        .join("")
        .toUpperCase()
        .slice(0, 2) || "AI"
    );
  };

  return (
    <div className="h-screen w-screen bg-gradient-to-br from-slate-50 via-white to-slate-50 flex flex-col">
      {/* Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="bg-white/80 backdrop-blur-xl border-b border-slate-200/60 px-4 py-3 flex-shrink-0 shadow-sm"
      >
        <div className="flex items-center gap-3">
          <div className="relative">
            <Avatar className="w-10 h-10 ring-2 ring-indigo-100 shadow-sm">
              <AvatarFallback className="bg-primary text-white font-semibold text-sm">
                {getAgentInitials(agentDetails?.name || "AI")}
              </AvatarFallback>
            </Avatar>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3 }}
              className="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-emerald-500 rounded-full border-2 border-white shadow-sm"
            />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-0.5">
              <div className="text-slate-600 text-xs font-medium">👋 Hoi, ik ben</div>
            </div>
            <div className="text-slate-900 font-semibold text-base leading-tight truncate">
              {agentDetails?.name || "AI Assistent"}
            </div>
            <div className="flex items-center gap-2 mt-0.5">
              <div className="flex items-center gap-1.5">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                  className="w-1.5 h-1.5 bg-emerald-500 rounded-full"
                />
                <span className="text-slate-500 text-xs font-medium">Online</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Chat Content */}
      <div className="flex flex-col flex-1 min-h-0">
        {/* Messages */}
        <div className="flex-1 p-4 overflow-y-auto">
          <LayoutGroup>
            <div className="space-y-4 max-w-full">
              <AnimatePresence mode="popLayout">
                {messages.map((msg, index) => (
                  <motion.div
                    key={msg.id}
                    layout
                    initial={{ opacity: 0, y: 20, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95, transition: { duration: 0.2 } }}
                    transition={{
                      duration: 0.3,
                      ease: "easeOut",
                      layout: { duration: 0.2 },
                    }}
                    className={`flex ${msg.role === "user" ? "justify-end" : "justify-start"}`}
                  >
                    {msg.role === "user" ? (
                      <div className="max-w-[85%] group">
                        <motion.div
                          className="bg-slate-900 text-white rounded-2xl rounded-tr-md px-4 py-3 shadow-sm"
                          transition={{ duration: 0.2 }}
                        >
                          <p className="text-sm leading-relaxed">{msg.content}</p>
                        </motion.div>
                        <div className="text-xs text-slate-400 text-right mt-1.5 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          {msg.timestamp.toLocaleTimeString("nl-NL", {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </div>
                      </div>
                    ) : (
                      <div className="max-w-[85%] group">
                        <motion.div
                          className={`rounded-2xl rounded-tl-md px-4 py-3 ${
                            msg.role === "system"
                              ? "bg-red-50 text-red-700 border border-red-200"
                              : "bg-slate-50 text-slate-800 border border-slate-200"
                          }`}
                          transition={{ duration: 0.2 }}
                        >
                          <p className="text-sm leading-relaxed">{msg.content}</p>
                        </motion.div>
                        <div className="text-xs text-slate-400 ml-1 mt-1.5 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          {msg.timestamp.toLocaleTimeString("nl-NL", {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Typing Indicator */}
              <AnimatePresence>
                {loading && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                    className="flex justify-start mb-4"
                  >
                    <div className="bg-slate-50 border border-slate-200 rounded-2xl rounded-tl-md px-4 py-3">
                      <div className="flex items-center gap-1">
                        {[0, 1, 2].map((i) => (
                          <motion.div
                            key={i}
                            animate={{
                              scale: [1, 1.4, 1],
                              opacity: [0.4, 1, 0.4],
                            }}
                            transition={{
                              duration: 1.2,
                              repeat: Number.POSITIVE_INFINITY,
                              delay: i * 0.2,
                              ease: "easeInOut",
                            }}
                            className="w-2 h-2 bg-slate-400 rounded-full"
                          />
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </LayoutGroup>
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="px-4 pb-4 flex-shrink-0"
        >
          <form onSubmit={handleSubmit} className="relative">
            <div className="relative bg-white border border-slate-200 rounded-xl focus-within:border-slate-400 focus-within:ring-2 focus-within:ring-slate-400/20 transition-all duration-200 shadow-sm">
              <Textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Typ je bericht hier..."
                className="min-h-[48px] max-h-[120px] pr-14 resize-none border-0 rounded-xl text-sm placeholder:text-slate-400 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent"
                maxLength={1000}
                disabled={loading}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e);
                  }
                }}
              />
              <Button
                type="submit"
                size="sm"
                disabled={loading || !input.trim()}
                className="absolute bottom-2 right-2 bg-slate-900 hover:bg-slate-800 w-8 h-8 p-0 rounded-lg text-white shadow-sm disabled:opacity-50 transition-all duration-200 hover:scale-105 active:scale-95"
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </Button>
            </div>
          </form>
        </motion.div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="px-4 py-2 border-t border-slate-100 flex-shrink-0 bg-white/50 backdrop-blur-sm"
        >
          <div className="flex items-center justify-center gap-2 text-xs text-slate-500">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
              className="w-1.5 h-1.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"
            />
            <span className="font-medium">Mogelijk gemaakt door Nousu AI</span>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
