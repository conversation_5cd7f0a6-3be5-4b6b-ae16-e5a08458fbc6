import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import "./App.css";

type Channel = "chat" | "form" | "phone" | "whatsapp";

function App() {
  const [currentChannel, setCurrentChannel] = useState<Channel>("chat");
  const [message, setMessage] = useState("");

  return (
    <div className="h-screen w-screen bg-white flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-[#7e79f0] px-4 py-3 relative flex-shrink-0">
        {/* Menu dots */}
        <div className="absolute left-4 top-4">
          <svg width="16" height="4" viewBox="0 0 16 4" fill="none" className="text-white">
            <circle cx="2" cy="2" r="1.5" fill="currentColor" />
            <circle cx="8" cy="2" r="1.5" fill="currentColor" />
            <circle cx="14" cy="2" r="1.5" fill="currentColor" />
          </svg>
        </div>

        {/* Header Content */}
        <div className="flex items-center gap-3 ml-8">
          <div className="relative">
            <Avatar className="w-12 h-12">
              <AvatarFallback className="bg-white/20 text-white text-lg font-medium">
                S
              </AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white" />
          </div>

          <div className="flex-1">
            <div className="text-white">
              <div className="text-sm">Hoi 👋🏻 ik ben</div>
              <div className="font-semibold text-base">Sophie</div>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-white/90 text-xs">online in chat</span>
              <Badge
                variant="secondary"
                className="bg-white/20 text-white text-xs px-2 py-1 h-auto font-medium"
              >
                AI
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Content - Takes remaining space */}
      {currentChannel === "chat" && (
        <div className="flex flex-col flex-1 min-h-0">
          {/* Messages - Grows to fill available space */}
          <div className="flex-1 p-4 overflow-y-auto message-area">
            {/* Sophie's welcome message */}
            <div className="mb-4">
              <div className="bg-gray-50 rounded-2xl rounded-tl-md p-4 max-w-[85%] mb-2">
                <p className="text-sm text-gray-800 leading-relaxed">
                  Welkom! Deel je website met me en dan personaliseer ik binnen 5 minuten een demo
                  AI agent voor je 😁
                </p>
              </div>
              <div className="text-xs text-gray-500 ml-1">08:59 AM</div>
            </div>

            {/* User message */}
            <div className="mb-4 flex justify-end">
              <div className="max-w-[85%]">
                <div className="bg-[#7e79f0] text-white rounded-2xl rounded-tr-md p-4 mb-2">
                  <p className="text-sm leading-relaxed">hallo</p>
                </div>
                <div className="text-xs text-gray-500 text-right mr-1">11:07 AM</div>
              </div>
            </div>

            {/* Sophie's response */}
            <div className="mb-4">
              <div className="bg-gray-50 rounded-2xl rounded-tl-md p-4 max-w-[85%] mb-2">
                <p className="text-sm text-gray-800 leading-relaxed">
                  Hallo! Hoe kan ik je helpen vandaag? Heb je vragen over Sleak of onze AI chat
                  widget? Laat het me weten, ik sta klaar om je te helpen! Als je een chatbot
                  service wilt, stuur dan je e-mailadres en website URL, zodat ik een demo voor je
                  kan maken.
                </p>
              </div>
              <div className="text-xs text-gray-500 ml-1">11:07 AM</div>
            </div>

            {/* Another Sophie message */}
            <div className="mb-4">
              <div className="bg-gray-50 rounded-2xl rounded-tl-md p-4 max-w-[85%] mb-2">
                <p className="text-sm text-gray-800 leading-relaxed">
                  Hallo daar! Heb je een specifieke vraag over Sleak of onze AI chat widget? Laat
                  het me weten, ik sta klaar om je te helpen! Als je een chatbot service wilt, stuur
                  dan je e-mailadres en website URL, zodat ik een demo voor je kan maken.
                </p>
              </div>
              <div className="text-xs text-gray-500 ml-1">11:07 AM</div>
            </div>
          </div>

          {/* Input Area - Fixed at bottom */}
          <div className="px-4 pb-4 bg-white flex-shrink-0">
            <div className="relative">
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Typ hier je bericht…"
                className="min-h-[50px] max-h-[100px] pr-14 resize-none border-gray-200 rounded-xl text-sm"
                maxLength={1000}
              />
              <Button
                size="sm"
                className="absolute bottom-2 right-2 bg-[#7e79f0] hover:bg-[#6b66e0] w-10 h-8 p-0 rounded-lg"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M2 8l12-6-4 6 4 6-12-6z" fill="currentColor" />
                </svg>
              </Button>
            </div>
          </div>

          {/* Footer Branding */}
          <div className="px-4 py-3 bg-gray-50/50 border-t border-gray-100 flex-shrink-0">
            <a
              href="https://www.sleak.chat"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-xs text-gray-600 hover:text-gray-800 transition-colors"
            >
              <img
                src="https://ext.same-assets.com/2417750198/2709408737.png"
                alt="Sleak"
                className="w-3 h-3"
              />
              Wij werken met Sleak.chat
            </a>
          </div>
        </div>
      )}

      {/* Email Form */}
      {currentChannel === "form" && (
        <div className="flex flex-col flex-1 min-h-0">
          <div className="p-4 flex-1 overflow-y-auto">
            <div className="mb-4">
              <p className="text-sm text-gray-800 leading-relaxed">
                Stuur eenvoudig een e-mail en we nemen zo snel mogelijk contact met je op!
              </p>
            </div>

            <div className="space-y-4 max-w-md">
              <div>
                <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                  Naam
                </Label>
                <Input id="name" className="mt-2 rounded-lg border-gray-200" />
              </div>
              <div>
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                  E-mailadres
                </Label>
                <Input id="email" type="email" className="mt-2 rounded-lg border-gray-200" />
              </div>
              <div>
                <Label htmlFor="message" className="text-sm font-medium text-gray-700">
                  Bericht
                </Label>
                <Textarea id="message" className="mt-2 min-h-[100px] rounded-lg border-gray-200" />
              </div>
              <Button className="w-full bg-[#7e79f0] hover:bg-[#6b66e0] rounded-lg py-3 text-sm font-medium">
                E-mail sturen
              </Button>
            </div>
          </div>

          {/* Footer Branding */}
          <div className="px-4 py-3 bg-gray-50/50 border-t border-gray-100 flex-shrink-0">
            <a
              href="https://www.sleak.chat"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-xs text-gray-600 hover:text-gray-800 transition-colors"
            >
              <img
                src="https://ext.same-assets.com/2417750198/2709408737.png"
                alt="Sleak"
                className="w-3 h-3"
              />
              Wij werken met Sleak.chat
            </a>
          </div>
        </div>
      )}

      {/* Phone Contact */}
      {currentChannel === "phone" && (
        <div className="flex flex-col flex-1 min-h-0">
          <div className="p-4 flex-1 overflow-y-auto">
            <div className="mb-6">
              <p className="text-sm text-gray-800 leading-relaxed">
                Bel ons tijdens reguliere kantoortijden via de onderstaande knop 😁
              </p>
            </div>

            <div className="space-y-3 max-w-md">
              <Button className="w-full bg-[#7e79f0] hover:bg-[#6b66e0] rounded-lg py-4 text-base font-medium">
                0850608199
              </Button>
              <Button className="w-full bg-[#7e79f0] hover:bg-[#6b66e0] rounded-lg py-4 text-base font-medium">
                Bel 0850608199
              </Button>
            </div>
          </div>

          {/* Footer Branding */}
          <div className="px-4 py-3 bg-gray-50/50 border-t border-gray-100 flex-shrink-0">
            <a
              href="https://sleak.chat/"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-xs text-gray-600 hover:text-gray-800 transition-colors"
            >
              <img
                src="https://ext.same-assets.com/2417750198/2709408737.png"
                alt="Sleak"
                className="w-3 h-3"
              />
              Wij werken met Sleak.chat
            </a>
          </div>
        </div>
      )}

      {/* WhatsApp Contact */}
      {currentChannel === "whatsapp" && (
        <div className="flex flex-col flex-1 min-h-0">
          <div className="p-4 flex-1 overflow-y-auto">
            <div className="mb-6">
              <p className="text-sm text-gray-800 leading-relaxed">
                Liever contact via WhatsApp? Schakel eenvoudig over via de paarse knop!
              </p>
            </div>

            <div className="space-y-3 max-w-md">
              <Button className="w-full bg-[#7e79f0] hover:bg-[#6b66e0] rounded-lg py-4 text-base font-medium">
                WhatsApp bericht sturen
              </Button>
            </div>
          </div>

          {/* Footer Branding */}
          <div className="px-4 py-3 bg-gray-50/50 border-t border-gray-100 flex-shrink-0">
            <a
              href="https://sleak.chat/"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-xs text-gray-600 hover:text-gray-800 transition-colors"
            >
              <img
                src="https://ext.same-assets.com/2417750198/2709408737.png"
                alt="Sleak"
                className="w-3 h-3"
              />
              Wij werken met Sleak.chat
            </a>
          </div>
        </div>
      )}

      {/* Demo Controls - Fixed bottom overlay */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg p-3 border z-50">
        <div className="text-xs text-gray-500 mb-2 text-center">Demo Controls:</div>
        <div className="flex gap-1">
          <Button
            variant={currentChannel === "chat" ? "default" : "outline"}
            size="sm"
            onClick={() => setCurrentChannel("chat")}
            className="text-xs"
          >
            Chat
          </Button>
          <Button
            variant={currentChannel === "form" ? "default" : "outline"}
            size="sm"
            onClick={() => setCurrentChannel("form")}
            className="text-xs"
          >
            Form
          </Button>
          <Button
            variant={currentChannel === "phone" ? "default" : "outline"}
            size="sm"
            onClick={() => setCurrentChannel("phone")}
            className="text-xs"
          >
            Phone
          </Button>
          <Button
            variant={currentChannel === "whatsapp" ? "default" : "outline"}
            size="sm"
            onClick={() => setCurrentChannel("whatsapp")}
            className="text-xs"
          >
            WhatsApp
          </Button>
        </div>
      </div>
    </div>
  );
}

export default App;
