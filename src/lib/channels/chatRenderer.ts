import { ChatOpenAI } from "@langchain/openai";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { StringOutputParser } from "@langchain/core/output_parsers";
import type { <PERSON><PERSON>enderer, RenderingContext } from "@/lib/types/agent";

/**
 * Chat channel renderer for live chat conversations
 * Optimized for short, informal, conversational responses
 */
export class ChatRenderer implements ChannelRenderer {
  async render(context: RenderingContext): Promise<string> {
    const {
      action,
      toolResult,
      userMessage,
      agentName,
      siteName,
      conversationHistory,
      policyConfig,
    } = context;

    // Check if this is the first assistant message (greeting logic)
    const hasAssistantSpoken =
      context.hasAssistantSpoken ||
      conversationHistory?.some((msg) => msg.role === "assistant") ||
      false;

    const conversationHistoryStr =
      conversationHistory
        ?.map((msg) => `${msg.role === "user" ? "Klant" : "Agent"}: ${msg.content}`)
        .join("\n") || "Geen eerdere berichten";

    // If this was a plan that required human input, generate a question
    if (action === "ask_question") {
      return this.renderQuestion(context, hasAssistantSpoken, conversationHistoryStr);
    }

    // Handle tool results
    if (toolResult) {
      return this.renderToolResponse(context, hasAssistantSpoken, conversationHistoryStr);
    }

    // Fallback to general response
    return this.renderGeneralResponse(context, hasAssistantSpoken, conversationHistoryStr);
  }

  private async renderQuestion(
    context: RenderingContext,
    hasAssistantSpoken: boolean,
    conversationHistoryStr: string,
  ): Promise<string> {
    const { userMessage, agentName, siteName, policyConfig } = context;

    const questionPrompt = ChatPromptTemplate.fromTemplate(`
You are {agentName}, a customer service agent for {siteName}.
{personality}

CONVERSATION CONTEXT:
- This is {messageType} message in the conversation
- Previous conversation: {conversationHistory}

EMOTIONAL INTELLIGENCE GUIDELINES:
1. GREETING LOGIC: {greetingInstruction}
2. EMPATHY DETECTION: If the user expresses uncertainty, doubt, worry, or negative sentiment, start with an empathic sentence that acknowledges their feelings
3. SPECIFIC CHECK-BACKS: End with a specific, targeted question or offer related to their ask instead of generic closings
4. NO REDUNDANCY: Never repeat information already shared in the conversation

You need to ask the user for more information to help them better.

USER'S MESSAGE: {message}

Generate a helpful, conversational question in Dutch asking for the missing information that follows the emotional intelligence guidelines.
Keep it short and chat-friendly.
`);

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });
    const chain = questionPrompt.pipe(model).pipe(new StringOutputParser());

    return await chain.invoke({
      personality: policyConfig?.personalityPrompt || "Be helpful and friendly",
      message: userMessage,
      agentName: agentName,
      siteName: siteName,
      messageType: hasAssistantSpoken ? "een vervolg" : "het eerste",
      greetingInstruction: hasAssistantSpoken
        ? "DO NOT greet again - continue the conversation naturally"
        : "You may greet naturally if appropriate",
      conversationHistory: conversationHistoryStr,
    });
  }

  private async renderToolResponse(
    context: RenderingContext,
    hasAssistantSpoken: boolean,
    conversationHistoryStr: string,
  ): Promise<string> {
    const { action, toolResult, userMessage, agentName, siteName, policyConfig } = context;

    const responsePrompt = ChatPromptTemplate.fromTemplate(`
You are {agentName}, a customer service agent for {siteName}.
{personality}

CONVERSATION CONTEXT:
- This is {messageType} message in the conversation
- Previous conversation: {conversationHistory}

EMOTIONAL INTELLIGENCE GUIDELINES:
1. GREETING LOGIC: {greetingInstruction}
2. EMPATHY DETECTION: If the user expressed uncertainty, doubt, worry, or negative sentiment, acknowledge their feelings appropriately
3. SPECIFIC CHECK-BACKS: End with a specific, targeted question or offer related to their situation instead of generic closings like "Laat het me weten!"
4. NO REDUNDANCY: Never repeat information already shared in the conversation

You just performed an action to help a user. Now, formulate a friendly, natural response in Dutch for CHAT.

USER'S MESSAGE: {message}
YOUR ACTION: You used the tool '{tool_name}' with parameters {tool_params}.
RESULT OF ACTION: {tool_result}

Based on the result, give a clear and helpful CHAT response to the user that:
- If the result has status "processing", return the message from the result (this is a loading message)
- If the result has status "error", apologize and return the error message  
- If the action was successful, confirm what you've done and provide the relevant information
- Keep it conversational and chat-friendly (short sentences, informal tone)
- Always respond in Dutch, be friendly and helpful
- Follow the emotional intelligence guidelines above
- Use "je/jij" (informal) instead of "u" (formal)
`);

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });
    const chain = responsePrompt.pipe(model).pipe(new StringOutputParser());

    return await chain.invoke({
      personality: policyConfig?.personalityPrompt || "Be helpful and friendly",
      message: userMessage,
      tool_name: action || "none",
      tool_params: JSON.stringify(context.toolResult?.params || {}),
      tool_result: JSON.stringify(toolResult),
      agentName: agentName,
      siteName: siteName,
      messageType: hasAssistantSpoken ? "een vervolg" : "het eerste",
      greetingInstruction: hasAssistantSpoken
        ? "DO NOT greet again - continue the conversation naturally"
        : "You may greet naturally if appropriate",
      conversationHistory: conversationHistoryStr,
    });
  }

  private async renderGeneralResponse(
    context: RenderingContext,
    hasAssistantSpoken: boolean,
    conversationHistoryStr: string,
  ): Promise<string> {
    const { userMessage, agentName, siteName, policyConfig } = context;

    const generalPrompt = ChatPromptTemplate.fromTemplate(`
You are {agentName}, a customer service agent for {siteName}.
{personality}

CONVERSATION CONTEXT:
- This is {messageType} message in the conversation
- Previous conversation: {conversationHistory}

EMOTIONAL INTELLIGENCE GUIDELINES:
1. GREETING LOGIC: {greetingInstruction}
2. EMPATHY DETECTION: If the user expresses uncertainty, doubt, worry, or negative sentiment, start with an empathic sentence that acknowledges their feelings
3. SPECIFIC CHECK-BACKS: End with a specific, targeted question or offer related to their ask
4. NO REDUNDANCY: Never repeat information already shared in the conversation

Generate a helpful chat response in Dutch for this message: {message}

Keep it conversational, friendly, and chat-appropriate (short and informal).
`);

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });
    const chain = generalPrompt.pipe(model).pipe(new StringOutputParser());

    return await chain.invoke({
      personality: policyConfig?.personalityPrompt || "Be helpful and friendly",
      message: userMessage,
      agentName: agentName,
      siteName: siteName,
      messageType: hasAssistantSpoken ? "een vervolg" : "het eerste",
      greetingInstruction: hasAssistantSpoken
        ? "DO NOT greet again - continue the conversation naturally"
        : "You may greet naturally if appropriate",
      conversationHistory: conversationHistoryStr,
    });
  }
}
