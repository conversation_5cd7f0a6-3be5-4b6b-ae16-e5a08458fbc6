import type { Channel, <PERSON><PERSON><PERSON>er } from "@/lib/types/agent";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./chatRenderer";
import { <PERSON>ail<PERSON>enderer } from "./emailRenderer";

/**
 * Factory for creating channel-specific renderers
 * Allows dynamic instantiation of renderers per channel type
 */
export class ChannelFactory {
  /**
   * Create a renderer for the specified channel
   */
  static create(channel: Channel): ChannelRenderer {
    switch (channel) {
      case "chat":
        return new Chat<PERSON>enderer();
      case "email":
        return new EmailRenderer();
      case "whatsapp":
        // TODO: Implement WhatsAppRenderer in Phase 4
        throw new Error("WhatsApp channel not yet implemented");
      default:
        throw new Error(`Unknown channel: ${channel}`);
    }
  }

  /**
   * Check if a channel is supported
   */
  static isSupported(channel: Channel): boolean {
    return ["chat", "email"].includes(channel);
    // TODO: Add whatsapp when implemented
    // return ["chat", "email", "whatsapp"].includes(channel);
  }

  /**
   * Get all supported channels
   */
  static getSupportedChannels(): Channel[] {
    return ["chat", "email"];
    // TODO: Add whatsapp when implemented
    // return ["chat", "email", "whatsapp"];
  }
}
