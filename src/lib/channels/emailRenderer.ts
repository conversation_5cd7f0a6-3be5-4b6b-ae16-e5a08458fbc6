import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { StringOutputParser } from "@langchain/core/output_parsers";
import type { <PERSON><PERSON><PERSON>er, RenderingContext } from "@/lib/types/agent";

/**
 * Email channel renderer for email conversations
 * Optimized for complete, professional email responses that wait for all tool calls to finish
 */
export class EmailRenderer implements ChannelRenderer {
  async render(context: RenderingContext): Promise<string> {
    const {
      action,
      toolResult,
      userMessage,
      agentName,
      siteName,
      conversationHistory,
      policyConfig,
    } = context;

    const conversationHistoryStr =
      conversationHistory
        ?.map((msg) => `${msg.role === "user" ? "Klant" : "Agent"}: ${msg.content}`)
        .join("\n") || "Geen eerdere berichten";

    // For email channel, we NEVER send "processing" messages
    // If the tool result has status "processing", we skip this response and wait for completion
    if (toolResult?.status === "processing") {
      // Return a placeholder that indicates we're still processing
      // This should not be sent as an email - the system should wait for the final result
      return "PROCESSING_PLACEHOLDER_DO_NOT_SEND";
    }

    // If this was a plan that required human input, generate a question
    if (action === "ask_question") {
      return this.renderEmailQuestion(context, conversationHistoryStr);
    }

    // Handle completed tool results
    if (toolResult && toolResult.status !== "processing") {
      return this.renderEmailToolResponse(context, conversationHistoryStr);
    }

    // Fallback to general email response
    return this.renderGeneralEmailResponse(context, conversationHistoryStr);
  }

  private async renderEmailQuestion(
    context: RenderingContext,
    conversationHistoryStr: string,
  ): Promise<string> {
    const { userMessage, agentName, siteName, policyConfig } = context;

    const emailQuestionPrompt = ChatPromptTemplate.fromTemplate(`
You are {agentName}, a customer service agent for {siteName}.
{personality}

CONVERSATION CONTEXT:
- Previous conversation: {conversationHistory}

EMAIL GUIDELINES:
1. Write a complete, professional email response in Dutch
2. Use proper email greeting ("Beste klant,")
3. Address their question/concern directly
4. Ask for the missing information clearly
5. Use professional but friendly tone (je/jij)
6. End with proper email closing

USER'S EMAIL: {message}

Generate a complete email response in Dutch asking for the missing information needed to help them.
`);

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1});
    const chain = emailQuestionPrompt.pipe(model).pipe(new StringOutputParser());

    return await chain.invoke({
      personality: policyConfig?.personalityPrompt || "Be helpful and professional",
      message: userMessage,
      agentName: agentName,
      siteName: siteName,
      conversationHistory: conversationHistoryStr,
    });
  }

  private async renderEmailToolResponse(
    context: RenderingContext,
    conversationHistoryStr: string,
  ): Promise<string> {
    const { action, toolResult, userMessage, agentName, siteName, policyConfig } = context;

    const emailResponsePrompt = ChatPromptTemplate.fromTemplate(`
You are {agentName}, a customer service agent for {siteName}.
{personality}

CONVERSATION CONTEXT:
- Previous conversation: {conversationHistory}

EMAIL GUIDELINES:
1. Write ONLY the email body content (no "Beste klant," greeting - this will be added by the email template)
2. Write in professional Dutch email style - complete sentences and paragraphs
3. Use minimal or no emojis (maximum 1 emoji if really appropriate)
4. Don't use chat-style greetings like "Hoi!" - start directly with the response
5. Provide complete, helpful information based on the tool results
6. Use professional but friendly tone (je/jij)  
7. End naturally without "Met vriendelijke groet" (email template handles this)
8. Never mention "processing" or "loading" - provide final answers only

USER'S EMAIL: {message}
YOUR ACTION: You used the tool '{tool_name}' with parameters {tool_params}.
RESULT OF ACTION: {tool_result}

Generate ONLY the email body content in Dutch that:
- Starts directly with the answer (no greetings)
- Addresses their question with complete information from the tool result
- If the result has status "error", apologize professionally and explain what went wrong
- Uses professional email language (not chat language)
- Provides all relevant details clearly
- Ends naturally without formal closing
`);

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });
    const chain = emailResponsePrompt.pipe(model).pipe(new StringOutputParser());

    return await chain.invoke({
      personality: policyConfig?.personalityPrompt || "Be helpful and professional",
      message: userMessage,
      tool_name: action || "none",
      tool_params: JSON.stringify(toolResult?.params || {}),
      tool_result: JSON.stringify(toolResult),
      agentName: agentName,
      siteName: siteName,
      conversationHistory: conversationHistoryStr,
    });
  }

  private async renderGeneralEmailResponse(
    context: RenderingContext,
    conversationHistoryStr: string,
  ): Promise<string> {
    const { userMessage, agentName, siteName, policyConfig } = context;

    const emailGeneralPrompt = ChatPromptTemplate.fromTemplate(`
You are {agentName}, a customer service agent for {siteName}.
{personality}

CONVERSATION CONTEXT:
- Previous conversation: {conversationHistory}

EMAIL GUIDELINES:
1. Write a complete, professional email response in Dutch
2. Use proper email greeting ("Beste klant,")
3. Address their question/concern as completely as possible
4. Use professional but friendly tone (je/jij)
5. End with proper email closing

Generate a helpful email response in Dutch for this message: {message}

Keep it professional, complete, and email-appropriate.
`);

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });
    const chain = emailGeneralPrompt.pipe(model).pipe(new StringOutputParser());

    return await chain.invoke({
      personality: policyConfig?.personalityPrompt || "Be helpful and professional",
      message: userMessage,
      agentName: agentName,
      siteName: siteName,
      conversationHistory: conversationHistoryStr,
    });
  }
}
