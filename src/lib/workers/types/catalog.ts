export interface ProductTextEmbedPayload {
  productId: string;
  siteId: string; // For tenant isolation
  content: string;
  checksum: string;
  jobType: "product_text_embed";
}

export interface ProductImageEmbedPayload {
  productId: string;
  siteId: string; // For tenant isolation
  imageUrl: string;
  position: number;
  checksum: string;
  jobType: "product_image_embed";
}

export type CatalogJobPayload = ProductTextEmbedPayload | ProductImageEmbedPayload;
