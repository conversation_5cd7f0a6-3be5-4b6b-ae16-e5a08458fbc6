import { db } from "@/db";
import { catalogProducts, sites } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { queueJob } from "@/lib/workers/job-queue";
import crypto from "crypto";

interface WebhookPayload {
  rawBody: string;
  headers: Record<string, string>;
  jobType: "process_shopify_webhook";
  siteId: string;
}

/**
 * Process Shopify webhook asynchronously
 * This runs in the background after the webhook endpoint has already responded
 */
export async function processShopifyWebhook(payload: WebhookPayload) {
  const { rawBody, headers, siteId } = payload;

  try {
    const topic = headers["x-shopify-topic"];
    const shopDomain = headers["x-shopify-shop-domain"];

    console.log(`🔄 Processing webhook: ${topic} for shop ${shopDomain}`);

    const webhookData = JSON.parse(rawBody);

    // Get site info
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, siteId),
    });

    if (!site) {
      throw new Error(`Site not found: ${siteId}`);
    }

    switch (topic) {
      case "products/create":
      case "products/update": {
        // Upsert product to catalog
        const product = {
          siteId: site.id,
          shopifyProductId: String(webhookData.id),
          title: webhookData.title || "",
          description: webhookData.body_html || "",
          price: webhookData.variants?.[0]?.price || "0",
          vendor: webhookData.vendor || "",
          tags: webhookData.tags || "",
          updatedAt: new Date(),
        };

        await db
          .insert(catalogProducts)
          .values(product)
          .onConflictDoUpdate({
            target: [catalogProducts.siteId, catalogProducts.shopifyProductId],
            set: product,
          });

        // Get the product ID for embedding jobs
        const savedProduct = await db.query.catalogProducts.findFirst({
          where: and(
            eq(catalogProducts.siteId, site.id),
            eq(catalogProducts.shopifyProductId, String(webhookData.id)),
          ),
        });

        if (!savedProduct) {
          throw new Error("Failed to save product");
        }

        // Queue text embedding job with rate limiting
        await queueJob({
          type: "product_text_embed",
          payload: {
            productId: savedProduct.id,
            siteId: site.id,
            content: `${webhookData.title} ${webhookData.body_html} ${webhookData.tags}`,
            checksum: crypto
              .createHash("md5")
              .update(`${webhookData.title}${webhookData.body_html}${webhookData.tags}`)
              .digest("hex"),
            jobType: "product_text_embed",
          },
          // Add delay to respect rate limits
          delay: 1, // 1 second delay for text embeddings
        });

        // Queue image embedding jobs with staggered delays
        const images = webhookData.images || [];
        for (let i = 0; i < images.length; i++) {
          const image = images[i];
          await queueJob({
            type: "product_image_embed",
            payload: {
              productId: savedProduct.id,
              siteId: site.id,
              imageUrl: image.src,
              position: image.position || i,
              checksum: crypto.createHash("md5").update(image.src).digest("hex"),
              jobType: "product_image_embed",
            },
            // Stagger image embeddings more to avoid rate limits
            delay: 5 + i * 3, // Start at 5s, then 8s, 11s, etc.
          });
        }

        console.log(
          `✅ Processed ${topic} webhook for product ${savedProduct.id} with ${images.length} images`,
        );
        break;
      }

      case "products/delete": {
        // Delete product (CASCADE will handle embeds & images)
        await db
          .delete(catalogProducts)
          .where(
            and(
              eq(catalogProducts.siteId, site.id),
              eq(catalogProducts.shopifyProductId, String(webhookData.id)),
            ),
          );

        console.log(`✅ Deleted product ${webhookData.id} for site ${site.id}`);
        break;
      }

      // Other webhook topics can be handled here
      case "orders/create":
      case "orders/updated":
      case "orders/fulfilled":
      case "orders/cancelled":
        console.log(`📦 Order webhook: ${topic} - not implemented yet`);
        break;

      default:
        console.warn(`⚠️ Unhandled webhook topic: ${topic}`);
    }

    return {
      success: true,
      topic,
      shopDomain,
      processed: true,
    };
  } catch (error) {
    console.error("Webhook processing error:", error);
    throw error;
  }
}
