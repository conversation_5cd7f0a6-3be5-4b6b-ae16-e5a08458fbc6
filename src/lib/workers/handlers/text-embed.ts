import { OpenA<PERSON> } from "openai";
import { db } from "@/db";
import { catalogTextEmbeds } from "@/db/schema";
import { eq } from "drizzle-orm";
import { redis } from "@/lib/upstash";
import { waitForRateLimit, RateLimiter } from "@/lib/rate-limiter";
import type { ProductTextEmbedPayload } from "../types/catalog";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

export async function processTextEmbedding(payload: ProductTextEmbedPayload) {
  const startTime = Date.now();
  const { productId, siteId, content, checksum } = payload;

  try {
    // Check if embedding exists with same checksum (skip if unchanged)
    const cacheKey = `embed:text:${productId}:checksum`;
    const cachedChecksum = await redis.get(cacheKey);

    if (cachedChecksum === checksum) {
      console.log(`Text embedding unchanged for product ${productId}, skipping`);
      return { skipped: true, reason: "checksum_match" };
    }

    // Wait for rate limit before calling OpenAI
    await waitForRateLimit(`openai:embeddings:${siteId}`, RateLimiter.limits.openaiEmbeddings);

    // Generate embedding with text-embedding-3-large (1536 dims)
    const response = await openai.embeddings.create({
      model: "text-embedding-3-large",
      input: content.slice(0, 8000), // Limit input length
    });

    const embedding = response.data[0].embedding;

    // Upsert embedding to database
    await db
      .insert(catalogTextEmbeds)
      .values({
        productId,
        embedding: embedding as any, // pgvector handles array conversion
        updatedAt: new Date(),
      })
      .onConflictDoUpdate({
        target: [catalogTextEmbeds.productId],
        set: {
          embedding: embedding as any,
          updatedAt: new Date(),
        },
      });

    // Update checksum cache (expire in 30 days)
    await redis.set(cacheKey, checksum, { ex: 2592000 });

    const duration = Date.now() - startTime;
    console.log(`Text embedding completed for product ${productId} in ${duration}ms`);

    return {
      success: true,
      productId,
      duration,
      dimensions: embedding.length,
    };
  } catch (error) {
    console.error(`Text embedding failed for product ${productId}:`, error);
    throw error;
  }
}
