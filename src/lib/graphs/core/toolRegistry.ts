import type { Platform, PlatformToolset } from "@/lib/types/agent";

/**
 * Registry for platform-specific toolsets
 * Allows dynamic registration and retrieval of tools per platform
 */
export class ToolRegistry {
  private static toolsets: Map<Platform, PlatformToolset> = new Map();

  /**
   * Register a toolset for a specific platform
   */
  static register(platform: Platform, toolset: PlatformToolset): void {
    this.toolsets.set(platform, toolset);
  }

  /**
   * Get the toolset for a specific platform
   */
  static getTools(platform: Platform): PlatformToolset {
    const toolset = this.toolsets.get(platform);
    if (!toolset) {
      throw new Error(`No toolset registered for platform: ${platform}`);
    }
    return toolset;
  }

  /**
   * Check if a platform is registered
   */
  static isRegistered(platform: Platform): boolean {
    return this.toolsets.has(platform);
  }

  /**
   * Get all registered platforms
   */
  static getRegisteredPlatforms(): Platform[] {
    return Array.from(this.toolsets.keys());
  }

  /**
   * Get available tools for a platform
   */
  static getAvailableTools(platform: Platform): string[] {
    const toolset = this.toolsets.get(platform);
    if (!toolset) return [];
    return Object.keys(toolset);
  }
}
