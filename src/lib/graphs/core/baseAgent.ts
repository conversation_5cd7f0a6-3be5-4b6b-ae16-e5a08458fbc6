import { StateGraph, Annotation, START, END } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { RunnableSequence } from "@langchain/core/runnables";
import { AgentStateManager, redis } from "@/lib/upstash";
import { db } from "@/db";
import { agents, sites, returnRequests } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { loadShopPolicyConfig, ShopPolicyConfig } from "@/lib/config/shop-policy";
import { ToolRegistry } from "./toolRegistry";
import { ChannelFactory } from "@/lib/channels/factory";
import { searchKnowledgeBase } from "@/lib/embeddings";
import type { AgentState, Platform, Channel, Message } from "@/lib/types/agent";
import { processSingleShot, shouldUseSingleShot } from "./single-shot-agent";
import { processAura, getAuraGuidance, AuraResult, ConversationContext } from "@/lib/ei/senseEngine";
import { Emotion, Urgency } from "../../ei/senseTypes";

/**
 * The enriched agent state for the new multi-platform architecture
 */
const MultiPlatformAgentState = Annotation.Root({
  message: Annotation<string>(),
  agentId: Annotation<string>(),
  sessionId: Annotation<string>(),
  siteId: Annotation<string>(),
  platform: Annotation<Platform>(),
  channel: Annotation<Channel>(),
  // Agent and site names for personalized responses
  agentName: Annotation<string>({
    default: () => "",
    reducer: (current, update) => update,
  }),
  siteName: Annotation<string>({
    default: () => "",
    reducer: (current, update) => update,
  }),
  conversationHistory: Annotation<Array<Message>>({
    default: () => [],
    reducer: (current, update) => [...current, ...update],
  }),
  // DYNAMICALLY LOADED CONFIG
  policyConfig: Annotation<ShopPolicyConfig | null>({
    default: () => null,
    reducer: (current, update) => update,
  }),
  // Return context for continued conversations
  returnContext: Annotation<any | null>({
    default: () => null,
    reducer: (current, update) => update,
  }),
  // The agent's plan for the current turn
  plan: Annotation<{
    action: string;
    params: any;
    reasoning: string;
    requiresHumanInput: boolean;
  } | null>({
    default: () => null,
    reducer: (current, update) => update,
  }),
  // The result from a tool execution
  toolResult: Annotation<any | null>({
    default: () => null,
    reducer: (current, update) => update,
  }),
  // Final response for the user
  userReply: Annotation<string | null>({
    default: () => null,
    reducer: (current, update) => update,
  }),
  // Rendering context for channel-specific formatting
  renderingContext: Annotation<any | null>({
    default: () => null,
    reducer: (current, update) => update,
  }),
  // Knowledge base context for the current query
  knowledgeContext: Annotation<any[] | null>({
    default: () => null,
    reducer: (current, update) => update,
  }),
  // AURA emotional intelligence result
  auraResult: Annotation<AuraResult | null>({
    default: () => null,
    reducer: (current, update) => update,
  }),
});

export type CoreAgentState = typeof MultiPlatformAgentState.State;

// Removed - now using unified AURA engine

/**
 * Simple agent data caching
 */
interface AgentWithSite {
  id: string;
  name: string | null;
  siteId: string | null;
  siteName: string | null;
  platform: string | null;
}

async function getCachedAgentWithSite(agentId: string): Promise<AgentWithSite | null> {
  const cacheKey = `agent:with-site:${agentId}`;

  try {
    const cached = await redis.get(cacheKey);
    if (cached) {
      if (typeof cached === "string") {
        return JSON.parse(cached);
      } else if (typeof cached === "object") {
        return cached as AgentWithSite;
      }
    }

    const agent = await db.query.agents.findFirst({
      where: eq(agents.id, agentId),
      columns: { id: true, name: true, siteId: true },
    });

    if (!agent) return null;

    const site = agent.siteId
      ? await db.query.sites.findFirst({
          where: eq(sites.id, agent.siteId),
          columns: { name: true, platform: true },
        })
      : null;

    const result: AgentWithSite = {
      id: agent.id,
      name: agent.name,
      siteId: agent.siteId,
      siteName: site?.name || null,
      platform: site?.platform || null,
    };

    await redis.set(cacheKey, JSON.stringify(result), { ex: 1800 });
    return result;
  } catch (error) {
    console.error(`Error getting cached agent ${agentId}:`, error);

    const agent = await db.query.agents.findFirst({
      where: eq(agents.id, agentId),
      columns: { id: true, name: true, siteId: true },
    });

    if (!agent) return null;

    const site = agent.siteId
      ? await db.query.sites.findFirst({
          where: eq(sites.id, agent.siteId),
          columns: { name: true, platform: true },
        })
      : null;

    return {
      id: agent.id,
      name: agent.name,
      siteId: agent.siteId,
      siteName: site?.name || null,
      platform: site?.platform || null,
    };
  }
}

async function getCachedShopPolicyConfig(siteId: string): Promise<ShopPolicyConfig> {
  const cacheKey = `policy:config:${siteId}`;

  try {
    const cached = await redis.get(cacheKey);
    if (cached) {
      if (typeof cached === "string") {
        return JSON.parse(cached);
      } else if (typeof cached === "object") {
        return cached as ShopPolicyConfig;
      }
    }

    const config = await loadShopPolicyConfig(siteId);
    await redis.set(cacheKey, JSON.stringify(config), { ex: 900 });
    return config;
  } catch (error) {
    console.error(`Error getting cached policy config for site ${siteId}:`, error);
    return loadShopPolicyConfig(siteId);
  }
}

/**
 * Parallel loading of all context data
 */
async function loadAllContext(state: CoreAgentState): Promise<{
  policyConfig: ShopPolicyConfig;
  returnContext: any;
  knowledgeContext: any[] | null;
}> {
  const startTime = Date.now();

  // Define all async operations
  const loadPolicyConfig = async () => {
    return getCachedShopPolicyConfig(state.siteId);
  };

  const checkReturnContext = async () => {
    try {
      const returnRequest = await db.query.returnRequests.findFirst({
        where: and(
          eq(returnRequests.sessionId, state.sessionId),
          eq(returnRequests.status, "reviewing"),
        ),
      });

      if (returnRequest) {
        return {
          returnRequestId: returnRequest.id,
          orderNumber: returnRequest.orderNumber,
          reason: returnRequest.reason,
          status: "awaiting_more_info",
          staffNotes: returnRequest.staffNotes,
        };
      }
      return null;
    } catch (error) {
      console.error("Error checking return context:", error);
      return null;
    }
  };

  const searchKnowledge = async () => {
    const { message, siteId } = state;

    // Skip knowledge search for specific queries
    const hasOrderNumber = /\b\d{4,}\b/.test(message);
    const hasEmail = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/.test(message);
    const isVeryShort = message.trim().length < 4;
    const isSingleWord = message.trim().split(/\s+/).length === 1;

    if (hasOrderNumber || hasEmail || isVeryShort || isSingleWord) {
      return null;
    }

    try {
      const results = await searchKnowledgeBase(message, siteId, {
        limit: 3,
        minScore: 0.5,
        useCache: true,
      });

      return results.length > 0 ? results : null;
    } catch (error) {
      console.error("Error searching knowledge base:", error);
      return null;
    }
  };

  const [policyConfig, returnContext, knowledgeContext] = await Promise.all([
    loadPolicyConfig(),
    checkReturnContext(),
    searchKnowledge(),
  ]);

  return {
    policyConfig,
    returnContext,
    knowledgeContext,
  };
}

/**
 * The "Brain" of the agent with Nousu AURA™ emotional intelligence.
 * It decides what to do next and detects emotional context.
 * This is now platform-agnostic - it only decides WHAT to do, not HOW.
 */
async function router(state: CoreAgentState): Promise<Partial<CoreAgentState>> {
  const { conversationHistory, message, policyConfig, returnContext, platform, knowledgeContext } =
    state;

  if (!policyConfig) {
    throw new Error("Policy configuration not loaded!");
  }

  // AURA: Process emotional intelligence
  const conversationContext: ConversationContext = {
    previousMessages: conversationHistory
      .slice(-3)
      .filter((m): m is { role: "user" | "assistant"; content: string } => 
        m.role !== "system"
      ),
    timeOfDay: new Date().getHours(),
    messageCount: conversationHistory.filter(m => m.role === "user").length + 1,
    hasAssistantSpoken: conversationHistory.some(m => m.role === "assistant"),
  };

  const auraResult = await processAura(message, conversationContext, policyConfig.aura || { enabled: false, mode: "soft" });

  // Verify the platform has registered tools
  if (!ToolRegistry.isRegistered(platform)) {
    console.error(`❌ No tools registered for platform: ${platform}`);
    console.error(`Available platforms: ${ToolRegistry.getRegisteredPlatforms().join(", ")}`);
    throw new Error(`No tools registered for platform: ${platform}`);
  }

  // Handle return context - if this is a continuation of a return discussion
  if (returnContext && returnContext.status === "awaiting_more_info") {
    console.log("Handling return context continuation for order:", returnContext.orderNumber);

    // The agent should collect the additional information and update the return request
    return {
      plan: {
        action: "handleReturnFollowUp",
        params: {
          returnRequestId: returnContext.returnRequestId,
          orderNumber: returnContext.orderNumber,
          additionalInfo: message,
        },
        reasoning: "Customer is providing additional information for their return request",
        requiresHumanInput: false,
      },
    };
  }

  // TODO: ADD THIS TOOL AGAIN WHEN IT's IMPLEMENTED: 5. If you are stuck or the user is frustrated, choose "escalateToHuman".

  const routerPrompt = ChatPromptTemplate.fromMessages([
    [
      "system",
      `You are the reasoning core of {agentName}, a customer service agent for {siteName}. Your goal is to choose the correct tool to help the user.

# PERSONALITY
{personality}

# AVAILABLE TOOLS
You have access to the following tools. Only use these tools.
{tools_json}

# TOOL USAGE POLICIES
You MUST follow these rules when deciding to use a tool:
{policies}

# KNOWLEDGE BASE CONTEXT
{knowledgeContext}

# CONVERSATION HISTORY
{history}

# INSTRUCTIONS
Based on the user's latest message, decide what to do.

SMART CONTEXT DETECTION:
- If user asks about order STATUS (verzending, status, wanneer komt het): Use lookupOrder without orderContext
- If user asks about order CONTENTS (wat besteld, inhoud, items, producten): Use lookupOrder with orderContext: "contents"
- If user asks follow-up questions about an order ALREADY looked up in conversation: Use answerFromContext to avoid redundant API calls
- If user asks about same order number again but different context: Use lookupOrder with appropriate orderContext

PRODUCT RECOMMENDATIONS - CRITICAL RULE:
- ALWAYS use recommendedProducts when user mentions ANY product type or expresses interest in buying something
- If conversation history mentions products (t-shirt, shoes, clothing, etc.) and user responds: Use recommendedProducts
- If user asks for suggestions (ik zoek..., heb je..., aanbevelingen, kies maar uit...): Use recommendedProducts
- If user describes what they want (slim fit, witte t-shirt, warme jas, t-shirt, broek): Use recommendedProducts  
- If user says "look for", "choose for me", "show me", "what do you have": Use recommendedProducts
- If user says "geen voorkeur" or "no preference" when products were discussed: Use recommendedProducts with broad search from previous context
- If you just asked about products and user gives ANY response: Use recommendedProducts (NEVER switch to answerGeneralQuestion)
- BE PROACTIVE: Don't ask clarifying questions when you can search. Search first, then refine if needed.
- Examples: "kies maar wat leuks uit", "ik wil een t-shirt", "kijk maar voor een broek", "wat hebben jullie?", "geen voorkeur", "laat maar wat zien", "toon me iets", "laat zien"

KNOWLEDGE BASE USAGE:
- If knowledge context is provided above, it contains relevant information from the knowledge base
- When knowledge context exists: Use answerWithKnowledge to provide accurate, policy-based responses
- When no knowledge context: Use answerGeneralQuestion for general queries
- The knowledge context has already been searched - DO NOT use searchKnowledgeBase

1. If user mentions ANY product or wants to see/buy something: ALWAYS use recommendedProducts first
2. If you have enough information to use a tool, choose it.
3. If you need more information before you can use a tool, ask the user for it.
4. For general questions with knowledge context available: Use answerWithKnowledge
5. For general questions without knowledge context: Use answerGeneralQuestion
6. NEVER use answerGeneralQuestion for product requests - this causes dangerous hallucinations

Your output must be a single JSON object with the following schema:
{{
  "action": "the_name_of_the_action_to_use",
  "params": {{ "parameter_name": "parameter_value" }},
  "reasoning": "a brief explanation of why you chose this action",
  "requiresHumanInput": boolean // Set to true if you are asking the user a question to get more params.
}}
`,
    ],
    ["human", "{message}"],
  ]);

  const enabledToolsDescription = policyConfig.enabledTools
    .map((tool) => {
      // Generate tool descriptions dynamically
      if (tool === "lookupOrder")
        return `  - lookupOrder({ orderNumber: string, phone?: string, orderContext?: string }): Looks up order status or contents. Use orderContext "contents" if user asks what they ordered.`;
      if (tool === "createReturn")
        return `  - createReturn({ orderNumber: string, email: string, reason: string }): Creates a product return.`;
      if (tool === "answerGeneralQuestion")
        return `  - answerGeneralQuestion({ question: string }): Answers general questions, including questions about the agent's name and identity.`;
      if (tool === "answerFromContext")
        return `  - answerFromContext({ question: string }): Answers questions using conversation history to avoid redundant API calls.`;
      if (tool === "generateOrderResponse")
        return `  - generateOrderResponse({ orderData: object, userContext: string, questionType: string }): Generates natural responses from structured order data.`;
      if (tool === "generateReturnResponse")
        return `  - generateReturnResponse({ orderData: object, stage: string, errorDetails?: string }): Generates natural return-related responses.`;
      if (tool === "handleReturnFollowUp")
        return `  - handleReturnFollowUp({ returnRequestId: string, orderNumber: string, additionalInfo: string }): Handles additional information for return requests.`;
      if (tool === "searchKnowledgeBase")
        return `  - searchKnowledgeBase({ query: string }): Searches the knowledge base for relevant information about policies, procedures, or general questions.`;
      if (tool === "answerWithKnowledge")
        return `  - answerWithKnowledge({ question: string, knowledgeContext: array }): Answers questions using retrieved knowledge base context.`;
      if (tool === "recommendedProducts")
        return `  - recommendedProducts({ query: string, limit?: number }): Finds and recommends products based on natural language search. Use when customers ask for product suggestions, specific items, or describe what they're looking for.`;
      return `  - ${tool}`;
    })
    .join("\n");

  const policiesText = Object.entries(policyConfig.toolPolicies)
    .map(([tool, rules]) => `For action '${tool}':\n${rules.map((r) => `  - ${r}`).join("\n")}`)
    .join("\n\n");

  const model = new ChatOpenAI({
    model: "gpt-4o",
    temperature: 0,
    modelKwargs: { response_format: { type: "json_object" } },
  });

  const chain = RunnableSequence.from([routerPrompt, model, new StringOutputParser()]);

  const historyStr = conversationHistory.map((m) => `${m.role}: ${m.content}`).join("\n");

  // Format knowledge context for the prompt
  const knowledgeContextStr =
    knowledgeContext && knowledgeContext.length > 0
      ? `Found relevant information from the knowledge base:\n${knowledgeContext
          .map(
            (chunk, idx) =>
              `[${idx + 1}] ${chunk.content} (relevance: ${(chunk.similarity * 100).toFixed(0)}%)`,
          )
          .join("\n\n")}`
      : "No relevant knowledge base information found for this query.";

  const result = await chain.invoke({
    personality: policyConfig.personalityPrompt,
    tools_json: enabledToolsDescription,
    policies: policiesText,
    history: historyStr,
    message: message,
    agentName: state.agentName,
    siteName: state.siteName,
    knowledgeContext: knowledgeContextStr,
  });

  try {
    const plan = JSON.parse(result);
    console.log("CORE AGENT PLAN:", plan);
    console.log("AURA RESULT:", { 
      emotion: auraResult.emotion, 
      urgency: auraResult.urgency, 
      confidence: auraResult.confidence,
      method: auraResult.detectionMethod 
    });
    return { 
      plan,
      auraResult,
    };
  } catch (e) {
    console.error("Failed to parse router response:", result);
    return {
      plan: {
        action: "answerGeneralQuestion",
        params: { question: message },
        reasoning: "Router failed, falling back to general answer.",
        requiresHumanInput: false,
      },
    };
  }
}

/**
 * A generic tool executor. It looks up the chosen tool in the platform-specific
 * toolset and runs it.
 */
async function toolExecutor(state: CoreAgentState): Promise<{ toolResult: any }> {
  const { plan, siteId, sessionId, agentId, conversationHistory, platform } = state;
  if (!plan || !plan.action || plan.requiresHumanInput) {
    return { toolResult: null };
  }

  // Get the platform-specific toolset
  const tools = ToolRegistry.getTools(platform);
  const toolFunction = tools[plan.action as keyof typeof tools];

  if (!toolFunction) {
    return {
      toolResult: {
        status: "error",
        message: `Action '${plan.action}' not found for platform '${platform}'.`,
      },
    };
  }

  // Add context parameters that tools need for real API calls
  const enrichedParams = {
    ...plan.params,
    siteId,
    sessionId,
    agentId,
    agentName: state.agentName,
    siteName: state.siteName,
    // Pass conversation history for emotional intelligence features
    conversationHistory: conversationHistory,
    // Pass knowledge context if this is answerWithKnowledge
    ...(plan.action === "answerWithKnowledge" && state.knowledgeContext
      ? { knowledgeContext: state.knowledgeContext }
      : {}),
  };

  const result = await toolFunction(enrichedParams);
  return { toolResult: result };
}

/**
 * Generates the final, natural language response for the user using
 * the appropriate channel renderer with AURA emotional intelligence.
 */
async function generateFinalResponse(state: CoreAgentState): Promise<{ userReply: string }> {
  const { plan, toolResult, message, policyConfig, conversationHistory, channel, auraResult } = state;

  if (!policyConfig) {
    throw new Error("Policy config not loaded");
  }

  // Create rendering context
  const renderingContext = {
    action: plan?.requiresHumanInput ? "ask_question" : plan?.action || "general",
    toolResult: toolResult,
    userMessage: message,
    agentName: state.agentName,
    siteName: state.siteName,
    conversationHistory: conversationHistory,
    channel: channel,
    platform: state.platform,
    policyConfig: policyConfig,
    hasAssistantSpoken: conversationHistory?.some((msg) => msg.role === "assistant") || false,
  };

  // Get the appropriate channel renderer
  // Ensure channel is a string
  const channelString = typeof channel === "string" ? channel : "chat";

  const renderer = ChannelFactory.create(channelString as any);
  let response = await renderer.render(renderingContext);

  // Get AURA guidance for the full workflow
  if (auraResult && typeof response === "string") {
    const auraGuidance = getAuraGuidance(
      auraResult,
      policyConfig.aura || { enabled: false, mode: "soft" }
    );
    
    console.log("AURA: Empathy guidance provided", { 
      emotion: auraResult.emotion, 
      method: auraResult.detectionMethod,
      confidence: auraResult.confidence,
      guidance: auraGuidance
    });
  }

  // For now, we assume all renderers return strings (email will be handled differently)
  return { userReply: response as string };
}

const routeAfterContext = (state: CoreAgentState) => {
  try {
    const useSingleShot = shouldUseSingleShot(state);
    return useSingleShot ? "single_shot" : "full_workflow";
  } catch (error) {
    console.error("Error in routeAfterContext:", error);
    return "full_workflow"; // Safe fallback
  }
};

/**
 * Single-shot processor node
 */
async function singleShotProcessor(state: CoreAgentState): Promise<{ userReply: string | null }> {
  const result = await processSingleShot(state);
  return result.usedSingleShot ? { userReply: result.userReply } : { userReply: null }; // Don't set userReply when deferring to full workflow
}

/**
 * Decides where to go after the single-shot processor
 */
const routeFromSingleShot = (state: CoreAgentState) => {
  try {
    // If single-shot was used and returned a response, we're done
    if (state.userReply && state.userReply.trim().length > 0) {
      return "__end__";
    }
    // Otherwise, continue to full workflow
    return "router";
  } catch (error) {
    console.error("Error in routeFromSingleShot:", error);
    return "router"; // Safe fallback to continue workflow
  }
};

/**
 * Decides where to go after the router makes a plan.
 */
const routeFromRouter = (state: CoreAgentState) => {
  try {
    console.log("Router route decision:", { 
      requiresHumanInput: state.plan?.requiresHumanInput,
      hasAction: !!state.plan?.action,
      action: state.plan?.action 
    });

    if (state.plan?.requiresHumanInput) {
      // The plan is just to ask a question, so we go straight to generating that response.
      return "generate_response";
    }
    if (state.plan?.action) {
      // The plan is to execute a tool.
      return "execute_tool";
    }
    // Fallback if no plan was made - should not happen in normal flow
    console.warn("No plan found in routeFromRouter, going to generate_response");
    return "generate_response";
  } catch (error) {
    console.error("Error in routeFromRouter:", error);
    return "generate_response"; // Safe fallback
  }
};

// Cache the compiled graph to avoid recompilation on every request
let cachedGraph: any = null;

// Clear cache function for debugging
export function clearGraphCache() {
  cachedGraph = null;
  console.log("Graph cache cleared");
}

/**
 * Builds the complete, configurable multi-platform agent graph.
 * Uses caching to avoid expensive recompilation.
 */
export const buildMultiPlatformAgent = () => {
  if (cachedGraph) {
    return cachedGraph;
  }
  
  console.log("Building new LangGraph agent...");

  const workflow = new StateGraph(MultiPlatformAgentState)
    .addNode("load_all_context", loadAllContext)
    .addNode("single_shot_processor", singleShotProcessor)
    .addNode("router", router)
    .addNode("tool_executor", toolExecutor)
    .addNode("generate_response", generateFinalResponse)
    .addEdge(START, "load_all_context")
    .addConditionalEdges("load_all_context", routeAfterContext, {
      single_shot: "single_shot_processor",
      full_workflow: "router",
    })
    .addConditionalEdges("single_shot_processor", routeFromSingleShot, {
      router: "router",
      __end__: END,
    })
    .addConditionalEdges("router", routeFromRouter, {
      execute_tool: "tool_executor",
      generate_response: "generate_response",
    })
    .addEdge("tool_executor", "generate_response")
    .addEdge("generate_response", END);

  cachedGraph = workflow.compile();
  return cachedGraph;
};

/**
 * The main entry point for processing a user message with the new architecture.
 */
export const processMessage = async (
  message: string,
  agentId: string,
  sessionId: string,
  channel: Channel = "chat", // Default to chat for backward compatibility
  _previousState?: any,
) => {
  // Use cached graph instance to avoid recompiling on every request
  const graph = buildMultiPlatformAgent();

  const agentWithSite = await getCachedAgentWithSite(agentId);
  if (!agentWithSite) throw new Error(`Agent not found: ${agentId}`);

  // Determine platform (default to shopify for backward compatibility)
  const platform: Platform = (agentWithSite.platform as Platform) || "shopify";

  // Parallel loading of conversation history and message saving
  const [conversationHistory] = await Promise.all([
    AgentStateManager.getConversation(sessionId),
    AgentStateManager.addMessage(sessionId, "user", message),
  ]);

  // Filter and transform conversation history to match expected type
  const filteredHistory = conversationHistory
    .filter((msg) => msg.role === "user" || msg.role === "assistant")
    .map((msg) => ({ role: msg.role as "user" | "assistant", content: msg.content }));

  const initialState: CoreAgentState = {
    message,
    agentId,
    sessionId,
    siteId: agentWithSite.siteId || "",
    platform,
    channel,
    agentName: agentWithSite.name || "AI Agent",
    siteName: agentWithSite.siteName || "De winkel",
    conversationHistory: filteredHistory,
    // Reset other state fields for the new turn
    policyConfig: null,
    returnContext: null,
    plan: null,
    toolResult: null,
    userReply: null,
    renderingContext: null,
    knowledgeContext: null,
    auraResult: null,
  };

  try {
    // 2. Run the graph
    const result = await graph.invoke(initialState);
    const agentReply = result.userReply || "Er is iets misgegaan, probeer het later opnieuw.";

    // 3. Persist state and history
    await AgentStateManager.addMessage(sessionId, "assistant", agentReply);

    return {
      reply: agentReply,
      state: result,
    };
  } catch (error) {
    console.error("Error processing message:", error);
    const errorReply =
      "Sorry, ik ondervind momenteel een technisch probleem. Probeer het later opnieuw.";
    await AgentStateManager.addMessage(sessionId, "assistant", errorReply);
    return { reply: errorReply, state: null };
  }
};
