/**
 * Single-shot processing for simple messages with Nousu AURA™ integration
 */

import { ChatOpenAI } from "@langchain/openai";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import {
  SingleShotOut,
  Emotion,
  Urgency,
} from "../../ei/senseTypes";
import { processAura, getAuraGuidance, ConversationContext } from "@/lib/ei/senseEngine";

interface AuraConfig {
  enabled: boolean;
  mode: "shadow" | "soft" | "full";
  empathyAck?: { enabled: boolean; delayMs: number; nlText: string; enText: string };
  baseCaps?: { maxCouponEUR: number; approvalThresholdEUR: number };
  locales?: string[];
}

/**
 * Single-shot processor with Nousu AURA™ emotional intelligence
 * Falls back to full workflow for complex scenarios
 */
export async function processSingleShot(state: any): Promise<{
  userReply: string;
  usedSingleShot: boolean;
  fallbackReason?: string;
  affect?: { emotion: Emotion; urgency: Urgency };
  style_tags?: string[];
}> {
  const {
    message,
    policyConfig,
    conversationHistory,
    knowledgeContext,
    returnContext,
    agentName,
    siteName,
    customerName,
  } = state;

  const aura: AuraConfig = policyConfig.aura ?? {
    enabled: true,
    mode: "soft",
  };

  if (!policyConfig) {
    return {
      userReply: "Er is een fout opgetreden. Probeer het later opnieuw.",
      usedSingleShot: false,
      fallbackReason: "No policy config loaded",
    };
  }

  // Quick complexity check - fallback to full workflow if too complex
  const isComplex =
    message.length > 200 ||
    returnContext?.status === "awaiting_more_info" ||
    /\b(retour|return|refund|terugsturen|klacht)\b/i.test(message) ||
    /\b\d{5,}\b/.test(message); // Likely order numbers

  if (isComplex) {
    return {
      userReply: "",
      usedSingleShot: false,
      fallbackReason: "Message complexity requires full workflow",
    };
  }

  try {
    const knowledgeText =
      knowledgeContext && knowledgeContext.length > 0
        ? knowledgeContext
            .map((chunk: any, idx: number) => `[${idx + 1}] ${chunk.content}`)
            .join("\n")
        : "Geen relevante kennis gevonden.";

    const historyText = conversationHistory
      .map((m: any) => `${m.role}: ${m.content}`)
      .slice(-5)
      .join("\n");

    const singleShotPrompt = ChatPromptTemplate.fromMessages([
      [
        "system",
        `Je bent ${agentName}, klantenservice agent voor ${siteName}.

PERSOONLIJKHEID:
${policyConfig.personalityPrompt}

KENNIS CONTEXT:
${knowledgeText}

GESPREKGESCHIEDENIS (laatste 5):
${historyText}

INSTRUCTIES:
- Detecteer emotie en urgentie uit het klantbericht
- Kies relevante style_tags uit: ack, apologize_once, assure_action, de_escalate, short_sentences, step_by_step, ask_for_info, positive_closure
- Voor productzoekopdrachten of toolgebruik: zet reply naar "NEEDS_FULL_WORKFLOW"
- Antwoord in het Nederlands
- BELANGRIJK: Schrijf zoals een echte Nederlandse klantenservice medewerker zou doen
- Gebruik NOOIT em-dashes (—), ellipses (...), of andere AI-achtige leestekens
- Houd het simpel, direct en menselijk

Reageer met een natuurlijk antwoord op het volgende bericht:`,
      ],
      ["human", "{message}"],
    ]);

    const model = new ChatOpenAI({
      model: "gpt-4o",
      temperature: policyConfig.temperature,
      maxCompletionTokens: 300,
    });

    const result = await singleShotPrompt.pipe(model).invoke({ message });
    const response = result.content as string;

    // Use unified AURA engine for emotion detection and empathy
    const conversationContext: ConversationContext = {
      previousMessages: conversationHistory
        .slice(-3)
        .filter((m: any): m is { role: "user" | "assistant"; content: string } => 
          m.role !== "system"
        ),
      customerName,
      timeOfDay: new Date().getHours(),
      messageCount: conversationHistory.filter((m: any) => m.role === "user").length + 1,
      hasAssistantSpoken: conversationHistory.some((m: any) => m.role === "assistant"),
    };

    const auraResult = await processAura(
      message, 
      conversationContext, 
      aura
    );

    // Create SingleShotOut for compatibility
    const out: SingleShotOut = {
      emotion: auraResult.emotion,
      urgency: auraResult.urgency,
      style_tags: auraResult.style_tags,
      reply: response.trim(),
    };

    const affect = { emotion: out.emotion, urgency: out.urgency };

    console.log('NOUSU SENSE RESULT:', auraResult);

    // Check if the single-shot agent wants to defer to full workflow
    if (out.reply.includes("NEEDS_FULL_WORKFLOW")) {
      return {
        userReply: "",
        usedSingleShot: false,
        fallbackReason: "Single-shot agent deferred to full workflow for tool usage",
        affect,
      };
    }

    // Shadow mode: do not alter text
    if (aura.enabled && aura.mode === "shadow") {
      return {
        userReply: out.reply.trim(),
        usedSingleShot: true,
        affect,
        style_tags: out.style_tags,
      };
    }

    // Get AURA guidance (but let the LLM handle the actual response)
    const auraGuidance = aura.enabled ? getAuraGuidance(auraResult, aura) : null;
    
    console.log('AURA GUIDANCE:', auraGuidance);

    // For single-shot, we trust the LLM response and just log the guidance
    const userReply = out.reply.trim();

    return {
      userReply,
      usedSingleShot: true,
      affect,
      style_tags: out.style_tags,
    };
  } catch (error) {
    console.error("Error in single-shot processing:", error);
    return {
      userReply: "",
      usedSingleShot: false,
      fallbackReason: `Single-shot error: ${(error as Error).message}`,
    };
  }
}

/**
 * Determine if a message should use single-shot processing
 */
export function shouldUseSingleShot(state: any): boolean {
  const { message, returnContext } = state;

  // Skip single-shot for return contexts that need careful handling
  if (returnContext?.status === "awaiting_more_info") {
    return false;
  }

  // Skip for messages that likely need tool execution
  if (/\b\d{4,}\b/.test(message)) {
    // Order numbers
    return false;
  }

  if (/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/.test(message)) {
    // Emails
    return false;
  }


  // Skip for very long messages that might be complex
  if (message.length > 200) {
    return false;
  }

  // Use single-shot for most other cases
  return true;
}
