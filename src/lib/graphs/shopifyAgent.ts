/**
 * LEGACY FILE - KEPT FOR BACKWARD COMPATIBILITY
 *
 * This file has been refactored into the new multi-platform architecture.
 * The new architecture is located in:
 * - src/lib/graphs/core/baseAgent.ts (core reasoning)
 * - src/lib/graphs/platforms/shopify/tools.ts (Shopify-specific tools)
 * - src/lib/channels/chatRenderer.ts (chat channel renderer)
 *
 * This file now acts as a compatibility layer for existing code.
 */

import { processMessage as newProcessMessage } from "./core/baseAgent";
import { ToolRegistry } from "./core/toolRegistry";
import { shopifyTools } from "./platforms/shopify/tools";
// Channel type imported for potential future use
// import type { Channel } from "@/lib/types/agent";

// Initialize the tool registry for backward compatibility
// This ensures Shopify tools are registered when this file is imported
if (!ToolRegistry.isRegistered("shopify")) {
  ToolRegistry.register("shopify", shopifyTools);
}

/**
 * BACKWARD COMPATIBILITY EXPORTS
 * These functions maintain the same interface as before but use the new architecture
 */

/**
 * Legacy function that uses the new multi-platform architecture
 * Defaults to Shopify platform and chat channel for backward compatibility
 */
export const processMessage = async (
  message: string,
  agentId: string,
  sessionId: string,
  _previousState?: any, // Ignored in new architecture, kept for compatibility
) => {
  // Default to chat channel for backward compatibility
  return newProcessMessage(message, agentId, sessionId, "chat", _previousState);
};

/**
 * Legacy export - replaced by new architecture
 * @deprecated Use the new processMessage from core/baseAgent.ts instead
 */
export const buildShopifyAgent = () => {
  console.warn(
    "buildShopifyAgent is deprecated. The new architecture automatically handles agent building.",
  );

  // Return a mock function that delegates to the new architecture
  return {
    invoke: async (_state: any) => {
      console.warn("Direct graph invocation is deprecated. Use processMessage instead.");
      throw new Error(
        "Direct graph invocation is no longer supported. Use processMessage instead.",
      );
    },
  };
};

// Re-export the new architecture components for migration purposes
export { processMessage as newProcessMessage } from "./core/baseAgent";
export { ToolRegistry } from "./core/toolRegistry";
export { shopifyTools } from "./platforms/shopify/tools";
export { ChatRenderer } from "../channels/chatRenderer";

/**
 * Migration helper: check if the new architecture is properly initialized
 */
export const checkArchitectureStatus = () => {
  try {
    const { ToolRegistry } = require("./core/toolRegistry");
    return {
      status: "ready",
      registeredPlatforms: ToolRegistry.getRegisteredPlatforms(),
      message: "New multi-platform architecture is active",
    };
  } catch (error) {
    return {
      status: "error",
      message: "Failed to initialize new architecture",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};
