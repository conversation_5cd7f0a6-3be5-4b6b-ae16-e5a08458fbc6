import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { enqueueQStashJob, AgentStateManager } from "@/lib/upstash";
import { db } from "@/db";
import { returnRequests, users, orgMembers, sites, chatMessages } from "@/db/schema";
import { eq, and, inArray } from "drizzle-orm";
import { scheduleEmail } from "@/lib/email";
import { searchKnowledgeBase } from "@/lib/embeddings";
import { getProductRecommendations } from "@/lib/embeddings/products";
import type {
  PlatformToolset,
  OrderLookupParams,
  ReturnCreateParams,
  StandardOrder,
  Message,
} from "@/lib/types/agent";

/**
 * Schedule staff notification for return follow-up
 */
async function scheduleStaffFollowupNotification(
  returnRequestId: string,
  details: Record<string, any>,
) {
  try {
    // Get the return request with additional details
    const returnRequest = await db.query.returnRequests.findFirst({
      where: eq(returnRequests.id, returnRequestId),
    });

    if (!returnRequest) {
      console.error(`Return request ${returnRequestId} not found`);
      return;
    }

    // Get the site to find the organization
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, details.siteId),
    });

    if (!site || !site.orgId) {
      console.error(`Site ${details.siteId} not found or has no organization`);
      return;
    }

    // Get all users in the organization with appropriate roles
    const orgUsers = await db
      .select({
        email: users.email,
        name: users.name,
        role: orgMembers.role,
      })
      .from(orgMembers)
      .innerJoin(users, eq(orgMembers.userId, users.id))
      .where(
        and(
          eq(orgMembers.orgId, site.orgId),
          // Only notify owners and admins, not agents
          inArray(orgMembers.role, ["owner", "admin"]),
        ),
      );

    if (orgUsers.length === 0) {
      console.error(`No staff users found for organization ${site.orgId}`);
      return;
    }

    // Generate chat summary for staff
    const chatSummary = await generateChatSummary(details.sessionId, returnRequest);

    // Send notification to each staff member
    for (const user of orgUsers) {
      await scheduleEmail(
        user.email,
        "staff_return_followup_notification",
        {
          orderNumber: details.orderNumber,
          customerEmail: returnRequest.customerEmail || "Onbekend",
          orderValue: "Onbekend",
          returnReason: returnRequest.reason || details.additionalInfo,
          chatSummary,
          orderDate: returnRequest.createdAt?.toLocaleDateString("nl-NL") || "Onbekend",
          returnRequestId,
          dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/${details.siteId}/returns/${returnRequestId}/review`,
          agentName: details.agentName || "AI Agent",
          staffName: user.name || user.email.split("@")[0],
        },
        {
          returnRequestId,
          from: `${details.agentName || "AI Agent"} <<EMAIL>>`,
        },
      );
    }

    console.log(
      `📧 Staff follow-up notifications scheduled for return ${returnRequestId} to ${orgUsers.length} users`,
    );
  } catch (error) {
    console.error("Failed to schedule staff follow-up notification:", error);
    // Don't throw error - staff notification failure shouldn't break return follow-up
  }
}

/**
 * Generate AI summary of the chat conversation for staff review
 */
async function generateChatSummary(sessionId: string, returnRequest: any): Promise<string> {
  try {
    // Get recent chat messages for this session
    const messages = await db.query.chatMessages.findMany({
      where: eq(chatMessages.sessionId, sessionId),
      orderBy: [chatMessages.createdAt],
      limit: 20,
    });

    // Filter out internal messages and clean the conversation
    const cleanMessages = messages
      .filter((msg) => {
        // Skip system messages
        if (msg.role === "system") return false;

        // Skip messages that contain internal prompts or state data
        const content = msg.content || "";

        // Filter out internal prompts and instructions
        if (
          content.includes("You are responding in a CHAT CONVERSATION") ||
          content.includes("You are in a CHAT CONVERSATION") ||
          content.includes("CHAT GUIDELINES") ||
          content.includes("Generate a natural") ||
          content.includes("__STATE_DATA__")
        ) {
          return false;
        }

        // Skip very long messages that look like system prompts
        if (content.length > 500 && content.includes("Generate") && content.includes("Dutch")) {
          return false;
        }

        return true;
      })
      .map((msg) => {
        // Clean any remaining state data from message content
        let cleanContent = msg.content || "";

        // Remove any __STATE_DATA__ blocks
        cleanContent = cleanContent.replace(/__STATE_DATA__[\s\S]*$/, "").trim();

        // Remove JSON objects that look like internal data
        cleanContent = cleanContent.replace(/\{\"orderNumber\".*?\}/g, "").trim();

        return {
          role: msg.role,
          content: cleanContent,
        };
      })
      .filter((msg) => msg.content.length > 0); // Remove empty messages

    // Create a conversational summary
    const conversation = cleanMessages
      .map((msg) => `${msg.role === "user" ? "Klant" : "Agent"}: ${msg.content}`)
      .join("\n");

    return `Bestelling: ${returnRequest.orderNumber}
Retour reden: ${returnRequest.reason || "Niet opgegeven"}

Recente gespreksberichten:
${conversation}

Automatisch gegenereerd op ${new Date().toLocaleString("nl-NL")}`;
  } catch (error) {
    console.error("Error generating chat summary:", error);
    return "Kon gespreksamenvatting niet genereren.";
  }
}

/**
 * Shopify-specific tools implementation
 */
export const shopifyTools: PlatformToolset = {
  /**
   * Looks up an order in Shopify - REAL IMPLEMENTATION
   */
  lookupOrder: async (params: OrderLookupParams) => {
    console.log(`SHOPIFY TOOL: Executing REAL lookupOrder with params:`, params);

    if (!params.siteId || !params.sessionId || !params.agentId) {
      throw new Error("Missing required parameters for order lookup");
    }

    try {
      // Determine the loading message based on context
      let loadingMessage = "Ik ga je bestelling opzoeken. Een moment geduld...";
      if (params.orderContext && params.orderContext.includes("contents")) {
        loadingMessage = "Even kijken wat je precies hebt besteld...";
      }

      // Use QStash for async processing with a small delay to ensure message ordering
      const jobId = await enqueueQStashJob(
        {
          jobType: "order_status",
          sessionId: params.sessionId,
          agentId: params.agentId,
          siteId: params.siteId,
          orderNumber: params.orderNumber,
          orderContext: params.orderContext, // Pass context to worker
          phone: params.phone,
          userInfo: {},
        },
        1,
      ); // 1 second delay to ensure loading message appears first

      return {
        status: "processing",
        jobId,
        message: loadingMessage, // Return the loading message immediately
      };
    } catch (error) {
      console.error("Failed to enqueue order lookup job:", error);
      return {
        status: "error",
        message:
          "Sorry, er ging iets mis bij het opzoeken van je bestelling. Probeer het later opnieuw.",
      };
    }
  },

  /**
   * Generates responses based on structured order data (used by worker)
   */
  generateOrderResponse: async (params: {
    orderData: any;
    userContext: string;
    questionType: "status" | "contents" | "general";
  }) => {
    console.log(`SHOPIFY TOOL: Generating order response for:`, params.questionType);

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });

    let prompt = "";
    const { orderData } = params;

    if (params.questionType === "contents") {
      prompt = `Generate a natural response in Dutch about what the customer ordered. Here's the order data: ${JSON.stringify(orderData)}
      
Focus on:
- What items they ordered
- Quantities and variants
- Order date and total price
- Make it conversational, not a list
- Use "je" instead of "u"
- Be friendly and personal`;
    } else if (params.questionType === "status") {
      prompt = `Generate a natural response in Dutch about the order status and delivery. Here's the order data: ${JSON.stringify(orderData)}
      
Focus on:
- Current fulfillment status  
- Shipping information and tracking if available
- Expected delivery times
- Make it reassuring and informative
- Use "je" instead of "u"
- Be friendly and personal`;
    } else {
      prompt = `Generate a natural response in Dutch about this order. Here's the order data: ${JSON.stringify(orderData)}
      
Context: ${params.userContext}
      
Be helpful, natural and conversational. Use "je" instead of "u". Provide relevant information based on what the user is asking.`;
    }

    return await model.pipe(new StringOutputParser()).invoke([["human", prompt]]);
  },

  /**
   * Generates responses for return confirmations (used by worker)
   */
  generateReturnResponse: async (params: {
    orderData: any;
    stage: "confirmation" | "not_found" | "error";
    errorDetails?: string;
  }) => {
    console.log(`SHOPIFY TOOL: Generating return response for stage:`, params.stage);

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });

    let prompt = "";

    if (params.stage === "confirmation") {
      prompt = `Generate a natural confirmation response in Dutch for a return request. Order data: ${JSON.stringify(params.orderData)}
      
The return request has been successfully created. Generate a message that:
- Confirms the return request is received
- Mentions they'll get email within 24 hours
- Is reassuring and friendly
- Uses "je" instead of "u"
- Feels personal, not templated`;
    } else if (params.stage === "not_found") {
      prompt = `Generate a helpful response in Dutch when an order cannot be found for a return. Order number was: ${params.orderData?.orderNumber}
      
Be helpful and suggest they:
- Check the order number
- Try a different format
- Contact support if needed
- Use "je" instead of "u"`;
    } else {
      prompt = `Generate an apologetic error response in Dutch for when a return request fails. Error: ${params.errorDetails}
      
Be understanding and suggest they try again later or contact support. Use "je" instead of "u".`;
    }

    return await model.pipe(new StringOutputParser()).invoke([["human", prompt]]);
  },

  /**
   * Creates a return request in Shopify - REAL IMPLEMENTATION
   */
  createReturn: async (params: ReturnCreateParams) => {
    console.log(`SHOPIFY TOOL: Executing REAL createReturn with params:`, params);

    if (!params.siteId || !params.sessionId || !params.agentId) {
      throw new Error("Missing required parameters for return creation");
    }

    try {
      // Use QStash for async processing
      const jobId = await enqueueQStashJob({
        jobType: "return_create",
        sessionId: params.sessionId,
        agentId: params.agentId,
        siteId: params.siteId,
        orderNumber: params.orderNumber,
        email: params.email,
        userInfo: { reason: params.reason },
      });

      return {
        status: "processing",
        jobId,
        message: "Ik ga je retourzending aanmaken. Je ontvangt zo meer informatie.",
      };
    } catch (error) {
      console.error("Failed to enqueue return creation job:", error);
      return {
        status: "error",
        message:
          "Sorry, er ging iets mis bij het aanmaken van je retour. Probeer het later opnieuw.",
      };
    }
  },

  /**
   * A simple tool to answer general questions with improved emotional intelligence.
   */
  answerGeneralQuestion: async (params: {
    question: string;
    agentName?: string;
    siteName?: string;
    conversationHistory?: Message[];
  }) => {
    console.log(`SHOPIFY TOOL: Executing answerGeneralQuestion with question:`, params.question);
    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });

    // Check if this is the first assistant message (greeting logic)
    const hasAssistantSpoken =
      params.conversationHistory?.some((msg) => msg.role === "assistant") || false;

    const prompt = ChatPromptTemplate.fromTemplate(`
You are {agentName}, a customer service assistant for {siteName}. 

CONVERSATION CONTEXT:
- This is {messageType} message in the conversation
- Previous conversation: {conversationHistory}

CRITICAL SAFETY RULES:
1. NEVER mention specific products, prices, or inventory unless you have REAL DATA from the system
2. NEVER make up product names, descriptions, or prices - this is DANGEROUS for e-commerce
3. If user asks about products, direct them to search: "Laat me even zoeken wat we hebben..."

EMOTIONAL INTELLIGENCE GUIDELINES:
1. GREETING LOGIC: {greetingInstruction}
2. EMPATHY DETECTION: If the user expresses uncertainty, doubt, worry, or negative sentiment, start with an empathic sentence that acknowledges their feelings
3. SPECIFIC CHECK-BACKS: End with a specific, targeted question or offer related to their ask (e.g., "Helpt dit je verder?", "Zal ik de exacte maat voor je opzoeken?") instead of generic closings like "Laat het me weten!"
4. NO REDUNDANCY: Never repeat information already shared in the conversation

USER'S QUESTION: {question}

Generate a natural, conversational response in Dutch that:
- Uses "je/jij" (informal) instead of "u" (formal)  
- Is friendly and helpful
- Follows the emotional intelligence guidelines above
- Feels human and personalized, not robotic
- If asking for your name, introduce yourself as {agentName}
- CRITICAL: If the question is about products, inventory, or shopping, respond with: "Laat me even zoeken in ons assortiment voor je..." and DO NOT make up products
`);

    const conversationHistoryStr =
      params.conversationHistory
        ?.map((msg) => `${msg.role === "user" ? "Klant" : "Agent"}: ${msg.content}`)
        .join("\n") || "Geen eerdere berichten";

    return await prompt
      .pipe(model)
      .pipe(new StringOutputParser())
      .invoke({
        question: params.question,
        agentName: params.agentName || "de klantenservice agent",
        siteName: params.siteName || "onze webshop",
        messageType: hasAssistantSpoken ? "een vervolg" : "het eerste",
        greetingInstruction: hasAssistantSpoken
          ? "DO NOT greet again - continue the conversation naturally"
          : "You may greet naturally if appropriate",
        conversationHistory: conversationHistoryStr,
      });
  },

  /**
   * Answer questions using conversation context (avoid redundant API calls)
   */
  answerFromContext: async (params: {
    question: string;
    conversationHistory?: Message[];
    sessionId?: string;
    agentName?: string;
    siteName?: string;
  }) => {
    console.log(`SHOPIFY TOOL: Executing answerFromContext with question:`, params.question);

    // Get conversation history if not provided
    let conversationHistory = params.conversationHistory;
    if (!conversationHistory && params.sessionId) {
      conversationHistory = await AgentStateManager.getConversation(params.sessionId);
    }

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 0 });
    const prompt = ChatPromptTemplate.fromTemplate(`
You are {agentName}, working as a customer service agent for {siteName}. Based on the conversation history, answer the user's question without making new API calls.

CONVERSATION HISTORY:
{history}

USER'S CURRENT QUESTION: {question}

If the conversation history contains the information needed to answer the question, provide a helpful response in Dutch.
If the information is not available in the conversation, say you need to look it up.
Be conversational and natural.
`);

    const historyStr = conversationHistory?.map((m) => `${m.role}: ${m.content}`).join("\n") || "";

    return await prompt
      .pipe(model)
      .pipe(new StringOutputParser())
      .invoke({
        question: params.question,
        history: historyStr,
        agentName: params.agentName || "AI Agent",
        siteName: params.siteName || "de winkel",
      });
  },

  /**
   * Handle follow-up information for a return request
   */
  handleReturnFollowUp: async (params: {
    returnRequestId: string;
    orderNumber: string;
    additionalInfo: string;
    sessionId?: string;
    siteId?: string;
    agentName?: string;
  }) => {
    console.log(`SHOPIFY TOOL: Handling return follow-up for ${params.returnRequestId}`);

    try {
      // Fetch the return request from the id
      const returnRequest = await db
        .select()
        .from(returnRequests)
        .where(eq(returnRequests.id, params.returnRequestId))
        .then((res) => res[0]);

      if (!returnRequest) {
        throw new Error("Return request not found");
      }

      // Update the return request with the additional information
      await db
        .update(returnRequests)
        .set({
          reason: `${returnRequest.reason ? returnRequest.reason + "\n" : ""}| Nieuw opgegeven informatie klant: ${params.additionalInfo}`,
          status: "waiting_on_staff", // Move back to pending for staff review
          updatedAt: new Date(),
        })
        .where(eq(returnRequests.id, params.returnRequestId));

      // Schedule staff notification for the follow-up
      if (params.siteId && params.sessionId) {
        await scheduleStaffFollowupNotification(params.returnRequestId, {
          orderNumber: params.orderNumber,
          additionalInfo: params.additionalInfo,
          sessionId: params.sessionId,
          siteId: params.siteId,
          agentName: params.agentName,
        });
      }

      // Generate a confirmation message
      const model = new ChatOpenAI({ model: "gpt-4o", temperature: 1 });
      const prompt = ChatPromptTemplate.fromTemplate(`
You are a helpful customer service agent. The customer just provided additional information for their return request.

Order: ${params.orderNumber}
Additional information: ${params.additionalInfo}

Generate a brief, friendly confirmation in Dutch that:
- Thanks them for the additional information
- Confirms you've updated their return request
- Mentions that staff will review it again
- Uses "je" instead of "u"
`);

      const response = await prompt.pipe(model).pipe(new StringOutputParser()).invoke({});

      return {
        status: "updated",
        message: response,
      };
    } catch (error) {
      console.error("Error updating return request:", error);
      return {
        status: "error",
        message:
          "Er ging iets mis bij het bijwerken van je retourverzoek. Probeer het later opnieuw.",
      };
    }
  },

  /**
   * Search the knowledge base for relevant information
   */
  searchKnowledgeBase: async (params: {
    query: string;
    siteId: string;
    topicId?: string;
    limit?: number;
  }) => {
    console.log(`SHOPIFY TOOL: Searching knowledge base for query:`, params.query);

    try {
      const results = await searchKnowledgeBase(params.query, params.siteId, {
        limit: params.limit || 3,
        minScore: 0.7,
        topicId: params.topicId,
        useCache: true,
      });

      if (results.length === 0) {
        return {
          status: "no_results",
          message: "Geen relevante informatie gevonden in de kennisbank.",
          results: [],
        };
      }

      return {
        status: "success",
        results: results,
        message: `${results.length} relevante resultaten gevonden.`,
      };
    } catch (error) {
      console.error("Error searching knowledge base:", error);
      return {
        status: "error",
        message: "Er ging iets mis bij het doorzoeken van de kennisbank.",
        results: [],
      };
    }
  },

  /**
   * Answer questions using knowledge base context
   */
  answerWithKnowledge: async (params: {
    question: string;
    knowledgeContext: any[];
    siteId: string;
    agentName?: string;
    siteName?: string;
    conversationHistory?: Message[];
  }) => {
    console.log(`SHOPIFY TOOL: Answering with knowledge base context`);

    const model = new ChatOpenAI({ model: "gpt-4o", temperature: 0.1 });

    // Format knowledge chunks for the prompt
    const knowledgeStr = params.knowledgeContext
      .map((chunk, idx) => `[${idx + 1}] ${chunk.content}`)
      .join("\n\n");

    const prompt = ChatPromptTemplate.fromTemplate(`
You are {agentName}, a customer service assistant for {siteName}.

KNOWLEDGE BASE CONTEXT:
{knowledgeContext}

CONVERSATION HISTORY:
{conversationHistory}

USER'S QUESTION: {question}

Instructions:
1. Answer the user's question using the knowledge base information provided
2. Be accurate and only use information from the knowledge base
3. If the knowledge doesn't fully answer the question, acknowledge what you can answer and what requires more information
4. Write in conversational Dutch using "je/jij"
5. Reference specific policies or information naturally (don't cite chunk numbers)
6. If multiple chunks contain conflicting information, use the one with higher similarity/scores

Provide a natural, helpful response:
`);

    const conversationHistoryStr =
      params.conversationHistory
        ?.map((msg) => `${msg.role === "user" ? "Klant" : "Agent"}: ${msg.content}`)
        .join("\n") || "Geen eerdere berichten";

    return await prompt
      .pipe(model)
      .pipe(new StringOutputParser())
      .invoke({
        question: params.question,
        knowledgeContext: knowledgeStr,
        conversationHistory: conversationHistoryStr,
        agentName: params.agentName || "de klantenservice agent",
        siteName: params.siteName || "onze webshop",
      });
  },

  /**
   * Get product recommendations based on natural language query
   */
  recommendedProducts: async (params: {
    query: string;
    siteId: string;
    sessionId: string;
    agentId: string;
    limit?: number;
    agentName?: string;
    siteName?: string;
    conversationHistory?: Message[];
  }) => {
    console.log(`SHOPIFY TOOL: Getting product recommendations for query:`, params.query);

    if (!params.siteId || !params.sessionId || !params.agentId) {
      throw new Error("Missing required parameters for product recommendations");
    }

    try {
      // Use QStash for async processing with a small delay to ensure message ordering
      const jobId = await enqueueQStashJob(
        {
          jobType: "product_recommendations",
          sessionId: params.sessionId,
          agentId: params.agentId,
          siteId: params.siteId,
          userInfo: {
            query: params.query,
            limit: params.limit || 5,
          },
        },
        1, // 1 second delay to ensure loading message appears first
      );

      return {
        status: "processing",
        jobId,
        message:
          "Ik ga even kijken welke producten we hebben die bij je zoekopdracht passen. Een momentje...", // Return the loading message immediately
      };
    } catch (error) {
      console.error("Failed to enqueue product recommendations job:", error);
      return {
        status: "error",
        message:
          "Sorry, er ging iets mis bij het zoeken naar producten. Probeer het later opnieuw.",
      };
    }
  },
};
