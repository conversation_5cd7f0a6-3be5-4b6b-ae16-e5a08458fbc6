import { db } from "@/db";
import { agentConfigurations } from "@/db/schema";
import { eq, and, isNull } from "drizzle-orm";

/**
 * Type definitions for agent configurations
 */
export type ConfigType = "returns" | "orders" | "products" | "customer_service" | "global";

export interface BaseConfig {
  enabled: boolean;
}

export interface ReturnsConfig extends BaseConfig {
  requireEmail: boolean;
  requirePhone: boolean;
  requireReason: boolean;
  maxDaysAfterPurchase: number;
  autoApproveBelow: number;
  requirePhoto: boolean;
  notifyChannels: string[];
  customFields?: Array<{ name: string; required: boolean }>;
}

export interface OrdersConfig extends BaseConfig {
  maxHoursAfterPurchase: number;
  requireReason: boolean;
  autoApproveBelow: number;
  notifyChannels: string[];
}

export interface CustomerServiceConfig extends BaseConfig {
  collectCustomerInfo: boolean;
  requiredFields: string[];
  optionalFields: string[];
  escalationThreshold: number;
  maxTurnsBeforeHuman: number;
}

export interface GlobalConfig extends BaseConfig {
  defaultLanguage: string;
  supportedLanguages: string[];
  businessHours: {
    timezone: string;
    schedule: Record<string, { open: string; close: string }>;
  };
  outOfHoursMessage?: string;
}

/**
 * Default configurations
 */
const DEFAULT_RETURNS_CONFIG: ReturnsConfig = {
  enabled: true,
  requireEmail: true,
  requirePhone: false,
  requireReason: true,
  maxDaysAfterPurchase: 30,
  autoApproveBelow: 50,
  requirePhoto: false,
  notifyChannels: ["email"],
};

const DEFAULT_ORDERS_CONFIG: OrdersConfig = {
  enabled: true,
  maxHoursAfterPurchase: 24,
  requireReason: true,
  autoApproveBelow: 100,
  notifyChannels: ["email"],
};

const DEFAULT_CUSTOMER_SERVICE_CONFIG: CustomerServiceConfig = {
  enabled: true,
  collectCustomerInfo: true,
  requiredFields: ["email"],
  optionalFields: ["phone", "orderNumber"],
  escalationThreshold: 0.7,
  maxTurnsBeforeHuman: 5,
};

const DEFAULT_GLOBAL_CONFIG: GlobalConfig = {
  enabled: true,
  defaultLanguage: "nl",
  supportedLanguages: ["nl", "en"],
  businessHours: {
    timezone: "Europe/Amsterdam",
    schedule: {
      "1": { open: "09:00", close: "17:00" }, // Monday
      "2": { open: "09:00", close: "17:00" }, // Tuesday
      "3": { open: "09:00", close: "17:00" }, // Wednesday
      "4": { open: "09:00", close: "17:00" }, // Thursday
      "5": { open: "09:00", close: "17:00" }, // Friday
      // Weekend days not included = closed
    },
  },
};

/**
 * Get configuration for a specific site and type
 * Falls back to default configuration if not found
 */
export async function getAgentConfig<T extends BaseConfig>(
  siteId: string,
  agentId: string | null,
  configType: ConfigType,
): Promise<T> {
  try {
    // Build query conditions
    let conditions = [
      eq(agentConfigurations.siteId, siteId),
      eq(agentConfigurations.configType, configType),
    ];

    if (agentId) {
      conditions.push(eq(agentConfigurations.agentId, agentId));
    }

    // Try to get agent-specific config first
    let config = agentId
      ? await db.query.agentConfigurations.findFirst({
          where: and(...conditions),
        })
      : null;

    // If no agent-specific config, try site-level config
    if (!config && agentId) {
      config = await db.query.agentConfigurations.findFirst({
        where: and(
          eq(agentConfigurations.siteId, siteId),
          eq(agentConfigurations.configType, configType),
          isNull(agentConfigurations.agentId),
        ),
      });
    }

    // If found and enabled, return the configuration
    if (config && config.enabled) {
      return config.settings as T;
    }

    // If found but disabled, return disabled config
    if (config && !config.enabled) {
      return { enabled: false } as T;
    }

    // Fall back to default configuration
    return getDefaultConfig(configType) as T;
  } catch (error) {
    console.error(`Error getting ${configType} configuration:`, error);
    return getDefaultConfig(configType) as T;
  }
}

/**
 * Get default configuration for a specific type
 */
export function getDefaultConfig(configType: ConfigType): BaseConfig {
  switch (configType) {
    case "returns":
      return DEFAULT_RETURNS_CONFIG;
    case "orders":
      return DEFAULT_ORDERS_CONFIG;
    case "customer_service":
      return DEFAULT_CUSTOMER_SERVICE_CONFIG;
    case "global":
      return DEFAULT_GLOBAL_CONFIG;
    default:
      return { enabled: true };
  }
}

/**
 * Check if a specific feature is enabled for a site/agent
 */
export async function isFeatureEnabled(
  siteId: string,
  agentId: string | null,
  configType: ConfigType,
): Promise<boolean> {
  const config = await getAgentConfig<BaseConfig>(siteId, agentId, configType);
  return config.enabled;
}
