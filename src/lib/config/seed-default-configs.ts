import { db } from "@/db";
import { sites, shopPolicyConfigs } from "@/db/schema";
import { createShopPolicyConfig } from "./shop-policy";
import { eq } from "drizzle-orm";

/**
 * Seeds default shop policy configurations for sites that don't have them yet
 */
export async function seedDefaultConfigs() {
  console.log("🌱 Seeding default shop policy configurations...");

  try {
    // Get all sites
    const allSites = await db.query.sites.findMany({
      columns: { id: true, name: true },
    });

    console.log(`Found ${allSites.length} sites`);

    // For each site, check if it has a policy config
    for (const site of allSites) {
      const existingConfig = await db.query.shopPolicyConfigs.findFirst({
        where: eq(shopPolicyConfigs.siteId, site.id),
      });

      if (!existingConfig) {
        console.log(`Creating default config for site: ${site.name} (${site.id})`);

        await createShopPolicyConfig(site.id, {
          // Uses the default configuration from getDefaultShopPolicyConfig()
        });

        console.log(`✅ Created default config for ${site.name}`);
      } else {
        console.log(`⏭️ Site ${site.name} already has config, skipping`);
      }
    }

    console.log("✅ Seeding completed successfully!");
  } catch (error) {
    console.error("❌ Error seeding default configs:", error);
    throw error;
  }
}

/**
 * Seeds a specific configuration for a site (useful for testing)
 */
export async function seedSiteConfig(siteId: string, customConfig?: any) {
  console.log(`🌱 Seeding config for site: ${siteId}`);

  const config = await createShopPolicyConfig(siteId, customConfig || {});

  console.log(`✅ Created config for site ${siteId}`);
  return config;
}
