import { db } from "@/db";
import { shopPolicyConfigs, sites } from "@/db/schema";
import { eq, and } from "drizzle-orm";

export interface ShopPolicyConfig {
  personalityPrompt: string;
  enabledTools: string[];
  toolPolicies: Record<string, string[]>;
  maxTurnsBeforeEscalation: number;
  escalationEnabled: boolean;
  escalationEmail?: string;
  primaryModel: string;
  fallbackModel: string;
  temperature: number;
  reflectionEnabled: boolean;
  reflectionThreshold: number;
  businessHours?: {
    timezone: string;
    schedule: Record<string, { start: string; end: string; enabled: boolean }>;
  };
  aura?: {
    enabled: boolean;
    mode: "shadow" | "soft" | "full";
    empathyAck?: { enabled: boolean; delayMs: number; nlText: string; enText: string };
    baseCaps?: { maxCouponEUR: number; approvalThresholdEUR: number };
    locales?: string[];
  };
}

/**
 * Loads shop-specific policy configuration from database
 */
export async function loadShopPolicyConfig(siteId: string): Promise<ShopPolicyConfig> {
  console.log(`Loading shop policy config for siteId: ${siteId}`);

  try {
    // Get the active policy config for this site
    const config = await db.query.shopPolicyConfigs.findFirst({
      where: and(eq(shopPolicyConfigs.siteId, siteId), eq(shopPolicyConfigs.active, true)),
    });

    if (config) {
      return {
        personalityPrompt: config.personalityPrompt,
        enabledTools: config.enabledTools,
        toolPolicies: config.toolPolicies,
        maxTurnsBeforeEscalation: config.maxTurnsBeforeEscalation || 5,
        escalationEnabled: config.escalationEnabled || true,
        escalationEmail: config.escalationEmail || undefined,
        primaryModel: config.primaryModel || "gpt-4o",
        fallbackModel: config.fallbackModel || "gpt-5-mini",
        temperature: (config.temperature || 20) / 100, // Convert back to decimal
        reflectionEnabled: config.reflectionEnabled || true,
        reflectionThreshold: (config.reflectionThreshold || 40) / 100,
        businessHours: config.businessHours || undefined,
        aura: config.aura || {
          enabled: true,
          mode: "soft",
          empathyAck: { enabled: true, delayMs: 200, nlText: "", enText: "" },
          baseCaps: { maxCouponEUR: 10, approvalThresholdEUR: 50 },
          locales: ["nl", "en"],
        },
      };
    }

    // If no config exists, return default configuration
    console.log(`No policy config found for site ${siteId}, using defaults`);
    return getDefaultShopPolicyConfig();
  } catch (error) {
    console.error(`Failed to load shop policy config for site ${siteId}:`, error);
    return getDefaultShopPolicyConfig();
  }
}

/**
 * Creates a new shop policy configuration
 */
export async function createShopPolicyConfig(
  siteId: string,
  config: Partial<ShopPolicyConfig>,
): Promise<ShopPolicyConfig> {
  const fullConfig = {
    ...getDefaultShopPolicyConfig(),
    ...config,
  };

  await db.insert(shopPolicyConfigs).values({
    siteId,
    personalityPrompt: fullConfig.personalityPrompt,
    enabledTools: fullConfig.enabledTools,
    toolPolicies: fullConfig.toolPolicies,
    maxTurnsBeforeEscalation: fullConfig.maxTurnsBeforeEscalation,
    escalationEnabled: fullConfig.escalationEnabled,
    escalationEmail: fullConfig.escalationEmail,
    primaryModel: fullConfig.primaryModel,
    fallbackModel: fullConfig.fallbackModel,
    temperature: Math.round(fullConfig.temperature * 100), // Store as integer
    reflectionEnabled: fullConfig.reflectionEnabled,
    reflectionThreshold: Math.round(fullConfig.reflectionThreshold * 100),
    businessHours: fullConfig.businessHours,
    aura: fullConfig.aura,
    active: true,
  });

  return fullConfig;
}

/**
 * Updates an existing shop policy configuration
 */
export async function updateShopPolicyConfig(
  siteId: string,
  updates: Partial<ShopPolicyConfig>,
): Promise<ShopPolicyConfig> {
  const updateData: any = {};

  if (updates.personalityPrompt !== undefined)
    updateData.personalityPrompt = updates.personalityPrompt;
  if (updates.enabledTools !== undefined) updateData.enabledTools = updates.enabledTools;
  if (updates.toolPolicies !== undefined) updateData.toolPolicies = updates.toolPolicies;
  if (updates.maxTurnsBeforeEscalation !== undefined)
    updateData.maxTurnsBeforeEscalation = updates.maxTurnsBeforeEscalation;
  if (updates.escalationEnabled !== undefined)
    updateData.escalationEnabled = updates.escalationEnabled;
  if (updates.escalationEmail !== undefined) updateData.escalationEmail = updates.escalationEmail;
  if (updates.primaryModel !== undefined) updateData.primaryModel = updates.primaryModel;
  if (updates.fallbackModel !== undefined) updateData.fallbackModel = updates.fallbackModel;
  if (updates.temperature !== undefined)
    updateData.temperature = Math.round(updates.temperature * 100);
  if (updates.reflectionEnabled !== undefined)
    updateData.reflectionEnabled = updates.reflectionEnabled;
  if (updates.reflectionThreshold !== undefined)
    updateData.reflectionThreshold = Math.round(updates.reflectionThreshold * 100);
  if (updates.businessHours !== undefined) updateData.businessHours = updates.businessHours;
  if (updates.aura !== undefined) updateData.aura = updates.aura;

  updateData.updatedAt = new Date();

  await db
    .update(shopPolicyConfigs)
    .set(updateData)
    .where(and(eq(shopPolicyConfigs.siteId, siteId), eq(shopPolicyConfigs.active, true)));

  return loadShopPolicyConfig(siteId);
}

/**
 * Default configuration for new shops
 */
function getDefaultShopPolicyConfig(): ShopPolicyConfig {
  return {
    personalityPrompt:
      "You are NousuAI, a friendly, slightly informal, and very helpful customer service agent for a modern Dutch webshop. Always reply in Dutch. Keep responses conversational and natural - you're chatting, not writing emails.",
    enabledTools: [
      "lookupOrder",
      "createReturn",
      "answerGeneralQuestion",
      "answerFromContext",
      "generateOrderResponse",
      "generateReturnResponse",
      "recommendedProducts",
    ],
    toolPolicies: {
      lookupOrder: [
        "You can look up order status with just the order number.",
        "Only ask for additional information if truly necessary for the specific issue.",
        "Use orderContext 'contents' if user asks what they ordered.",
      ],
      createReturn: [
        "A reason for the return is always required.",
        "An email address is required to send the return label.",
        "Be empathetic and helpful during the return process.",
      ],
      answerGeneralQuestion: [
        "Use for general product questions, shipping info, or store policies.",
        "Keep answers helpful and conversational.",
      ],
      answerFromContext: [
        "Use this to avoid redundant API calls when information is already available.",
        "Reference previous conversation context naturally.",
      ],
      generateOrderResponse: [
        "Use when you have structured order data and need to generate a natural response.",
        "Focus on what the user specifically asked about.",
      ],
      generateReturnResponse: [
        "Use when generating return-related responses with structured data.",
        "Be reassuring and clear about next steps.",
      ],
      recommendedProducts: [
        "Use when customers ask for product suggestions or describe what they're looking for.",
        "Perfect for queries like 'I'm looking for...', 'Do you have...', 'Can you recommend...'",
        "Use natural language search - customers can describe colors, styles, themes, etc.",
      ],
    },
    maxTurnsBeforeEscalation: 5,
    escalationEnabled: true,
    primaryModel: "gpt-4o",
    fallbackModel: "gpt-5-mini",
    temperature: 0.2,
    reflectionEnabled: true,
    reflectionThreshold: 0.4,
    businessHours: {
      timezone: "Europe/Amsterdam",
      schedule: {
        monday: { start: "09:00", end: "17:00", enabled: true },
        tuesday: { start: "09:00", end: "17:00", enabled: true },
        wednesday: { start: "09:00", end: "17:00", enabled: true },
        thursday: { start: "09:00", end: "17:00", enabled: true },
        friday: { start: "09:00", end: "17:00", enabled: true },
        saturday: { start: "10:00", end: "16:00", enabled: true },
        sunday: { start: "12:00", end: "16:00", enabled: false },
      },
    },
    aura: {
      enabled: true,
      mode: "soft",
      empathyAck: { enabled: true, delayMs: 200, nlText: "", enText: "" },
      baseCaps: { maxCouponEUR: 10, approvalThresholdEUR: 50 },
      locales: ["nl", "en"],
    },
  };
}
