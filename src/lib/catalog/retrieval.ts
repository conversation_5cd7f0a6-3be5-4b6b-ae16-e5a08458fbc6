import { db } from "@/db";
import { sql } from "drizzle-orm";

interface ProductMatch {
  productId: string;
  title: string;
  price: string;
  score: number;
  vendor?: string;
  imageUrl?: string;
}

/**
 * Find products by image embedding using L2 distance
 * Lower distance = better match
 */
export async function findProductsByImage(
  siteId: string,
  imageEmbedding: number[],
  k = 5,
): Promise<ProductMatch[]> {
  // Format embedding for pgvector
  const embeddingStr = `[${imageEmbedding.join(",")}]`;

  const results = await db.execute(sql`
    SELECT 
      p.id as product_id,
      p.title,
      p.price,
      p.vendor,
      i.src as image_url,
      i.embedding <-> ${embeddingStr}::vector as distance
    FROM "once"."catalog_products" p
    INNER JOIN "once"."catalog_images" i ON i.product_id = p.id
    WHERE p.site_id = ${siteId}
      AND i.embedding IS NOT NULL
    ORDER BY distance ASC
    LIMIT ${k}
  `);

  return results.rows.map((row: any) => ({
    productId: row.product_id,
    title: row.title,
    price: row.price,
    vendor: row.vendor,
    imageUrl: row.image_url,
    score: 1 / (1 + row.distance), // Convert distance to similarity score
  }));
}

/**
 * Find products by text embedding using L2 distance
 */
export async function findProductsByText(
  siteId: string,
  textEmbedding: number[],
  k = 5,
): Promise<ProductMatch[]> {
  const embeddingStr = `[${textEmbedding.join(",")}]`;

  const results = await db.execute(sql`
    SELECT 
      p.id as product_id,
      p.title,
      p.price,
      p.vendor,
      p.description,
      t.embedding <-> ${embeddingStr}::vector as distance
    FROM "once"."catalog_products" p
    INNER JOIN "once"."catalog_text_embeds" t ON t.product_id = p.id
    WHERE p.site_id = ${siteId}
      AND t.embedding IS NOT NULL
    ORDER BY distance ASC
    LIMIT ${k}
  `);

  return results.rows.map((row: any) => ({
    productId: row.product_id,
    title: row.title,
    price: row.price,
    vendor: row.vendor,
    score: 1 / (1 + row.distance),
  }));
}

/**
 * Hybrid search combining text and image similarities
 */
export async function hybridProductSearch(
  siteId: string,
  textEmbedding?: number[],
  imageEmbedding?: number[],
  k = 5,
  textWeight = 0.7,
): Promise<ProductMatch[]> {
  if (!textEmbedding && !imageEmbedding) {
    throw new Error("At least one embedding required");
  }

  // If both embeddings provided, combine scores
  if (textEmbedding && imageEmbedding) {
    const textResults = await findProductsByText(siteId, textEmbedding, k * 2);
    const imageResults = await findProductsByImage(siteId, imageEmbedding, k * 2);

    // Combine and re-score
    const combinedMap = new Map<string, ProductMatch>();

    textResults.forEach((result) => {
      combinedMap.set(result.productId, {
        ...result,
        score: result.score * textWeight,
      });
    });

    imageResults.forEach((result) => {
      const existing = combinedMap.get(result.productId);
      if (existing) {
        existing.score += result.score * (1 - textWeight);
        if (result.imageUrl) {
          existing.imageUrl = result.imageUrl;
        }
      } else {
        combinedMap.set(result.productId, {
          ...result,
          score: result.score * (1 - textWeight),
        });
      }
    });

    // Sort by combined score and return top k
    return Array.from(combinedMap.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, k);
  }

  // Single embedding search
  if (textEmbedding) {
    return findProductsByText(siteId, textEmbedding, k);
  }

  return findProductsByImage(siteId, imageEmbedding!, k);
}
