import { redis } from "@/lib/upstash";

interface RateLimitConfig {
  identifier: string;
  maxRequests: number;
  windowMs: number;
}

interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  reset: Date;
}

/**
 * Simple rate limiter using Redis
 * Uses sliding window algorithm
 */
export class RateLimiter {
  /**
   * Check if request is allowed under rate limit
   */
  static async checkLimit(config: RateLimitConfig): Promise<RateLimitResult> {
    const { identifier, maxRequests, windowMs } = config;
    const now = Date.now();
    const windowStart = now - windowMs;

    const key = `ratelimit:${identifier}`;

    // Remove old entries outside the window
    await redis.zremrangebyscore(key, 0, windowStart);

    // Count requests in current window
    const count = await redis.zcard(key);

    if (count >= maxRequests) {
      // Get oldest entry to determine reset time
      const oldestEntries = await redis.zrange(key, 0, 0, { withScores: true });
      const resetTime =
        oldestEntries.length > 0
          ? new Date(((oldestEntries[0] as any).score as number) + windowMs)
          : new Date(now + windowMs);

      return {
        allowed: false,
        remaining: 0,
        reset: resetTime,
      };
    }

    // Add current request
    await redis.zadd(key, { score: now, member: `${now}-${Math.random()}` });

    // Set expiry on the key
    await redis.expire(key, Math.ceil(windowMs / 1000));

    return {
      allowed: true,
      remaining: maxRequests - count - 1,
      reset: new Date(now + windowMs),
    };
  }

  /**
   * Rate limit configurations for different services
   */
  static readonly limits = {
    // OpenAI embeddings: 3000 RPM = 50 per second
    openaiEmbeddings: {
      maxRequests: 50,
      windowMs: 1000, // 1 second
    },

    // Azure Vision: Depends on your tier, adjust accordingly
    azureVision: {
      maxRequests: 10,
      windowMs: 1000, // 1 second
    },

    // Shopify GraphQL: 1000 points per second (cost varies by query)
    shopifyApi: {
      maxRequests: 10,
      windowMs: 1000, // Conservative: 10 requests per second
    },

    // Per-shop webhook processing
    webhookPerShop: {
      maxRequests: 100,
      windowMs: 60000, // 100 webhooks per minute per shop
    },
  };
}

/**
 * Delay execution until rate limit allows
 */
export async function waitForRateLimit(
  identifier: string,
  config: { maxRequests: number; windowMs: number },
): Promise<void> {
  let attempt = 0;
  const maxAttempts = 10;

  while (attempt < maxAttempts) {
    const result = await RateLimiter.checkLimit({ identifier, ...config });

    if (result.allowed) {
      return;
    }

    // Wait until reset time plus a small random delay
    const waitTime = result.reset.getTime() - Date.now() + Math.random() * 1000;
    console.log(`Rate limit hit for ${identifier}, waiting ${Math.ceil(waitTime / 1000)}s`);

    await new Promise((resolve) => setTimeout(resolve, Math.max(waitTime, 1000)));
    attempt++;
  }

  throw new Error(`Rate limit exceeded for ${identifier} after ${maxAttempts} attempts`);
}
