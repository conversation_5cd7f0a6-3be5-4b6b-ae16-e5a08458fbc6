import { db } from "@/db";
import { sites } from "@/db/schema";
import { eq } from "drizzle-orm";

interface WebhookSubscription {
  id: string;
  topic: string;
  endpoint: string;
  createdAt: string;
}

/**
 * Shopify Webhook Manager
 * Handles automatic webhook registration and management for multi-tenant shops
 */
export class ShopifyWebhookManager {
  private accessToken: string;
  private shopDomain: string;
  private apiVersion = "2024-01";

  constructor(accessToken: string, shopDomain: string) {
    this.accessToken = accessToken;
    this.shopDomain = shopDomain;
  }

  /**
   * Register all required webhooks for a shop
   * Called during Shopify OAuth installation flow
   */
  async registerWebhooks(siteId: string): Promise<void> {
    console.log(`📦 Registering webhooks for shop ${this.shopDomain}`);

    // Get the base URL - in development with ngrok, this should be your ngrok URL
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://nousu.nl";

    console.log(`📎 Registering webhooks with base URL: ${baseUrl}`);

    // Define all webhooks we need
    const webhookTopics = [
      // Product catalog webhooks
      "products/create",
      "products/update",
      "products/delete",
      // Order webhooks for customer service
      "orders/create",
      "orders/updated",
      "orders/fulfilled",
      "orders/cancelled",
      // Return/refund webhooks
      "refunds/create",
      "returns/approve",
      "returns/cancel",
      "returns/close",
      "returns/decline",
      "returns/reopen",
      "returns/request",
      // Shop update webhook
      "shop/update",
      // App uninstall webhook
      "app/uninstalled",
    ];

    // Register each webhook
    const results = await Promise.allSettled(
      webhookTopics.map((topic) =>
        this.registerSingleWebhook(topic, `${baseUrl}/api/webhooks/shopify`, siteId),
      ),
    );

    // Log results
    const successful = results.filter((r) => r.status === "fulfilled").length;
    const failed = results.filter((r) => r.status === "rejected");

    console.log(`✅ Registered ${successful}/${webhookTopics.length} webhooks`);

    if (failed.length > 0) {
      console.error("Failed webhooks:", failed);
      // Don't throw - some webhooks might already exist
    }
  }

  /**
   * Register a single webhook
   */
  private async registerSingleWebhook(
    topic: string,
    callbackUrl: string,
    siteId: string,
  ): Promise<WebhookSubscription> {
    const mutation = `
      mutation webhookSubscriptionCreate($topic: WebhookSubscriptionTopic!, $webhookSubscription: WebhookSubscriptionInput!) {
        webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
          webhookSubscription {
            id
            topic
            endpoint {
              ... on WebhookHttpEndpoint {
                callbackUrl
              }
            }
            createdAt
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    // Add site ID to callback URL for multi-tenant routing
    const fullCallbackUrl = `${callbackUrl}?site_id=${siteId}`;

    const variables = {
      topic: topic.toUpperCase().replace("/", "_"),
      webhookSubscription: {
        callbackUrl: fullCallbackUrl,
        format: "JSON",
        includeFields: ["id", "title", "vendor", "body_html", "tags", "images", "variants"],
      },
    };

    const response = await fetch(
      `https://${this.shopDomain}/admin/api/${this.apiVersion}/graphql.json`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": this.accessToken,
        },
        body: JSON.stringify({ query: mutation, variables }),
      },
    );

    const result = await response.json();

    if (result.data?.webhookSubscriptionCreate?.userErrors?.length > 0) {
      const errors = result.data.webhookSubscriptionCreate.userErrors;
      // Check if webhook already exists (not a fatal error)
      const alreadyExists = errors.some((e: any) =>
        e.message.toLowerCase().includes("already exists"),
      );

      if (alreadyExists) {
        console.log(`ℹ️ Webhook ${topic} already exists`);
        return {
          id: "existing",
          topic,
          endpoint: fullCallbackUrl,
          createdAt: new Date().toISOString(),
        };
      }

      throw new Error(`Webhook registration failed: ${JSON.stringify(errors)}`);
    }

    const webhook = result.data?.webhookSubscriptionCreate?.webhookSubscription;
    if (!webhook) {
      throw new Error("No webhook returned from Shopify");
    }

    console.log(`✅ Registered webhook: ${topic}`);

    return {
      id: webhook.id,
      topic: webhook.topic,
      endpoint: fullCallbackUrl,
      createdAt: webhook.createdAt,
    };
  }

  /**
   * List all existing webhooks for debugging
   */
  async listWebhooks(): Promise<WebhookSubscription[]> {
    const query = `
      query {
        webhookSubscriptions(first: 100) {
          edges {
            node {
              id
              topic
              endpoint {
                ... on WebhookHttpEndpoint {
                  callbackUrl
                }
              }
              createdAt
            }
          }
        }
      }
    `;

    const response = await fetch(
      `https://${this.shopDomain}/admin/api/${this.apiVersion}/graphql.json`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": this.accessToken,
        },
        body: JSON.stringify({ query }),
      },
    );

    const result = await response.json();
    const webhooks = result.data?.webhookSubscriptions?.edges || [];

    return webhooks.map((edge: any) => ({
      id: edge.node.id,
      topic: edge.node.topic,
      endpoint: edge.node.endpoint?.callbackUrl || "",
      createdAt: edge.node.createdAt,
    }));
  }

  /**
   * Delete a webhook
   */
  async deleteWebhook(webhookId: string): Promise<void> {
    const mutation = `
      mutation webhookSubscriptionDelete($id: ID!) {
        webhookSubscriptionDelete(id: $id) {
          deletedWebhookSubscriptionId
          userErrors {
            field
            message
          }
        }
      }
    `;

    const response = await fetch(
      `https://${this.shopDomain}/admin/api/${this.apiVersion}/graphql.json`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": this.accessToken,
        },
        body: JSON.stringify({
          query: mutation,
          variables: { id: webhookId },
        }),
      },
    );

    const result = await response.json();

    if (result.data?.webhookSubscriptionDelete?.userErrors?.length > 0) {
      throw new Error(
        `Failed to delete webhook: ${JSON.stringify(
          result.data.webhookSubscriptionDelete.userErrors,
        )}`,
      );
    }
  }

  /**
   * Clean up duplicate or old webhooks
   */
  async cleanupWebhooks(siteId: string): Promise<void> {
    const webhooks = await this.listWebhooks();
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://nousu.nl";

    // Find webhooks that don't match our current URL pattern
    const outdatedWebhooks = webhooks.filter(
      (w) => !w.endpoint.includes(`${baseUrl}/api/webhooks/shopify?site_id=${siteId}`),
    );

    for (const webhook of outdatedWebhooks) {
      try {
        await this.deleteWebhook(webhook.id);
        console.log(`🗑️ Deleted outdated webhook: ${webhook.topic}`);
      } catch (error) {
        console.error(`Failed to delete webhook ${webhook.id}:`, error);
      }
    }
  }
}

/**
 * Register webhooks for a site after Shopify OAuth
 */
export async function registerShopifyWebhooks(siteId: string): Promise<void> {
  const site = await db.query.sites.findFirst({
    where: eq(sites.id, siteId),
  });

  if (!site || !site.platformToken || !site.platformShopId) {
    throw new Error("Site not found or missing Shopify credentials");
  }

  const manager = new ShopifyWebhookManager(site.platformToken, site.platformShopId);

  // Clean up old webhooks first
  await manager.cleanupWebhooks(siteId);

  // Register new webhooks
  await manager.registerWebhooks(siteId);
}
