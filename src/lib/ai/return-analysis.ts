import { ChatOpenAI } from "@langchain/openai";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { StringOutputParser } from "@langchain/core/output_parsers";

interface ChatMessage {
  role: "user" | "assistant" | "system";
  content: string;
  createdAt: Date | null;
}

interface ReturnAnalysis {
  sentiment: "positive" | "neutral" | "frustrated" | "angry";
  recommendation: "approve" | "deny" | "ask_questions";
  confidence: number;
  reasoning: string;
  conversationSummary: string;
  riskFactors: string[];
  customerProfile: string;
}

export async function analyzeReturn(
  orderNumber: string,
  orderValue: number,
  chatHistory: ChatMessage[],
  customerEmail: string,
  returnReason: string,
): Promise<ReturnAnalysis> {
  const model = new ChatOpenAI({
    model: "gpt-4o",
    temperature: 0.1,
    maxCompletionTokens: 1000,
  });

  const conversationText = chatHistory
    .filter((msg) => msg.role !== "system")
    .map((msg) => `${msg.role === "user" ? "Klant" : "Agent"}: ${msg.content}`)
    .join("\n");

  const prompt = ChatPromptTemplate.fromTemplate(`
Je bent een ervaren customer service manager die retourverzoeken beoordeelt. Analyseer het volgende retourverzoek en geef een professionele beoordeling.

BESTELLING DETAILS:
- Bestelnummer: {orderNumber}
- Orderwaarde: €{orderValue}
- Klant email: {customerEmail}
- Retour reden: {returnReason}

GESPREK VERLOOP:
{conversationText}

BELANGRIJK: Antwoord ALLEEN met geldige JSON. Geen markdown, geen code blocks, geen extra tekst. Alleen puur JSON:

{{
  "sentiment": "positive|neutral|frustrated|angry",
  "recommendation": "approve|deny|ask_questions", 
  "confidence": 0.8,
  "reasoning": "Korte uitleg waarom je deze keuze maakt",
  "conversationSummary": "Samenvatting van het gesprek in 1-2 zinnen",
  "riskFactors": ["array", "of", "risk", "factors"],
  "customerProfile": "Beschrijving van het type klant (nieuw/terugkerend/problematisch)"
}}

Overwegingen:
- Retourwaarde onder €50: meestal goedkeuren
- Klant toon en redelijkheid 
- Legitimiteit van de reden
- Mogelijke fraude indicatoren
- Klantrelatie behoud
`);

  try {
    const result = await prompt
      .pipe(model)
      .pipe(new StringOutputParser())
      .invoke({
        orderNumber,
        orderValue: orderValue.toFixed(2),
        customerEmail,
        returnReason,
        conversationText: conversationText || "Geen chat geschiedenis beschikbaar",
      });

    // Clean and parse the JSON response
    let cleanResult = result.trim();

    // Remove markdown code blocks if present
    if (cleanResult.startsWith("```json")) {
      cleanResult = cleanResult.replace(/^```json\n?/, "").replace(/\n?```$/, "");
    } else if (cleanResult.startsWith("```")) {
      cleanResult = cleanResult.replace(/^```\n?/, "").replace(/\n?```$/, "");
    }

    const analysis = JSON.parse(cleanResult) as ReturnAnalysis;

    // Validate required fields
    if (!analysis.sentiment || !analysis.recommendation || !analysis.reasoning) {
      throw new Error("Incomplete analysis from AI");
    }

    return analysis;
  } catch (error) {
    console.error("Error analyzing return:", error);

    // Fallback analysis
    return {
      sentiment: "neutral",
      recommendation: orderValue < 50 ? "approve" : "ask_questions",
      confidence: 0.5,
      reasoning: "Automatische analyse mislukt - handmatige beoordeling aanbevolen",
      conversationSummary: "Chat geschiedenis kon niet worden geanalyseerd",
      riskFactors: ["Analysis failed"],
      customerProfile: "Onbekend profiel",
    };
  }
}

export function getRecommendationColor(recommendation: string): string {
  switch (recommendation) {
    case "approve":
      return "text-green-600 bg-green-50";
    case "deny":
      return "text-red-600 bg-red-50";
    case "ask_questions":
      return "text-yellow-600 bg-yellow-50";
    default:
      return "text-gray-600 bg-gray-50";
  }
}

export function getSentimentIcon(sentiment: string): string {
  switch (sentiment) {
    case "positive":
      return "😊";
    case "neutral":
      return "😐";
    case "frustrated":
      return "😤";
    case "angry":
      return "😡";
    default:
      return "🤔";
  }
}

export function getConfidenceText(confidence: number): string {
  if (confidence >= 0.8) return "Hoge zekerheid";
  if (confidence >= 0.6) return "Gemiddelde zekerheid";
  return "Lage zekerheid";
}
