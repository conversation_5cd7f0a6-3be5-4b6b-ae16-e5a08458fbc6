import { OpenA<PERSON> } from "openai";
import { db } from "@/db";
import { knowledgeChunks, knowledgeSources, knowledgeTopics } from "@/db/schema";
import { eq, and, sql, desc } from "drizzle-orm";
import { redis } from "@/lib/upstash";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

/**
 * Generate embeddings for text using OpenAI's embedding model
 */
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const response = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: text,
    });

    return response.data[0].embedding;
  } catch (error) {
    console.error("Error generating embedding:", error);
    throw new Error("Failed to generate embedding");
  }
}

/**
 * Search knowledge base for relevant chunks using vector similarity
 */
export async function searchKnowledgeBase(
  query: string,
  siteId: string,
  options: {
    limit?: number;
    minScore?: number;
    topicId?: string;
    useCache?: boolean;
  } = {},
) {
  const { limit = 5, minScore = 0.7, topicId, useCache = true } = options;

  // Check cache first
  if (useCache) {
    const cacheKey = `kb:search:${siteId}:${query}:${topicId || "all"}`;
    const cached = await redis.get(cacheKey);
    if (cached) {
      console.log("Knowledge base search cache hit");
      return JSON.parse(cached as string);
    }
  }

  // Generate embedding for the query
  const queryEmbedding = await generateEmbedding(query);

  // Format embedding for pgvector - it expects a string in format '[0.1,0.2,0.3,...]'
  const embeddingString = `[${queryEmbedding.join(",")}]`;

  // Execute the query with proper schema prefixes
  const results = await db.execute(sql`
    SELECT 
      kc.id,
      kc.content,
      kc.criticality_score,
      kc.clarity_score,
      kc.actionability_score,
      kc.expected_frequency_score,
      1 - (kc.embedding <=> ${embeddingString}::vector) as similarity,
      ks.label as source_label,
      ks.kind as source_kind,
      ks.meta as source_meta,
      kt.name as topic_name,
      kt.slug as topic_slug
    FROM "once"."knowledge_chunks" kc
    INNER JOIN "once"."knowledge_sources" ks ON kc.source_id = ks.id
    LEFT JOIN "once"."knowledge_topics" kt ON ks.topic_id = kt.id
    WHERE ks.site_id = ${siteId}
    ${topicId ? sql`AND ks.topic_id = ${topicId}` : sql``}
    AND kc.embedding IS NOT NULL
    AND 1 - (kc.embedding <=> ${embeddingString}::vector) >= ${minScore}
    ORDER BY 
      similarity DESC,
      kc.criticality_score DESC,
      kc.expected_frequency_score DESC
    LIMIT ${limit}
  `);

  // Transform results to a more usable format
  const formattedResults = results.rows.map((row: any) => ({
    id: row.id,
    content: row.content,
    similarity: row.similarity,
    scores: {
      criticality: row.criticality_score,
      clarity: row.clarity_score,
      actionability: row.actionability_score,
      expectedFrequency: row.expected_frequency_score,
    },
    source: {
      label: row.source_label,
      kind: row.source_kind,
      meta: row.source_meta,
    },
    topic: row.topic_name
      ? {
          name: row.topic_name,
          slug: row.topic_slug,
        }
      : null,
  }));

  // Cache the results for 5 minutes
  if (useCache && formattedResults.length > 0) {
    const cacheKey = `kb:search:${siteId}:${query}:${topicId || "all"}`;
    await redis.set(cacheKey, JSON.stringify(formattedResults), { ex: 300 });
  }

  console.log("Results: ", formattedResults);

  return formattedResults;
}

/**
 * Generate embeddings for all chunks of a knowledge source
 * Used by background jobs when articles are created/updated
 */
export async function generateSourceEmbeddings(sourceId: string) {
  // Get all chunks for this source that don't have embeddings
  const chunks = await db
    .select()
    .from(knowledgeChunks)
    .where(and(eq(knowledgeChunks.sourceId, sourceId), sql`${knowledgeChunks.embedding} IS NULL`));

  console.log(`Generating embeddings for ${chunks.length} chunks from source ${sourceId}`);

  // Process chunks in batches to avoid rate limits
  const batchSize = 20;
  for (let i = 0; i < chunks.length; i += batchSize) {
    const batch = chunks.slice(i, i + batchSize);

    // Generate embeddings for the batch
    const embeddings = await Promise.all(batch.map((chunk) => generateEmbedding(chunk.content)));

    // Update chunks with embeddings
    for (let j = 0; j < batch.length; j++) {
      await db
        .update(knowledgeChunks)
        .set({
          embedding: embeddings[j] as any, // pgvector handles the array conversion
        })
        .where(eq(knowledgeChunks.id, batch[j].id));
    }

    // Small delay to respect rate limits
    if (i + batchSize < chunks.length) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }

  console.log(`Successfully generated embeddings for source ${sourceId}`);
}

/**
 * Find similar knowledge chunks to a given chunk
 * Useful for deduplication and finding related content
 */
export async function findSimilarChunks(chunkId: string, siteId: string, limit = 5) {
  // Get the chunk's embedding
  const chunk = await db.query.knowledgeChunks.findFirst({
    where: eq(knowledgeChunks.id, chunkId),
  });

  if (!chunk || !chunk.embedding) {
    throw new Error("Chunk not found or has no embedding");
  }

  // Find similar chunks
  const similarQuery = sql`
    SELECT 
      kc.id,
      kc.content,
      1 - (kc.embedding <=> ${chunk.embedding}::vector) as similarity,
      ks.label as source_label
    FROM ${knowledgeChunks} kc
    INNER JOIN ${knowledgeSources} ks ON kc.source_id = ks.id
    WHERE ks.site_id = ${siteId}
    AND kc.id != ${chunkId}
    AND kc.embedding IS NOT NULL
    AND 1 - (kc.embedding <=> ${chunk.embedding}::vector) > 0.8
    ORDER BY similarity DESC
    LIMIT ${limit}
  `;

  const results = await db.execute(similarQuery);
  return results.rows;
}
