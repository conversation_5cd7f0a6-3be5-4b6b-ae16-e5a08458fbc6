import { z } from "zod";

export const EmotionEnum = z.enum([
  "neutral",
  "frustrated",
  "angry",
  "confused",
  "sad",
  "happy",
  "urgent",
]);

export const UrgencyEnum = z.enum(["low", "medium", "high"]);

export const StyleTagEnum = z.enum([
  "ack",
  "apologize_once",
  "assure_action",
  "de_escalate",
  "short_sentences",
  "step_by_step",
  "ask_for_info",
  "positive_closure",
]);

export const SingleShotOutSchema = z.object({
  emotion: EmotionEnum,
  urgency: UrgencyEnum.default("low"),
  style_tags: z.array(StyleTagEnum).default([]),
  reply: z.string(),
});

export type Emotion = z.infer<typeof EmotionEnum>;
export type Urgency = z.infer<typeof UrgencyEnum>;
export type StyleTag = z.infer<typeof StyleTagEnum>;
export type SingleShotOut = z.infer<typeof SingleShotOutSchema>;

export interface Affect {
  emotion: Emotion;
  urgency: Urgency;
}