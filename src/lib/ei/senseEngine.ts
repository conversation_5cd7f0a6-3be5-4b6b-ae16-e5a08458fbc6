/**
 * Nousu SENSE™ - Unified Adaptive Understanding & Response Architecture
 * Performance + Accuracy + Cost optimized emotional intelligence
 */

import { ChatOpenAI } from "@langchain/openai";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { Emotion, Urgency, StyleTag, Affect } from "./senseTypes";

export interface AuraConfig {
  enabled: boolean;
  mode: "shadow" | "soft" | "full";
  empathyAck?: {
    enabled: boolean;
    delayMs: number;
    nlText: string;
    enText: string;
  };
  baseCaps?: { maxCouponEUR: number; approvalThresholdEUR: number };
  locales?: string[];
}

export interface ConversationContext {
  previousMessages: Array<{ role: "user" | "assistant"; content: string }>;
  customerName?: string;
  issueHistory?: string[];
  timeOfDay: number;
  messageCount: number;
  hasAssistantSpoken: boolean;
}

export interface AuraResult {
  emotion: Emotion;
  urgency: Urgency;
  style_tags: StyleTag[];
  confidence: number;
  detectionMethod: "fast" | "llm" | "context";
  empathyGuidance: {
    shouldAddEmpathy: boolean;
    empathyType:
      | "acknowledgment"
      | "apology"
      | "reassurance"
      | "escalation"
      | "greeting"
      | "none";
    urgencyLevel: "low" | "medium" | "high";
  };
  shouldEscalate: boolean;
}

/**
 * Fast pattern-based emotion detection for obvious cases
 */
function fastEmotionDetection(
  message: string
): { emotion: Emotion; urgency: Urgency; confidence: number } | null {
  const lower = message.toLowerCase();
  const hasIntensity = /[A-Z]{3,}/.test(message);
  const multipleExclamations = (message.match(/!/g) || []).length >= 3;
  const hasSwearing = /\b(kut|klote|shit|fuck|godverdomme)\b/.test(lower);

  // High-confidence anger
  if (
    (hasSwearing || /\b(stoppen|nooit meer|schuld)\b/.test(lower)) &&
    (hasIntensity || multipleExclamations)
  ) {
    return { emotion: "angry", urgency: "high", confidence: 0.9 };
  }

  // Mild frustration
  if (/\b(ik snap het niet|hoezo|waarom)\b/.test(lower)) {
    return { emotion: "frustrated", urgency: "medium", confidence: 0.85 };
  }

  // High-confidence gratitude
  if (
    /\b(dank|bedankt|geweldig|perfect|super)\b/.test(lower) &&
    !/\b(niet|geen|maar)\b/.test(lower)
  ) {
    return { emotion: "happy", urgency: "low", confidence: 0.88 };
  }

  // Clear urgency markers
  if (/\b(urgent|spoed|asap|meteen|snel)\b/.test(lower)) {
    return { emotion: "urgent", urgency: "high", confidence: 0.85 };
  }

  return null;
}

/**
 * Empathy + escalation decision logic
 */
function decideEmpathyAndEscalation(
  emotion: Emotion,
  urgency: Urgency,
  confidence: number,
  context: ConversationContext,
  style_tags: StyleTag[]
) {
  const highEmotion = emotion === "angry" || emotion === "frustrated";
  const highUrgency = urgency === "high";

  const shouldEscalate =
    (highEmotion && highUrgency) ||
    (emotion === "angry" && urgency === "medium" && context.messageCount > 1) ||
    (confidence > 0.8 &&
      style_tags.includes("de_escalate") &&
      context.messageCount > 2);

  const shouldAddEmpathy =
    highEmotion ||
    emotion === "confused" ||
    emotion === "sad" ||
    !context.hasAssistantSpoken;

  return { shouldEscalate, shouldAddEmpathy };
}

/**
 * Generate empathy guidance metadata
 */
function generateEmpathyGuidance(
  affect: Affect,
  context: ConversationContext
) {
  const { emotion, urgency } = affect;
  const { hasAssistantSpoken, messageCount } = context;

  if (emotion === "angry" && urgency === "high") {
    return {
      shouldAddEmpathy: true,
      empathyType: messageCount > 2 ? "escalation" : "apology",
      urgencyLevel: "high",
    };
  }
  if (emotion === "frustrated" && urgency === "high") {
    return {
      shouldAddEmpathy: true,
      empathyType: "reassurance",
      urgencyLevel: "high",
    };
  }
  if (emotion === "confused") {
    return {
      shouldAddEmpathy: true,
      empathyType: "acknowledgment",
      urgencyLevel: "low",
    };
  }
  if (emotion === "sad") {
    return {
      shouldAddEmpathy: true,
      empathyType: "apology",
      urgencyLevel: "medium",
    };
  }
  if (!hasAssistantSpoken) {
    return {
      shouldAddEmpathy: true,
      empathyType: "greeting",
      urgencyLevel: "low",
    };
  }
  return {
    shouldAddEmpathy: false,
    empathyType: "none",
    urgencyLevel: "low",
  };
}

/**
 * LLM-based emotion detection
 */
async function llmEmotionDetection(
  message: string,
  context: ConversationContext
) {
  try {
    const model = new ChatOpenAI({
      model: "gpt-4o-mini",
      temperature: 0,
      maxTokens: 150,
    });

    const contextStr = context.previousMessages
      .slice(-2)
      .map((m) => `${m.role}: ${m.content}`)
      .join("\n");

    const prompt = ChatPromptTemplate.fromTemplate(`
Analyze Dutch customer message for emotion, considering conversation context and cultural nuances.

CONTEXT:
${contextStr}

CURRENT MESSAGE: "{message}"

Respond ONLY with JSON:
{
  "emotion": "neutral|frustrated|angry|confused|sad|happy|urgent",
  "urgency": "low|medium|high",
  "style_tags": ["ack","apologize_once","assure_action","de_escalate","short_sentences","step_by_step","ask_for_info","positive_closure"],
  "confidence": 0.0-1.0
}

Dutch cultural context:
- Direct communication style
- "je/jij" informal tone
- Context matters more than words
- Understatement vs emphasis`);

    const result = await prompt.pipe(model).invoke({ message });
    const parsed = JSON.parse((result.content as string).trim());

    return {
      emotion: parsed.emotion as Emotion,
      urgency: parsed.urgency as Urgency,
      style_tags: (parsed.style_tags || []) as StyleTag[],
      confidence: Math.min(parsed.confidence || 0.7, 0.9),
    };
  } catch (error) {
    console.error("LLM emotion detection failed:", error);
    return {
      emotion: "neutral" as Emotion,
      urgency: "low" as Urgency,
      style_tags: [] as StyleTag[],
      confidence: 0.3,
    };
  }
}

/**
 * Main AURA processing engine
 */
export async function processAura(
  message: string,
  context: ConversationContext,
  config: AuraConfig
): Promise<AuraResult> {
  if (!config.enabled) {
    return {
      emotion: "neutral",
      urgency: "low",
      style_tags: [],
      confidence: 0,
      detectionMethod: "fast",
      empathyGuidance: {
        shouldAddEmpathy: false,
        empathyType: "none",
        urgencyLevel: "low",
      },
      shouldEscalate: false,
    };
  }

  // Step 1: Fast path
  const fastResult = fastEmotionDetection(message);
  let emotion: Emotion;
  let urgency: Urgency;
  let style_tags: StyleTag[] = [];
  let confidence: number;
  let detectionMethod: "fast" | "llm" | "context";

  if (fastResult && fastResult.confidence > 0.8) {
    ({ emotion, urgency, confidence } = fastResult);
    detectionMethod = "fast";
  } else {
    // Step 2: LLM path
    const llmResult = await llmEmotionDetection(message, context);
    ({ emotion, urgency, style_tags, confidence } = llmResult);
    detectionMethod = "llm";
  }

  // Step 3: Add style tag hints (mapped to enum)
  if (emotion === "angry") style_tags.push("de_escalate");
  if (emotion === "frustrated") style_tags.push("step_by_step");
  if (emotion === "happy") style_tags.push("positive_closure");

  // Step 4: Decide empathy + escalation
  const { shouldEscalate, shouldAddEmpathy } = decideEmpathyAndEscalation(
    emotion,
    urgency,
    confidence,
    context,
    style_tags
  );

  const empathyGuidance = shouldAddEmpathy
    ? generateEmpathyGuidance({ emotion, urgency }, context)
    : {
        shouldAddEmpathy: false,
        empathyType: "none" as const,
        urgencyLevel: "low" as const,
      };

  return {
    emotion,
    urgency,
    style_tags,
    confidence,
    detectionMethod,
    empathyGuidance: {
      shouldAddEmpathy: empathyGuidance.shouldAddEmpathy,
      empathyType: empathyGuidance.empathyType as "acknowledgment" | "apology" | "reassurance" | "escalation" | "greeting" | "none",
      urgencyLevel: empathyGuidance.urgencyLevel as "low" | "medium" | "high"
    },
    shouldEscalate,
  };
}

/**
 * Provide AURA guidance for response modification
 */
export function getAuraGuidance(
  auraResult: AuraResult,
  config: AuraConfig
) {
  if (config.mode === "shadow" || !auraResult.empathyGuidance.shouldAddEmpathy) {
    return {
      shouldModifyResponse: false,
      guidance: "No empathy modification needed",
      priority: "low" as const,
    };
  }

  const { empathyType } = auraResult.empathyGuidance;
  let guidance = "";
  let priority: "low" | "medium" | "high" = "low";

  switch (empathyType) {
    case "escalation":
      guidance = "Consider escalating to human staff and acknowledge frustration";
      priority = "high";
      break;
    case "apology":
      guidance = "Add acknowledgment and apology for the situation";
      priority = "medium";
      break;
    case "reassurance":
      guidance = "Provide reassurance and commitment to help";
      priority = "medium";
      break;
    case "acknowledgment":
      guidance = "Acknowledge the customer's confusion and offer clear guidance";
      priority = "low";
      break;
    case "greeting":
      guidance = "Include appropriate time-based greeting";
      priority = "low";
      break;
    default:
      return {
        shouldModifyResponse: false,
        guidance: "No specific empathy needed",
        priority: "low" as const,
      };
  }

  return {
    shouldModifyResponse: true,
    guidance,
    priority,
  };
}