export function empathyAckText(
  locale: "nl" | "en",
  override?: string
): string {
  if (override) return override;
  return locale === "nl"
    ? "Ik kijk het meteen voor je na…"
    : "Let me check that for you right away…";
}

/**
 * Wrap a streaming send with an early empathy ack if model is slow to start.
 * onAck is called only if no first token by delayMs.
 */
export async function streamWithEmpathyAck<T>({
  startStream,
  onAck,
  delayMs = 180,
}: {
  startStream: () => Promise<T>;
  onAck: () => void;
  delayMs?: number;
}): Promise<T> {
  let acked = false;
  const t = setTimeout(() => {
    acked = true;
    onAck();
  }, delayMs);

  try {
    const res = await startStream();
    clearTimeout(t);
    if (!acked) {
      // Ack not sent; fine.
    }
    return res;
  } catch (e) {
    clearTimeout(t);
    throw e;
  }
}