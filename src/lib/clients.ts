import { createOpenAI } from "@ai-sdk/openai";
import { db } from "@/db";
import { sites } from "@/db/schema";
import { eq } from "drizzle-orm";
import { waitForRateLimit, RateLimiter } from "@/lib/rate-limiter";

export const openai = createOpenAI({ apiKey: process.env.OPENAI_API_KEY });

/**
 * Shopify GraphQL client for interacting with the Admin API
 */
export class ShopifyClient {
  private accessToken: string;
  private shopDomain: string;

  constructor(accessToken: string, shopDomain: string) {
    this.accessToken = accessToken;
    this.shopDomain = shopDomain;
  }

  /**
   * Create a ShopifyClient instance from a site ID
   */
  static async fromSiteId(siteId: string): Promise<ShopifyClient> {
    const site = await db.query.sites.findFirst({
      where: eq(sites.id, siteId),
    });

    if (!site || !site.platformToken || !site.platformShopId) {
      throw new Error("Site not found or missing Shopify credentials");
    }

    return new ShopifyClient(site.platformToken, site.platformShopId);
  }

  /**
   * Execute a GraphQL query against the Shopify Admin API
   */
  async query<T>(query: string, variables: Record<string, any> = {}): Promise<T> {
    // Apply rate limiting for Shopify API
    await waitForRateLimit(`shopify:${this.shopDomain}`, RateLimiter.limits.shopifyApi);

    const endpoint = `https://${this.shopDomain}/admin/api/2024-01/graphql.json`;

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": this.accessToken,
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify GraphQL error: ${errorText}`);
    }

    const result = await response.json();

    if (result.errors) {
      throw new Error(`Shopify GraphQL errors: ${JSON.stringify(result.errors)}`);
    }

    // Return the data portion of the response
    return (result.data || result) as T;
  }

  /**
   * Create a return for an order using Shopify's ReturnInput format
   */
  async createReturn(input: {
    orderId: string;
    returnLineItems: Array<{
      fulfillmentLineItemId: string;
      quantity: number;
      returnReason?: string;
      returnReasonNote?: string;
    }>;
  }) {
    const { orderId, returnLineItems } = input;

    const mutation = `
      mutation ReturnCreate($returnInput: ReturnInput!) {
        returnCreate(returnInput: $returnInput) {
          userErrors {
            field
            message
          }
          return {
            id
            order {
              id
            }
          }
        }
      }
    `;

    const variables = {
      returnInput: {
        orderId,
        returnLineItems: returnLineItems.map((item) => ({
          fulfillmentLineItemId: item.fulfillmentLineItemId,
          quantity: item.quantity,
          ...(item.returnReason ? { returnReason: item.returnReason } : {}),
          ...(item.returnReasonNote ? { returnReasonNote: item.returnReasonNote } : {}),
        })),
      },
    };

    return this.query<{
      returnCreate: {
        return: { id: string; order: { id: string } } | null;
        userErrors: Array<{ field: string; message: string }>;
      };
    }>(mutation, variables);
  }

  /**
   * Get order with fulfillment details for return creation
   */
  async getOrderWithFulfillments(orderNumber: string) {
    const query = `
      query GetOrderWithFulfillments($query: String!) {
        orders(first: 1, query: $query) {
          edges {
            node {
              id
              name
              displayFulfillmentStatus
              displayFinancialStatus
              createdAt
              totalPrice
              currencyCode
              fulfillments {
                id
                status
                trackingInfo {
                  number
                  url
                  company
                }
                fulfillmentLineItems(first: 50) {
                  edges {
                    node {
                      id
                      lineItem {
                        id
                        title
                        variant {
                          title
                        }
                      }
                      quantity
                    }
                  }
                }
              }
              lineItems(first: 10) {
                edges {
                  node {
                    id
                    title
                    quantity
                    variant {
                      title
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `name:${orderNumber}`,
    };

    const response = await this.query<{
      orders: {
        edges: Array<{
          node: {
            id: string;
            name: string;
            displayFulfillmentStatus: string;
            displayFinancialStatus: string;
            createdAt: string;
            totalPrice: string;
            currencyCode: string;
            fulfillments: Array<{
              id: string;
              status: string;
              trackingInfo: Array<{
                number: string;
                url: string;
                company: string;
              }>;
              fulfillmentLineItems: {
                edges: Array<{
                  node: {
                    id: string;
                    lineItem: {
                      id: string;
                      title: string;
                      variant: {
                        title: string;
                      };
                    };
                    quantity: number;
                  };
                }>;
              };
            }>;
            lineItems: {
              edges: Array<{
                node: {
                  id: string;
                  title: string;
                  quantity: number;
                  variant: {
                    title: string;
                  };
                };
              }>;
            };
          };
        }>;
      };
    }>(query, variables);

    return response.orders.edges.length > 0 ? response.orders.edges[0].node : null;
  }

  /**
   * Get order by order number/name
   */
  async getOrderByNumber(orderNumber: string) {
    const query = `
      query GetOrder($query: String!) {
        orders(first: 1, query: $query) {
          edges {
            node {
              id
              name
              displayFulfillmentStatus
              displayFinancialStatus
              createdAt
              totalPrice
              currencyCode
              fulfillments {
                status
                trackingInfo {
                  number
                  url
                }
              }
              lineItems(first: 10) {
                edges {
                  node {
                    title
                    quantity
                    variant {
                      title
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `name:${orderNumber}`,
    };

    const response = await this.query<{
      orders: {
        edges: Array<{
          node: {
            id: string;
            name: string;
            displayFulfillmentStatus: string;
            displayFinancialStatus: string;
            createdAt: string;
            totalPrice: string;
            currencyCode: string;
            fulfillments: Array<{
              status: string;
              trackingInfo: Array<{
                number: string;
                url: string;
              }>;
            }>;
            lineItems: {
              edges: Array<{
                node: {
                  title: string;
                  quantity: number;
                  variant: {
                    title: string;
                  };
                };
              }>;
            };
          };
        }>;
      };
    }>(query, variables);

    return response.orders.edges.length > 0 ? response.orders.edges[0].node : null;
  }

  /**
   * Create a refund for an order
   */
  async createRefund(input: {
    orderId: string;
    amount?: number;
    note?: string;
    notify?: boolean;
    locationId?: string;
    refundLineItems?: Array<{
      lineItemId: string;
      quantity: number;
      restockType?: "RETURN" | "CANCEL" | "LEGACY_RESTOCK" | "NO_RESTOCK";
    }>;
  }) {
    const { orderId, amount, note, notify = true, locationId, refundLineItems } = input;

    const mutation = `
      mutation RefundCreate($input: RefundInput!) {
        refundCreate(input: $input) {
          userErrors {
            field
            message
          }
          refund {
            id
            totalRefunded {
              amount
              currencyCode
            }
            order {
              id
            }
          }
        }
      }
    `;

    const refundInput: any = {
      orderId,
      notify,
      ...(note && { note }),
      // Note: reason is not a valid field in Shopify's RefundInput, only note is supported
    };

    // If specific line items are provided, refund those
    if (refundLineItems && refundLineItems.length > 0) {
      refundInput.refundLineItems = refundLineItems.map((item) => {
        const refundItem: any = {
          lineItemId: item.lineItemId,
          quantity: item.quantity,
          restockType: item.restockType || "RETURN",
        };

        // Add location for restocking if restockType is RETURN and locationId is provided
        if ((item.restockType === "RETURN" || !item.restockType) && locationId) {
          refundItem.locationId = locationId;
        }

        return refundItem;
      });
    } else if (amount) {
      // If a specific amount is provided, use that
      refundInput.amount = amount.toString();
    }

    const variables = { input: refundInput };

    return this.query<{
      refundCreate: {
        refund: {
          id: string;
          totalRefunded: { amount: string; currencyCode: string };
          order: { id: string };
        } | null;
        userErrors: Array<{ field: string; message: string }>;
      };
    }>(mutation, variables);
  }

  /**
   * Test the connection to Shopify and verify scopes
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      // Query shop to verify connection
      const query = `
        query {
          shop {
            name
            id
          }
        }
      `;

      const result = await this.query<{ shop: { name: string; id: string } }>(query);

      return {
        success: true,
        message: `Successfully connected to ${result.shop.name} (${result.shop.id})`,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Get the primary location for the shop
   */
  async getPrimaryLocation(): Promise<{ id: string; name: string } | null> {
    const query = `
      query {
        locations(first: 1) {
          edges {
            node {
              id
              name
            }
          }
        }
      }
    `;

    try {
      const result = await this.query<{
        locations: {
          edges: Array<{
            node: {
              id: string;
              name: string;
            };
          }>;
        };
      }>(query);

      if (result.locations.edges.length > 0) {
        return result.locations.edges[0].node;
      }

      return null;
    } catch (error) {
      console.error("Error getting primary location:", error);
      return null;
    }
  }
}
