import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import NextAuth from "next-auth";
import authConfig from "./auth.config";

const { auth: edgeAuth } = NextAuth(authConfig);

export async function middleware(request: NextRequest) {
  // Skip onboarding check for auth routes and api routes
  if (
    request.nextUrl.pathname.startsWith("/login") ||
    request.nextUrl.pathname.startsWith("/signup") ||
    request.nextUrl.pathname.startsWith("/api") ||
    request.nextUrl.pathname.startsWith("/")
  ) {
    return NextResponse.next();
  }

  const session = await edgeAuth();

  if (!session?.user?.id) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  // Check if user is part of any organization via API
  const response = await fetch(new URL("/api/auth/check-org", request.url));
  const { hasOrg } = await response.json();

  // If user has no org and not in onboarding, redirect to onboarding
  if (!hasOrg && !request.nextUrl.pathname.startsWith("/onboarding")) {
    return NextResponse.redirect(new URL("/onboarding", request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
