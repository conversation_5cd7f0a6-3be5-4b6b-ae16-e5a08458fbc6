Async background jobs for returns. This is the eventual goal we want to achieve:

E-mail notifications are absolutely mandatory for some processes. Let's think of an example scenario:
Customer opens chat and wants to return a T-Shirt because the size was not right.
AI agent understand what happens. Looks at the knowledgebase of the website to understand when a return can be accepted.
AI agent can ask some questions to the user to make sure the user can actually return
User can return
AI agent start the job and tells user that it can take some time to review the return

This is where we could go two ways:
We can maybe give the webshop owner the option to allow the agent to create a return without human review

Or we can make the agent ask the website staff, give a summary about the chat, what order it was, how long ago and why the return was created.
Webshop can  accept or deny it, but it can also ask the AI to ask a few follow up questions for the customer so they make sure the return gets handled correctly.

The Agent sends the user a mail (This should not be hardcoded. the texts for these mails should entirely be generated by the Agent based on the situation)
For example asking for more context with a link to a chatbox where the customer can respond. Then, the AI says something around the lines of: Alright, i'll pick this up further and keep you posted.

AI agent notifies the webshop staff, they now accept the return. 

Agent sends an e-mail to the customer saying the return is accepted with sending instructions for example.

For this system, we also have to keep in mind that when the webshop staff is already home for the day. There can be like 12 hours between the start of the chat/job and sending an email. 


Multi-Stage Return System - Designed for Real-World Delays
I've already designed this system specifically to handle those long delays gracefully. Here's what we've implemented:
Immediate Response (Customer sees results in seconds)

Email Bridge System (Spans the 12+ hour gap)
Confirmation Email: Sent immediately to customer
Staff Notification: Instant alert to staff dashboard/Slack
Scheduled Reminders: 12-hour automatic reminder if staff hasn't responded
AI-Generated Content: Personalized emails based on conversation context
Multi-Stage Job Queue
// Jobs can now have stages and wait for human input
export type JobStatus = "queued" | "running" | "done" | "error" | "awaiting_review" | "scheduled";

// Stage-based processing
case "initial": await processInitialReturnRequest(payload);
case "review_decision": await processReviewDecision(payload);
case "create_shopify_return": await createShopifyReturn(payload);
;
Staff Dashboard API
// GET /api/returns?siteId=xxx&status=pending
// POST /api/returns { returnRequestId, decision: "approve|deny|ask_questions" }

The Complete 24-Hour Timeline
8:00 PM - Customer: "I want to return this T-shirt"
AI validates eligibility, creates return request
Customer gets: "Your return is submitted, you'll hear back in 24h"
Email sent: "Return request received"
Staff notified via dashboard/Slack (eventually Slack, for now just our dashboard)
[12+ hours - Staff sleeping/busy]
System schedules reminder emails
Customer has confirmation email to reference
Return request safely stored in database
8:00 AM Next Day - Staff arrives at work
Sees return in dashboard with full chat context
Reviews: "Order #1005, €44.90, Size issue, customer chat summary"
Clicks "Approve" → Triggers next stage jobs
8:05 AM - Automated follow-up
Customer gets: "Return approved! Expect return label soon"
Shopify return created automatically
Return label email scheduled
Key Features for Long Delays
Persistent State: Everything saved in database tables
Email Continuity: Customer doesn't lose context
Staff Context: AI-generated chat summaries for review
Automatic Reminders: System prods staff if they forget
Multi-Channel Notifications: Dashboard + Slack + Email
What happens if staff is away for days?
Reminder emails escalate (12h, 24h, 48h)
System could auto-approve low-risk returns (configurable)
Emergency contact notifications for high-value returns
Customer gets status updates via email
This system is designed exactly for this scenario - where a customer starts a conversation at night, staff reviews the next morning (or even later), and email bridges keep everyone informed throughout the delay!

