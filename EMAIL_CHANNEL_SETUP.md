# Email Channel Setup with ForwardEmail.net

This document explains how to configure the email channel for your Nousu AI customer support platform using ForwardEmail.net.

## Webhook Configuration

### API Endpoint
Configure your ForwardEmail.net webhook to send to:
```
POST https://yourdomain.com/api/email/inbound
```

### Webhook Payload
The API automatically handles ForwardEmail.net webhook payloads with this structure:
- `session.recipient` - The inbound email address (e.g., `<EMAIL>`)
- `session.sender` - The customer's email address
- `from.value[0].address` - Customer email (alternative source)
- `from.value[0].name` - Customer name
- `text` - Plain text email content
- `html` - HTML email content (optional)
- `messageId` - Unique message identifier
- `headerLines` - Email headers (for subject, threading, etc.)

## Email Address Generation

Each site automatically gets its own inbound email address:
- **Site Name**: "DutchFashion" → **Email**: `<EMAIL>`
- **Site Name**: "Sports Store" → **Email**: `<EMAIL>`

The system:
1. Converts site names to lowercase
2. Removes special characters
3. Limits to 50 characters
4. Appends `@inbound.nousu.nl`

## Email Flow Setup

### 1. ForwardEmail.net Configuration
```
Customer Email: <EMAIL>
↓ emails to ↓
<EMAIL> (your business email)
↓ forwards to ↓
<EMAIL> (Nousu inbound)
↓ webhook calls ↓
https://yourdomain.com/api/email/inbound
```

### 2. AI Processing
- System finds site by inbound email address
- Creates/finds chat session for customer
- Stores email in history for context
- Queues AI processing job
- AI generates Dutch response using shop policies
- Sends HTML email back to customer

### 3. Email Threading
- Uses `In-Reply-To` headers for conversation threading
- Links to existing chat sessions when possible
- Maintains email history for context

## Required ForwardEmail.net Settings

1. **Domain Setup**: Configure your business domain (e.g., `dutchfashion.nl`)
2. **Forwarding Rules**: 
   ```
   <EMAIL> → <EMAIL>
   <EMAIL> → <EMAIL>
   ```
3. **Webhook URL**: `https://yourdomain.com/api/email/inbound`
4. **Webhook Method**: `POST`
5. **Content-Type**: `application/json`

## Database Schema

The system automatically tracks:
- **Email History**: All inbound/outbound emails with threading
- **Chat Sessions**: Links emails to chat conversations
- **Site Configuration**: Shop policies for AI responses

## Utilities Available

- `getSiteInboundEmail(siteId)` - Get email address by site ID
- `getSiteInboundEmailByName(siteName)` - Get email address by site name
- `findSiteByInboundEmail(email)` - Find site by inbound email
- `getAllSiteEmailAddresses()` - List all site email addresses

## Testing

To test the email channel:
1. Send email to your business address (e.g., `<EMAIL>`)
2. Check ForwardEmail.net forwards to `<EMAIL>`
3. Verify webhook calls your API endpoint
4. Check database for email history and chat session
5. Confirm AI response is sent back to customer

## Production Considerations

- Ensure proper error handling for malformed webhook payloads
- Monitor email delivery rates and webhook reliability
- Set up proper logging for email processing
- Configure proper DNS records for email authentication
- Test email threading with reply chains