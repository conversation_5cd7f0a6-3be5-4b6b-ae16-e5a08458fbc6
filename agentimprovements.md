Product Purchase Flow Improvements

  Current Problems

  🚨 Critical UX Issues

  1. Broken Purchase Intent Flow
    - User expresses interest in buying → Agent asks vague "do you have a product in mind?"
    - Agent forgets the specific product it just showed
    - No clear path from "I want to buy" to actual purchase
  2. Missing Product Links
    - Agent shows product details but no way to actually buy it
    - No direct link to product page
    - Dead end in conversion funnel
  3. Context Loss
    - Agent doesn't remember which specific product user was interested in
    - Loses conversation thread between recommendation and purchase intent

  Required Solutions

  1. Add Product URL to Vector Search

  Investigation Needed:
  - Check if product URLs are stored in catalogProducts table
  - Verify if URLs are included in vector embeddings
  - Test if product recommendations return clickable links

  Files to Check:
  - /src/db/schema.ts - Product schema
  - /src/lib/embeddings/products.ts - Product search logic
  - Shopify sync jobs - URL ingestion

  2. Improve Purchase Intent Recognition

  Current State: Agent recognizes product interest but fails at purchase intent

  Needed Changes:
  - Detect purchase intent phrases: "ik wil bestellen", "ik wil het hebben", "waar kan ik het kopen"
  - Maintain context of last shown product
  - Respond with direct product link instead of generic questions
  - Actual vectorization of product information. Currently it does save products and it vectorizes images, but the product itself, including the product link are not saved the way they should
  - We should not auto import product on the onboarding, but move this to the src/app/(dashboard)/dashboard/[siteId]/settings/page.tsx integrations section for shopify with a sync button.

  1. Enhanced Product Response Format

  Instead of:
  "Hier is een product: Rotterdam QHF T-shirt..."

  Should be:
  "Hier is een product: Rotterdam QHF T-shirt - €39.95
  👉 Bekijk hier: [PRODUCT_URL]
  Wil je het bestellen? Dan kun je direct via de link!"

  4. Conversation Context Retention

  Problem: Agent forgets what it just recommended

  Solution:
  - Store last recommended products in conversation state
  - When user says "ik wil het bestellen" → reference specific product + provide link
  - Don't ask "which product?" when context is clear

  Technical Implementation

  Phase 1: Data Verification ✅

  - Confirm product URLs are in database
  - Test product search returns URLs
  - Verify Shopify sync includes product links

  Phase 2: Response Enhancement ✅

  - Modify product recommendation format to include links
  - Update worker job responses to include URLs
  - Test link formatting in chat interface

  Phase 3: Purchase Intent Flow ✅

  - Add purchase intent detection to agent instructions
  - Create "provide product link" response pattern
  - Test full flow: recommend → express interest → get link

  Phase 4: Context Memory ✅

  - Enhance conversation memory for product context
  - Prevent "which product?" responses when context exists
  - Maintain product details across conversation turns

  Expected Outcome

  Perfect Flow:

  1. User: "laat wat zien van producten"
  2. Agent: "Rotterdam QHF T-shirt - €39.95 👉 [LINK]"
  3. User: "ik wil het bestellen"
  4. Agent: "Perfect! Je kunt de Rotterdam QHF T-shirt bestellen via: [SAME_LINK]"

  No More:

  - ❌ "Heb je een specifiek product in gedachten?"
  - ❌ Forgetting what was just shown
  - ❌ Dead ends without purchase links
  - ❌ Generic responses to specific purchase intent

  Files Likely Needing Changes

  - /src/app/api/worker/route.ts - Product recommendation responses
  - /src/lib/graphs/platforms/shopify/tools.ts - Product formatting
  - /src/lib/embeddings/products.ts - URL inclusion in search results
  - /src/db/schema.ts - Verify URL fields exist
  - /src/lib/graphs/core/baseAgent.ts - Purchase intent detection

  Success Metrics

  - ✅ User can go from product interest to purchase link in 2 steps
  - ✅ No context loss between recommendation and purchase intent
  - ✅ Every product recommendation includes direct purchase link
  - ✅ Clear conversion path from discovery to purchase