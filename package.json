{"name": "nousu-ai", "version": "1.1.1", "private": true, "scripts": {"dev": "next dev --turbopack", "export": "next export", "build": "next build", "start": "next start", "lint": "next lint", "biome-write": "npx @biomejs/biome format --write .", "db:generate": "drizzle-kit generate:pg", "db:push": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio", "backfill": "tsx scripts/backfill-catalog.ts"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@auth/core": "^0.40.0", "@auth/drizzle-adapter": "^1.9.1", "@developer-hub/liquid-glass": "^1.2.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@langchain/community": "^0.3.50", "@langchain/core": "^0.3.59", "@langchain/langgraph": "^0.3.4", "@langchain/openai": "^0.5.13", "@once-ui-system/core": "^1.1.5", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tailwindcss/postcss": "^4.1.10", "@tanstack/react-table": "^8.21.3", "@upstash/qstash": "^2.8.1", "@upstash/redis": "^1.35.0", "ai": "^4.3.16", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.9", "gsap": "^3.13.0", "input-otp": "^1.4.2", "liquid-glass-react": "^1.1.1", "lucide-react": "^0.516.0", "motion": "^12.19.0", "next": "15.3.2", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "openai": "^5.3.0", "pg": "^8.11.3", "postcss": "^8.5.6", "react": "19.0.0", "react-day-picker": "^9.7.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.2.1", "react-resizable-panels": "^3.0.3", "react-virtuoso": "^4.13.0", "recharts": "^2.15.3", "resend": "^4.6.0", "sass": "^1.77.6", "sharp": "^0.33.4", "sonner": "^2.0.5", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/bcryptjs": "^2.4.6", "@types/node": "20.17.23", "@types/pg": "^8.11.2", "@types/react": "19.0.1", "@types/react-dom": "19.0.2", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.31.1", "eslint": "^9.30.0", "eslint-config-next": "15.3.2", "tsx": "^4.20.2", "tw-animate-css": "^1.3.4", "typescript": "5.8.2"}, "overrides": {"@types/react": "19.0.1", "@types/react-dom": "19.0.2"}}