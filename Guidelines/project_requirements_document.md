# Nousu – AI-Powered Customer Support Platform (PRD)

## 1. Project Overview

Nousu is a multi-tenant SaaS platform designed to automate customer support for e-commerce merchants—starting with Dutch Shopify stores. By embedding an AI agent (<PERSON><PERSON>) into chat widgets and email flows, Nousu handles routine questions (order status, product info), complex tasks (returns processing, refunds), and personalized recommendations. It unites chat, email, and background jobs into a single, scalable dashboard, reducing manual work and raising customer satisfaction.

We’re building Nousu to help online retailers save time and money on support, while delivering consistent, on-brand service around the clock. Key objectives include:\
• Automating at least 70% of common inquiries via AI.\
• Reducing average first-response time to under 30 seconds.\
• Ensuring strict data isolation for each merchant (tenant).\
Success will be measured by adoption rate among pilot stores, response-time improvements, and a drop in manual ticket handling.

## 2. In-Scope vs. Out-of-Scope

**In-Scope (V1)**

*   Multi-tenant structure: Organizations → Sites → AI Agents
*   Shopify integration (orders, products, customers)
*   Live chat widget embeddable on storefronts
*   Automated email replies (Resend service) with Dutch templates
*   AI-driven return workflow: eligibility check, customer prompts, staff review, Shopify refund creation
*   Basic admin dashboard: chat logs, return queue, KPI overview, knowledge base editor
*   User authentication via NextAuth.js (email/password, GitHub, Google)
*   Background job processing with Upstash QStash (product sync, webhook handling, email queues)

**Out-of-Scope (Later Phases)**

*   Built-in subscription billing or payment gateway
*   WooCommerce, Lightspeed, or other e-commerce platforms (beyond Shopify)
*   WhatsApp channel (planned but not initial)
*   Advanced analytics beyond dashboard KPIs
*   Mobile-native apps (iOS/Android)

## 3. User Flow

When a merchant first signs up, they register with email/GitHub/Google, then create an Organization (their company). They connect a Shopify Site by granting API access, name their AI agent, set its personality and policies, and finish onboarding. The merchant lands on a dashboard showing an overview of chats, returns awaiting review, and key metrics like response time and customer satisfaction.

From the customer side, a shopper on the merchant’s website clicks the chat widget or sends an email to support. **Nousu** greets them, answers questions by fetching data from Shopify or the knowledge base, and guides them through multi-step tasks (e.g., product returns). If a return needs human approval, Sophie places it in the admin “Returns” queue. A staff member logs in, reviews details, approves or denies the request, and the system automatically triggers Shopify’s refund API and notifies the customer via email.

## 4. Core Features

*   **AI Agent (“Nousu”)**\
    • Handles chat and email queries with LangGraph-powered workflows\
    • Retrieves order/product data from Shopify and internal knowledge base\
    • Conducts multi-turn conversations for tasks like returns and refunds\
    • Customizable tone, tool permissions, and escalation rules per shop policy
*   **Multi-Tenant Architecture**\
    • Hierarchy: Organizations → Sites → Agents\
    • Database-level isolation via PostgreSQL schemas and Drizzle ORM\
    • Separate configurations, policies, and data stores per site
*   **Customer Support Channels**\
    • Embeddable live chat widget for websites\
    • Automated, context-aware email responses (Resend)\
    • Future integration point for WhatsApp
*   **Automated Returns Workflow**\
    • Eligibility check against customizable shop policies\
    • Guided customer prompts (reason, condition, order info)\
    • Staff review queue in admin dashboard\
    • Shopify refund/return creation upon approval\
    • Dutch-language email notifications at each stage
*   **Admin Dashboard**\
    • KPI overview (response times, return rates, satisfaction)\
    • Conversation logs and return request lists\
    • Knowledge base article and topic management\
    • Team member roles and access control\
    • Agent and policy configuration screens
*   **Background Jobs & Webhooks**\
    • Upstash QStash-based queues for webhooks, product sync, email processing\
    • Retry logic, rate limiting via Upstash Redis\
    • Decouples heavy tasks from user requests
*   **Authentication & Security**\
    • NextAuth.js v5 for secure login (email, GitHub, Google)\
    • JWT/session handling, protected API routes\
    • Role-based access control

## 5. Tech Stack & Tools

*   **Frontend**\
    • Next.js (App Router, React 19) + TypeScript\
    • Tailwind CSS, Radix UI, Once UI (design system)\
    • Framer Motion (animations), React Hook Form, react-day-picker\
    • embla-carousel-react (carousels), @tanstack/react-table (data tables)\
    • Sonner (toast notifications), Recharts (dashboard charts)
*   **Backend**\
    • Node.js runtime\
    • PostgreSQL with Drizzle ORM (type-safe queries + migrations)\
    • NextAuth.js v5 for auth\
    • LangChain / LangGraph + OpenAI API (LLM integration)\
    • Upstash QStash & Redis for job queuing and rate limiting\
    • Resend for transactional emails\
    • Shopify GraphQL API
*   **Deployment & Infrastructure**\
    • Vercel for frontend and API routes\
    • Upstash (Redis & QStash)\
    • PostgreSQL hosting (Neon or Supabase)
*   **Developer Tools**\
    • Claude Code (AI-powered coding assistant in terminal)\
    • pnpm (package manager), Biome (linter/formatter)

## 6. Non-Functional Requirements

*   **Performance:**\
    • Chat response latency < 500ms (excluding AI processing)\
    • Page load time < 2s on average merchant dashboard
*   **Scalability:**\
    • Handle hundreds of simultaneous tenants and thousands of chat sessions\
    • Queue workers auto-scale based on job backlog
*   **Security & Compliance:**\
    • Data isolation per tenant (no cross-site access)\
    • GDPR-compliant data handling (EU focus)\
    • Secure storage of API credentials and environment variables
*   **Reliability:**\
    • 99.9% uptime SLA for core API routes\
    • Automatic retries for transient failures in background jobs
*   **Usability:**\
    • Intuitive onboarding wizard for non-technical users\
    • Clear error messages and inline form validation

## 7. Constraints & Assumptions

*   **Constraints:**\
    • Initial support limited to Shopify stores.\
    • OpenAI API access and quotas must be provisioned for each tenant.\
    • Upstash QStash availability in chosen region for low-latency queues.
*   **Assumptions:**\
    • Merchants will provide valid Shopify API credentials.\
    • Dutch-language templates suffice for pilot market.\
    • Tenant scale will start small (10–20 stores) before rapid growth.

## 8. Known Issues & Potential Pitfalls

*   **API Rate Limits:**\
    • Shopify and OpenAI enforce rate limits. Mitigation: batch requests, exponential back-off, queue jobs.
*   **Data Consistency:**\
    • Asynchronous webhooks may arrive out of order. Mitigation: idempotent handlers, timestamp checks.
*   **AI Hallucinations:**\
    • LLMs may generate incorrect info. Mitigation: strict tool-based data lookups, policy checks before customer response.
*   **Tenant Isolation Bugs:**\
    • Risk of cross-tenant data leaks. Mitigation: enforce schema-based separation in database queries and ORM.
*   **Email Deliverability:**\
    • Automated emails might land in spam. Mitigation: set up proper SPF/DKIM records, monitor bounce rates.

This PRD outlines Nousu’s goals, scope, user journeys, features, and technical boundaries in clear everyday English. It serves as the definitive guide for all subsequent technical documents and implementations.
