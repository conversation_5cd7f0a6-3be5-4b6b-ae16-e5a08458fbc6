# Backend Structure Document

This document outlines the backend architecture, database setup, APIs, hosting, infrastructure, security, monitoring, and maintenance strategies for Nousu. It uses everyday language to ensure clarity for technical and non-technical readers alike.

## 1. Backend Architecture

### Overall Design

*   We use a serverless approach with **Next.js API routes** running on **Vercel**. Each API route is a small function handling a specific request.

*   Our code is organized in layers:

    1.  **API layer**: Handles HTTP requests and responses.
    2.  **Service layer**: Contains business logic, like return workflows or chat orchestration.
    3.  **Data layer**: Interacts with the database via **Drizzle ORM**.

*   **LangGraph** workflows sit inside the service layer, coordinating calls to OpenAI, Shopify, and our own data stores.

### Key Frameworks and Patterns

*   **Node.js**: JavaScript runtime for all server-side code.
*   **Next.js**: Provides API routes, routing, and build tools.
*   **Drizzle ORM**: Type-safe database queries and migrations.
*   **Repository pattern**: We encapsulate data access in repository classes to keep business logic separate from raw queries.

### Scalability, Maintainability & Performance

*   **Scalability**: Serverless functions auto-scale with traffic. Background jobs run in a managed queue (**Upstash QStash**) that grows with demand.
*   **Maintainability**: Clear separation of concerns, TypeScript typing, and small, focused functions make the codebase easy to understand and extend.
*   **Performance**: We offload heavy tasks (embeddings, webhook processing, product syncs) to background queues. Common data is cached in **Upstash Redis** to reduce database load.

## 2. Database Management

### Technology and Type

*   **PostgreSQL**: Our primary relational database.
*   We use **separate schemas per tenant** (organization) to enforce strict data isolation.

### Data Organization

*   Data is organized into tables for users, organizations, sites, agents, conversations, messages, return requests, policies, products, and more.
*   Foreign keys link related records (e.g., a message belongs to a conversation and a site).
*   Indexes on frequently queried columns (site ID, conversation ID, created_at) improve lookup speed.

### Access and Management Practices

*   **Drizzle ORM** handles all reads and writes with migrations managed in code.
*   **Backups & Restore**: Our DB provider (Neon or Supabase) takes daily snapshots.
*   **Connection pooling**: Managed by the provider to avoid overload.
*   **Vacuuming & Analyze**: Scheduled by the provider to keep query performance optimal.

## 3. Database Schema (Human-Readable & SQL)

Below is a simplified view of key tables and then sample SQL definitions.

### Human-Readable Table Summary

*   **organizations**: ID, name, creation time.
*   **sites**: ID, organization ID, shopify credentials, domain.
*   **users**: ID, email, name, role, organization ID.
*   **agents**: ID, site ID, name, personality config.
*   **conversations**: ID, site ID, channel (chat/email), status.
*   **messages**: ID, conversation ID, sender (agent/customer), content, timestamp.
*   **return_requests**: ID, site ID, order ID, status (pending/approved/denied), created_at.
*   **policies**: ID, site ID, return window days, product condition rules, escalation settings.
*   **jobs**: ID, type (webhook, sync, email), payload, status, attempts.
*   **knowledge_base_items**: ID, site ID, title, content, embedding_id.

### Sample SQL Schema (PostgreSQL)

`-- Organizations CREATE TABLE organizations ( id UUID PRIMARY KEY, name TEXT NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() ); -- Sites CREATE TABLE sites ( id UUID PRIMARY KEY, organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE, shopify_token TEXT NOT NULL, domain TEXT, created_at TIMESTAMPTZ DEFAULT NOW() ); -- Users CREATE TABLE users ( id UUID PRIMARY KEY, email TEXT UNIQUE NOT NULL, name TEXT, role TEXT NOT NULL, organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE, created_at TIMESTAMPTZ DEFAULT NOW() ); -- Agents CREATE TABLE agents ( id UUID PRIMARY KEY, site_id UUID REFERENCES sites(id) ON DELETE CASCADE, name TEXT NOT NULL, personality JSONB, created_at TIMESTAMPTZ DEFAULT NOW() ); -- Conversations CREATE TABLE conversations ( id UUID PRIMARY KEY, site_id UUID REFERENCES sites(id) ON DELETE CASCADE, channel TEXT NOT NULL, status TEXT NOT NULL, created_at TIMESTAMPTZ DEFAULT NOW() ); -- Messages CREATE TABLE messages ( id UUID PRIMARY KEY, conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE, sender TEXT NOT NULL, content TEXT NOT NULL, timestamp TIMESTAMPTZ DEFAULT NOW() ); -- Return Requests CREATE TABLE return_requests ( id UUID PRIMARY KEY, site_id UUID REFERENCES sites(id) ON DELETE CASCADE, order_id TEXT NOT NULL, status TEXT NOT NULL, created_at TIMESTAMPTZ DEFAULT NOW() ); -- Policies CREATE TABLE policies ( id UUID PRIMARY KEY, site_id UUID REFERENCES sites(id) ON DELETE CASCADE, return_window_days INT NOT NULL, condition_rules JSONB, escalation_threshold INT, created_at TIMESTAMPTZ DEFAULT NOW() ); -- Jobs (Background Tasks) CREATE TABLE jobs ( id UUID PRIMARY KEY, type TEXT NOT NULL, payload JSONB, status TEXT NOT NULL, attempts INT DEFAULT 0, created_at TIMESTAMPTZ DEFAULT NOW() ); -- Knowledge Base Items CREATE TABLE knowledge_base_items ( id UUID PRIMARY KEY, site_id UUID REFERENCES sites(id) ON DELETE CASCADE, title TEXT NOT NULL, content TEXT NOT NULL, embedding_id UUID, created_at TIMESTAMPTZ DEFAULT NOW() );`

## 4. API Design and Endpoints

We follow a RESTful approach using Next.js API routes. Below are key endpoints:

### Authentication

*   **POST /api/auth/signup**: Create new user.
*   **POST /api/auth/login**: Email/password or provider login.
*   **GET /api/auth/session**: Retrieve current user session.

### Organization & Site Management

*   **GET /api/organizations**: List organizations for the user.
*   **POST /api/organizations**: Create a new organization.
*   **GET /api/organizations/{orgId}/sites**: List sites.
*   **POST /api/organizations/{orgId}/sites**: Connect a new Shopify site.

### Agent & Policy Configuration

*   **GET /api/sites/{siteId}/agent**: Fetch agent settings.
*   **PUT /api/sites/{siteId}/agent**: Update agent personality.
*   **GET /api/sites/{siteId}/policies**: Fetch shop policies.
*   **PUT /api/sites/{siteId}/policies**: Update policy rules.

### Chat & Messages

*   **POST /api/conversations**: Start a new conversation.
*   **GET /api/conversations/{convId}/messages**: List messages.
*   **POST /api/conversations/{convId}/messages**: Send message (customer or agent).

### Return Requests

*   **GET /api/sites/{siteId}/returns**: List returns needing review.
*   **POST /api/sites/{siteId}/returns**: AI-initiated return request.
*   **PATCH /api/returns/{returnId}**: Staff approves or denies.

### Webhooks & Jobs

*   **POST /api/webhooks/shopify**: Receive Shopify events (orders, products).
*   **POST /api/jobs/process**: Internal endpoint triggered by QStash to handle queued tasks.

## 5. Hosting Solutions

*   **Vercel**: Hosts the Next.js frontend and serverless API functions.\
    • Auto-scales with demand.\
    • Global edge network for low latency.\
    • Zero-config deployments on every git push.
*   **Neon or Supabase (PostgreSQL)**: Managed database with automatic backups and scaling.
*   **Upstash**: Serverless Redis and QStash for queues and rate limiting—no servers to maintain.

**Benefits**: High reliability, pay-as-you-go cost model, minimal operational overhead, and global distribution for fast access.

## 6. Infrastructure Components

*   **Load Balancer & CDN**: Built into Vercel’s edge network, distributing traffic across functions and caching static assets.
*   **Caching (Redis)**: Stores frequent lookups (e.g., policy settings, embeddings) and rate-limit counters.
*   **Queue System (QStash)**: Decouples time-consuming tasks (webhooks, product sync, email sending) from real-time requests.
*   **Database**: Single primary instance with read replicas (if needed) managed by the provider.
*   **Worker Functions**: Small serverless endpoints that process queued jobs on demand.

Together, these ensure quick responses to customers, reliable background processing, and efficient use of resources.

## 7. Security Measures

*   **Authentication & Authorization**:\
    • **NextAuth.js** enforces secure login flows and session validation.\
    • **Role-based access control** ensures users only reach allowed data.
*   **Tenant Isolation**:\
    • Each organization lives in its own PostgreSQL schema.\
    • ORM and middleware block cross-schema queries.
*   **Data Encryption**: HTTPS everywhere; DB encryption at rest by provider.
*   **Environment Secrets**: API keys and credentials stored in Vercel’s secret manager.
*   **Rate Limiting**: Redis-backed counters prevent abuse of critical endpoints (e.g., AI calls).
*   **Input Validation & Sanitization**: All incoming data is checked to avoid injections.
*   **Compliance**: GDPR-friendly data deletion, access logs, and consent records for EU users.

## 8. Monitoring and Maintenance

### Monitoring Tools

*   **Vercel Analytics & Logs**: View request volumes, response times, and errors per function.
*   **Upstash Dashboard**: Track queue sizes, job success/failure rates, and Redis usage.
*   **Database Metrics**: Provider console shows connection counts, CPU, and query performance.
*   **Optional**: Integrate **Sentry** for error tracing and **Prometheus/Grafana** for custom metrics.

### Maintenance Practices

*   **Automated Migrations**: Drizzle migrations run on each deploy.
*   **Backups**: Daily snapshots via DB provider; manual restore drills every quarter.
*   **Dependency Updates**: Weekly review and automated pull requests for security patches.
*   **Health Checks**: Scheduled QStash cron jobs ping critical endpoints.
*   **On-Call Rotation**: Alerts triggered by unusual error rates or queue backlogs.

## 9. Conclusion and Overall Backend Summary

Nousu’s backend is a modular, serverless system designed for a seamless AI-driven support experience. Key highlights:

*   **Multi-tenant, schema-based data isolation** protects each merchant’s data.
*   **Serverless functions** on Vercel ensure high availability and automatic scaling.
*   **PostgreSQL + Drizzle ORM** provides reliable, type-safe data storage.
*   **Upstash QStash & Redis** power robust background jobs and caching.
*   **NextAuth.js** secures user access with flexible authentication options.
*   **LangGraph + OpenAI** orchestrate AI workflows for chat and email.

This setup balances performance, reliability, and maintainability, allowing Nousu to grow seamlessly with customer demand while delivering fast, accurate AI-powered support.
