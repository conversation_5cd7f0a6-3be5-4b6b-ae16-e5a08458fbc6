# Tech Stack Document for Nousu

This document explains the technologies behind Nousu—our AI-powered, multi-tenant customer support platform—in simple, everyday language. It covers why each technology was chosen and how it helps deliver a smooth, reliable experience.

## 1. Frontend Technologies

These are the tools that shape what you see and interact with in your browser.

*   **Next.js (App Router) & React**

    *   Provides a fast, SEO-friendly framework for building modern web pages and components.
    *   Lets us split pages into smaller bundles so users only load what they need, improving speed.

*   **TypeScript**

    *   Adds simple type checks on top of JavaScript, catching errors early and making the code easier to understand.

*   **Tailwind CSS & Custom CSS**

    *   A utility-first styling toolkit that helps us build responsive layouts quickly without writing long, repetitive CSS.

*   **Radix UI & Once UI**

    *   Ready-made, accessible components (buttons, modals, dropdowns) that fit our custom design language.

*   **Framer Motion**

    *   Makes adding smooth animations and transitions easy, giving the interface a polished feel.

*   **React Hook Form**

    *   Manages complex forms with built-in validation, reducing boilerplate and improving user feedback when filling out fields.

*   **react-day-picker**

    *   A lightweight date-picker that fits seamlessly into our forms.

*   **embla-carousel-react**

    *   Provides a simple, mobile-friendly carousel for showcasing product images or tutorials.

*   **@tanstack/react-table**

    *   Powers interactive, sortable, filterable data tables in our admin dashboard.

*   **Sonner (Toast Notifications)**

    *   Non-intrusive pop-up messages to confirm actions or show errors in real time.

*   **Recharts**

    *   A charting library for visualizing key metrics (like response times or return rates) with clear graphs.

*How these choices enhance the experience*: Together, these tools let us build a highly responsive, accessible, and visually engaging interface. Users benefit from fast page loads, consistent styling, clear feedback, and polished animations—all without added complexity.

## 2. Backend Technologies

These components handle data storage, business logic, and AI-powered intelligence behind the scenes.

*   **Node.js**

    *   Runs our server code in JavaScript. It handles incoming requests and sends back responses quickly.

*   **PostgreSQL**

    *   A reliable, open-source database for storing users, organizations, chat logs, returns, and more.

*   **Drizzle ORM**

    *   Simplifies database queries with clear, type-safe code, reducing mistakes when reading or writing data.

*   **NextAuth.js v5**

    *   Manages user sign-up, login, sessions, and multi-provider authentication (GitHub, Google, email/password).

*   **LangChain & LangGraph**

    *   Orchestrate AI workflows: managing conversation state, running conditional logic, and calling tools in a structured way.

*   **OpenAI API**

    *   Powers the natural-language understanding and response generation that our AI agent, Sophie, uses in chat and email.

*   **Upstash QStash (Queueing) & Redis**

    *   Handles background jobs (product syncs, email processing, webhook tasks) so these tasks don’t slow down real-time chats.
    *   Provides rate limiting to keep external API usage in check.

*   **Resend (Email Service)**

    *   Sends templated, context-aware emails in Dutch or other languages, with reliable delivery and retry logic.

*   **Shopify GraphQL API**

    *   Fetches order, product, and customer data, and creates returns/refunds directly in merchants’ stores.

*How these components work together*: When a customer asks a question or requests a return, Next.js API routes forward the request to our Node.js server. The AI agent uses OpenAI via LangGraph to understand the question, Drizzle ORM to pull relevant data from PostgreSQL, and Upstash QStash to queue any heavy tasks. NextAuth.js ensures only the right users see or do certain things. Email notifications go out via Resend, and any Shopify updates come in through webhooks that we also process in the queue.

## 3. Infrastructure and Deployment

These choices make sure Nousu runs reliably, scales with traffic, and is easy for our team to update.

*   **Vercel**

    *   Hosts the Next.js frontend and serverless API routes with automatic deployments on each code push.

*   **Neon or Supabase (PostgreSQL provider)**

    *   Managed database hosting with automatic backups and scaling.

*   **Upstash**

    *   Serverless Redis and QStash for queues, keeping operational overhead low.

*   **pnpm**

    *   Fast, efficient package manager that speeds up install times and disk usage.

*   **Biome**

    *   Linter and formatter to keep code consistent and error-free.

*   **Git & GitHub**

    *   Version control, code reviews, and collaboration. All changes are tracked and peer-reviewed.

*   **CI/CD Pipeline**

    *   Vercel’s built-in pipeline runs tests and deploys to staging or production automatically.

*Benefits*: This setup ensures that new features and bug fixes reach production quickly, with minimal downtime. Our infrastructure can scale up or down based on customer load, and managed services offload routine maintenance.

## 4. Third-Party Integrations

External services help us deliver specialized functionality without reinventing the wheel.

*   **OpenAI**

    *   High-quality language models for chat and email responses.

*   **Shopify GraphQL API**

    *   Direct integration with merchants’ stores for real-time data and order management.

*   **Resend**

    *   Reliable email delivery with template management and retry logic.

*   **Upstash Redis & QStash**

    *   Serverless queues for background jobs, rate limiting, and cron tasks.

*   **NextAuth.js Providers (GitHub, Google)**

    *   Allow merchants and team members to sign in with familiar accounts.

*   **(Planned) Twilio/WhatsApp**

    *   Adds messaging app support for an additional customer channel in future releases.

*Enhancements*: These integrations let Sophie perform actions directly in a merchant’s store, handle heavy tasks off the main thread, and keep customers informed across channels—all without building and maintaining each service ourselves.

## 5. Security and Performance Considerations

We’ve built in several safeguards and optimizations to protect data and deliver a smooth experience.

*   **Authentication & Authorization**

    *   NextAuth.js enforces secure sign-in flows and session checks on every API route.
    *   Role-based access control limits who can view or change sensitive data in the admin dashboard.

*   **Tenant Isolation**

    *   Data partitioned per organization using separate PostgreSQL schemas. One merchant’s data can’t leak into another’s.

*   **Rate Limiting**

    *   Upstash Redis enforces API quotas to prevent abuse and keep response times stable.

*   **Background Job Queues**

    *   Offloads long-running tasks (AI embeddings, webhook processing) to ensure chat and page loads remain snappy.

*   **Data Encryption**

    *   All traffic is served over HTTPS. Sensitive environment variables (API keys, database credentials) are stored securely.

*   **Performance Optimizations**

    *   Next.js image and code splitting features minimize JS payloads.
    *   Tailwind’s utility classes cut down on unused CSS.
    *   Vercel’s global CDN serves assets from the edge, reducing latency.

## 6. Conclusion and Overall Tech Stack Summary

Nousu’s stack was chosen to balance speed of development, operational reliability, and a best-in-class user experience. Here’s a quick recap:

*   **Frontend:** Next.js, React, TypeScript, Tailwind CSS, Radix/Once UI, Framer Motion
*   **Backend:** Node.js, PostgreSQL, Drizzle ORM, NextAuth.js, LangChain/LangGraph, OpenAI
*   **Queues & Caching:** Upstash Redis & QStash
*   **Email:** Resend
*   **E-commerce API:** Shopify GraphQL
*   **Infrastructure:** Vercel, Neon/Supabase, pnpm, Biome, Git/GitHub

Each piece fits together so that merchants can onboard in minutes, customers get fast, accurate support, and administrators have full visibility and control. By relying on proven frameworks and managed services, Nousu can scale effortlessly while focusing our team on building new AI capabilities rather than reinventing core infrastructure.

Thank you for reviewing the tech stack—feel free to reach out with any questions or if you need more detail on any component!
