# Security Guidelines for Nousu – AI-Powered Customer Support Platform

This document defines mandatory security principles and controls to protect Nousu’s multi-tenant SaaS architecture, data, and integrations throughout design, development, testing, and operations.

## 1. Authentication & Access Control

*   **Strong Authentication**\
    • Use NextAuth.js v5 with secure OAuth flows for GitHub/Google and email/password.\
    • Enforce MFA (e.g., TOTP or SMS) for staff accounts and organization owners.\
    • Reject weak passwords: minimum 12 characters, mixed-case, digits, symbols.
*   **Session & Token Management**\
    • Store sessions in an encrypted, server‐side store (e.g., Upstash Redis) with HttpOnly, Secure, SameSite=Strict cookies.\
    • Use rotating session identifiers and enforce idle (15 min) and absolute (8 hr) timeouts.\
    • Prevent session fixation by issuing new session cookies on login and privilege changes.
*   **Role-Based Access Control (RBAC)**\
    • Define roles (Organization Owner, Admin, Staff, Agent) and least-privilege permissions.\
    • Enforce server-side authorization in every API route and background job.\
    • Validate JWT claims (iss, aud, exp) and session objects on each request.

## 2. Input Handling & Processing

*   **Validation & Sanitization**\
    • Treat all inputs (JSON, form data, query parameters) as untrusted.\
    • Use Zod or Yup schemas in Next.js API routes for strict type validation.\
    • Enforce whitelist validation for enums (e.g., permitted Shopify webhook topics, return statuses).
*   **Prevent Injection Attacks**\
    • Use Drizzle ORM’s parameterized queries—never inline user input in SQL.\
    • Sanitize file‐upload names and reject suspicious content.
*   **Cross-Site Scripting (XSS)**\
    • Encode all user-generated content in React safely (e.g., `dangerouslySetInnerHTML` only after HTML sanitization with DOMPurify).\
    • Implement a restrictive Content Security Policy (CSP) header.
*   **CSRF Protection**\
    • Enable NextAuth.js’s built-in CSRF tokens for state‐changing requests.\
    • Validate anti-CSRF tokens in custom forms and API mutations.

## 3. Data Protection & Privacy

*   **Encryption In Transit & At Rest**\
    • Enforce HTTPS/TLS 1.2+ on all endpoints (Next.js, Webhooks).\
    • Use AWS-managed or equivalent TLS certificates via Vercel.\
    • Enable Transparent Data Encryption on PostgreSQL (Neon/Supabase).
*   **Secrets Management**\
    • Store API keys (Shopify, OpenAI, Resend) and database credentials in Vercel Environment Variables or a vault (Vault/Azure Key Vault).\
    • Rotate secrets periodically and on personnel changes.
*   **Sensitive Data Handling**\
    • Hash user passwords with Argon2 or bcrypt + unique salt.\
    • Mask PII (emails, addresses) in logs and error messages.\
    • Purge soft‐deleted tenant data after a configurable retention period to comply with GDPR.
*   **Logging & Monitoring**\
    • Centralize logs in a secure store (e.g., DataDog, ELK) with restricted access.\
    • Redact sensitive fields in logs (auth tokens, PII).\
    • Monitor suspicious activities (failed logins, rate-limit breaches).

## 4. API & Service Security

*   **HTTPS & Rate Limiting**\
    • Enforce TLS on all Next.js API routes and webhook endpoints.\
    • Use Upstash Redis to implement per-tenant rate limits on AI calls, webhook processing, and user actions.
*   **CORS & Redirects**\
    • Configure CORS in Next.js to allow only authorized merchant domains for the chat widget.\
    • Validate redirect URLs against an allow‐list after authentication flows.
*   **API Versioning & Minimization**\
    • Version public APIs (e.g., `/api/v1/returns/*`).\
    • Return only necessary fields in API responses (avoid disclosing internal IDs, secrets).
*   **Authentication & Authorization Checks**\
    • Secure webhook and background‐job endpoints with HMAC signatures or QStash token validation.\
    • Validate that Shopify webhooks match the expected shop and signature.
*   **Retry & Idempotency**\
    • Design webhook handlers and QStash jobs as idempotent (use unique event IDs).\
    • Apply exponential back‐off for transient external API errors (Shopify, OpenAI).

## 5. Web Application Security Hygiene

*   **Security Headers**\
    • Strict-Transport-Security: max-age=63072000; includeSubDomains; preload\
    • Content-Security-Policy: default-src 'self'; script-src 'self' cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'\
    • X-Content-Type-Options: nosniff\
    • X-Frame-Options: DENY\
    • Referrer-Policy: strict-origin-when-cross-origin
*   **Cookie Hardening**\
    • Set `HttpOnly`, `Secure`, `SameSite=Strict` on all session and auth cookies.
*   **Subresource Integrity (SRI)**\
    • Apply SRI hashes on any third-party scripts or styles (e.g., Radix UI CDN).

## 6. Infrastructure & Configuration Management

*   **Server Hardening**\
    • Use Vercel’s managed platform—ensure debug and verbose logs are disabled in production.\
    • Minimal exposure: only port 443 open for public; internal services accessible over private networks.
*   **TLS Configuration**\
    • Enforce TLS 1.2+; disable SSLv3/TLS 1.0/1.1.\
    • Use strong cipher suites (ECDHE_RSA with AES-256-GCM).
*   **File Permissions & Storage**\
    • Store uploaded assets in a private object store (e.g., AWS S3) with pre-signed URLs and strict ACLs.\
    • Scan uploads with a malware detection service before processing.
*   **Backup & Recovery**\
    • Enable automated, encrypted backups of PostgreSQL.\
    • Test restore procedures quarterly.
*   **Patch Management**\
    • Keep Node.js, Next.js, Drizzle ORM, and all dependencies up-to-date.\
    • Subscribe to security advisories for upstream libraries.

## 7. Dependency Management

*   **Use Trusted Dependencies**\
    • Vet all NPM packages for maintenance frequency and vulnerability history.\
    • Avoid unmaintained or deprecated libraries.
*   **Lockfiles & Scanning**\
    • Check in `pnpm-lock.yaml`; enforce CI checks to prevent unauthorized changes.
*   **Automated SCA**\
    • Integrate Snyk or GitHub Dependabot to scan for known CVEs.\
    • Block merges if critical vulnerabilities are detected.
*   **Minimize Footprint**\
    • Only include necessary dependencies (no monolithic utility libraries).\
    • Remove or replace large, underused packages.

## 8. DevOps & CI/CD Security

*   **Secure Pipelines**\
    • Enforce code reviews and branch protection rules on GitHub.\
    • Require passing tests, linting (Biome), and vulnerability scans before merges.
*   **Secrets in CI**\
    • Store secrets in encrypted repository-level or organization-level vaults (GitHub Secrets).\
    • Do not log secrets in CI output.
*   **Deployment Policies**\
    • Use Vercel’s role-based access control for deployment permissions.\
    • Maintain separate environments (dev, staging, prod) with isolated credentials.

Adhering to these guidelines ensures Nousu remains secure, resilient, and compliant by design. All team members must follow these measures and report any deviations or potential risks for immediate remediation.
