# Frontend Guideline Document for Nousu

This guide explains how the front end of Nousu is built, why we chose each tool, and how everything fits together. It’s written in everyday language so anyone on the team can understand and follow it.

## 1. Frontend Architecture

### Overview

*   We use Next.js (App Router) and React to build pages and components. Next.js gives us server-side rendering, automatic code splitting, and fast page loads.
*   TypeScript adds type checking on top of JavaScript to catch errors early and make the code easier to read and maintain.
*   Tailwind CSS (plus a bit of custom CSS) handles styling with utility classes. This keeps our styles consistent and reduces the need for long, custom stylesheets.
*   Radix UI and our custom Once UI design system provide accessible, prebuilt components (buttons, dialogs, dropdowns) that match our brand.
*   Animations are done with Framer Motion for smooth transitions and micro-interactions.

### Scalability, Maintainability, Performance

*   **Scalability:** Next.js’s file-based routing and automatic code splitting mean we can add new pages and features without slowing down existing ones.
*   **Maintainability:** TypeScript, clear folder structure, and a shared design system (Once UI) ensure that components are consistent and easy to update.
*   **Performance:** Next.js preloads routes, purges unused CSS in Tailwind, and serves assets via Vercel’s global CDN. We also lazy-load heavy components to keep initial loads fast.

## 2. Design Principles

### Key Principles

1.  **Usability:** Interfaces are simple, with clear labels, tooltips, and inline validation to guide users.
2.  **Accessibility:** We follow WCAG guidelines. Radix UI components are keyboard-friendly and have proper ARIA attributes.
3.  **Responsiveness:** Layouts adapt to any screen size using Tailwind’s mobile-first utilities.
4.  **Consistency:** We stick to a shared color palette, typography, and spacing scale defined in our Tailwind config.
5.  **Feedback:** Actions (saving, errors, notifications) always provide immediate, clear feedback via toast messages (react-hot-toast) or inline alerts.

### Applying These Principles

*   Forms built with React Hook Form include clear error messages.
*   Buttons have consistent hover and focus states defined in Once UI.
*   Charts (Recharts) use accessible color contrasts and include alt text for screen readers.

## 3. Styling and Theming

### Styling Approach

*   **Tailwind CSS:** Utility-first classes for layout, spacing, typography, and colors. We override or extend via `tailwind.config.js`.
*   **Custom CSS:** Rarely needed, only for global overrides or third-party library tweaks, organized in a single `styles/` folder.
*   **Pre-processor:** We rely on PostCSS (built into Tailwind) for autoprefixing and future CSS features.

### Theming

*   We use Tailwind’s theming support for light and dark modes, driven by a Context Provider in React.
*   CSS variables (e.g., `--color-primary`) make it easy to adjust theme colors at runtime.

### Visual Style

*   **Design Style:** Modern flat with subtle glassmorphism accents (semi-transparent cards with blurred backgrounds).

### Color Palette

|                                 |
| ------------------------------- |
| Can be found in the globals.css |

### Typography

*   **Font Family:** Inter (system-font fallback: `-apple-system, BlinkMacSystemFont, sans-serif`).
*   **Sizing Scale:** Defined in Tailwind (text-sm, text-base, text-lg, etc.) following 4px increments.

## 4. Component Structure

### Organization and Reuse

*   **Folder Layout:**

    *   `components/atoms` – basic building blocks (Button, Input, Label).
    *   `components/molecules` – small combinations (FormField, ModalHeader).
    *   `components/organisms` – larger sections (Sidebar, NavBar, DataTable).
    *   `components/pages` – page-level wrappers and layout components.

*   **Once UI:** Shared design tokens and wrappers (e.g., `<UI.Button>`, `<UI.Card>`), ensuring consistency.

### Benefits of Component-Based Architecture

*   **Reusability:** Build once, use everywhere. If the look or behavior needs changing, update it in one place.
*   **Isolation:** Components manage their own styles and logic, reducing side effects.
*   **Testability:** Smaller pieces are easier to test in isolation.

## 5. State Management

### Approach

*   **Local State:** React’s `useState` and `useReducer` for component-level UI state (toggles, form inputs).
*   **Global State:** React Context for theme, authenticated user session, and global notifications.
*   **Data Fetching State:** We use Next.js’s server functions and SWR (or React Query, if added later) to fetch and cache data. This keeps UI in sync and avoids repeated requests.

### Sharing State

*   The `AuthProvider` (via NextAuth.js) wraps the app to provide `session` and `user` throughout.
*   The `ThemeProvider` manages light/dark mode preference and persists it in `localStorage`.
*   Components subscribe only to the context slices they need, minimizing re-renders.

## 6. Routing and Navigation

### Routing

*   **Next.js App Router:** File-based routing under `app/`. Each folder maps to a URL segment.
*   Dynamic routes (e.g., `[siteId]/returns`) allow us to load pages for different stores or return queues.
*   Layouts (`layout.tsx`) wrap pages with common UI (sidebar, header).

### Navigation

*   **Sidebar and Top Bar:** Provide links to Dashboard, Chats, Returns, Settings.
*   **Linking:** We use Next.js’s `<Link>` component for client-side transitions and prefetching.
*   **Active State:** CSS classes highlight the current page in the sidebar and breadcrumb.

## 7. Performance Optimization

### Strategies

*   **Code Splitting & Lazy Loading:** Dynamic `import()` for non-critical components (charts, rich text editors) so they load on demand.
*   **Image Optimization:** Next.js `<Image>` component automatically resizes and serves images in modern formats.
*   **Tree Shaking:** Tailwind’s PurgeCSS removes unused CSS.
*   **Prefetching:** Next.js pre-fetches links in view to speed up navigation.
*   **Memoization:** We use `React.memo`, `useMemo`, and `useCallback` on expensive components and functions.

### Impact

These measures reduce JavaScript and CSS payloads, keep initial load times under 2 seconds, and maintain snappy UI interactions even on slower networks.

## 8. Testing and Quality Assurance

### Testing Strategies

1.  **Unit Tests:** Jest + React Testing Library for components, hooks, and utility functions.
2.  **Integration Tests:** RTL with mocked API calls to verify component interactions.
3.  **End-to-End Tests:** Cypress (or Playwright) to simulate user flows like login, return processing, and navigation.

### Tools and Workflow

*   **Linting & Formatting:** Biome (or ESLint/Prettier) to enforce code style and catch common errors before commit.
*   **Continuous Integration:** On each pull request, tests run automatically. Failing tests block merging.
*   **Storybook (optional):** Document components visually and run visual regression tests.

## 9. Conclusion and Overall Frontend Summary

Nousu’s frontend is built with a clear focus on speed, scalability, and ease of use. By leveraging Next.js, React, TypeScript, and Tailwind CSS—with a shared design system (Once UI) and standard component structure—we ensure:

*   Fast page loads and smooth interactions
*   A consistent, accessible design across all screens
*   A codebase that’s easy to understand, maintain, and extend

Our state and routing setup keeps data flowing smoothly between pages, while performance optimizations and testing practices keep the experience reliable as we grow. With these guidelines in place, any new team member can jump in, build new features, and maintain high quality without confusion.

Feel free to reference this document whenever you’re building or updating the frontend. It covers our architecture, design principles, styling, components, state management, routing, performance tips, and testing rules—everything you need to know to keep Nousu’s UI fast, reliable, and user-friendly.
