# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Development server:**
```bash
npm run dev               # Start Next.js dev server with Turbopack
```

**Database operations:**
```bash
npm run db:push          # Push schema changes to database
npm run db:studio        # Open Drizzle Studio database interface
```

**Code quality:**
```bash
npm run lint             # Run Next.js linting
npm run biome-write      # Format code with <PERSON>iome (preferred over Prettier)
```

**Build and deploy:**
```bash
npm run build            # Production build
npm run start            # Start production server
```

## Architecture Overview

Nousu is a **multi-tenant SaaS AI customer support platform** specifically designed for Dutch e-commerce businesses. The platform processes customer inquiries through AI agents that can perform complex tasks like return processing, order lookups, and policy enforcement.

### Core Technology Stack

- **Framework**: Next.js 15.3.2 with App Router and React 19
- **Database**: PostgreSQL with Drizzle ORM for type-safe operations
- **AI Engine**: LangChain + LangGraph for sophisticated agent workflows
- **Background Jobs**: Upstash QStash for production, direct calls for development
- **Authentication**: NextAuth.js v5 with multi-provider support
- **E-commerce**: Shopify GraphQL API integration with OAuth
- **Email**: Resend service with custom Dutch templates
- **UI**: Radix UI components with Tailwind CSS and Once UI design system

### Multi-Tenant Structure

The platform follows a strict hierarchical structure:
```
Organizations → Sites → Agents
     ↓           ↓       ↓
   Users    Shop Config  AI Personality
```

Always filter database queries by `siteId` or `organizationId` to maintain tenant isolation.

## Key Architectural Patterns

### 1. LangGraph Agent Workflows

All complex AI interactions use **LangGraph state machines** (`src/lib/graphs/shopifyAgent.ts`):

- **State-driven**: Agent state persists across conversation turns
- **Tool-based**: Modular capabilities (order lookup, return processing, etc.)
- **Policy-controlled**: Shop-specific configurations control agent behavior
- **Multi-stage**: Complex workflows broken into discrete steps

When adding new AI capabilities, create tools in the existing LangGraph structure rather than direct OpenAI calls.

### 2. Background Job System

**Critical pattern**: All external API calls (Shopify, emails) must use the background job system:

```typescript
// ❌ DON'T: Synchronous external calls in API routes
const order = await shopify.orders.get(orderId);

// ✅ DO: Queue background jobs
await queueJob({
  type: 'order_status',
  payload: { orderId, sessionId },
  shopId: site.shopify_shop_id
});
```

Jobs are processed by `/api/worker/route.ts` and update chat sessions with results.

### 3. Email System

Email notifications are **queue-based** and **template-driven**:
- All emails stored in `email_notifications` table first
- Processed by cron job at `/api/cron/process-emails`
- Dutch-language templates with professional HTML styling
- Automatic staff notifications for escalations

### 4. Multi-Stage Return Processing

Returns follow a sophisticated workflow:
1. **Customer request** → AI validation against shop policies
2. **Background processing** → Detailed analysis and preparation
3. **Staff review** → Human oversight for complex cases
4. **Automated execution** → Return creation in Shopify + email notifications
5. **Learning feedback** → System improves from staff corrections

## Development Guidelines

### Database Operations

- Use Drizzle ORM for all database operations
- Always include proper foreign key relationships
- Filter by `siteId`/`organizationId` for multi-tenant isolation
- Use `timestamp` fields with proper timezone handling

### API Route Patterns

- **Chat endpoints**: Always include `sessionId` and maintain conversation context
- **Long-running operations**: Use the job queue system, never block API responses
- **Error handling**: Provide graceful fallbacks and structured error responses
- **Authentication**: Use NextAuth session validation for protected routes

### Agent Development

- **Shop policies**: Respect per-shop configuration in `shop_policy_configs`
- **Tool creation**: Follow the existing tool pattern with structured input/output
- **State management**: Use Redis for temporary data, database for persistence
- **Conversation context**: Always preserve chat history for context-aware responses

### Configuration Management

Shop-specific behavior is controlled through `shop_policy_configs`:
- AI personality and tone
- Auto-approval thresholds
- Enabled tools and capabilities
- Business rules and escalation settings

New features should check these configurations and adapt behavior accordingly.

### Testing Patterns

The codebase currently lacks formal tests. When adding tests:
- Focus on API routes and business logic
- Mock external services (Shopify, OpenAI, QStash)
- Test multi-tenant isolation
- Validate job queue workflows

### Environment Considerations

- **Development**: Jobs execute directly, simplified debugging
- **Production**: QStash handles job queuing with signature verification
- **Staging**: Use separate databases and API keys

### Dutch Market Focus

The platform is optimized for Dutch e-commerce:
- All customer communications in Dutch
- Informal tone ("je/jij" not "u")
- Dutch business hours and practices
- Euro currency formatting

### Code Quality Standards

- **TypeScript**: Strict mode enabled, avoid `any` types
- **Formatting**: Use Biome (not Prettier) - configured for 100 char lines, 2-space indentation
- **Imports**: Organize imports enabled in Biome
- **Components**: Follow Radix UI patterns and Once UI design system

## Critical Files

- `src/db/schema.ts` - Multi-tenant database schema
- `src/lib/graphs/shopifyAgent.ts` - Main AI agent workflow
- `src/lib/workers/job-queue.ts` - Background job orchestration
- `src/lib/email/index.ts` - Email system and templates
- `src/app/api/chat/route.ts` - Main chat interface
- `src/app/api/worker/route.ts` - Background job processor
- `src/lib/config/shop-policy.ts` - Shop-specific configurations

## Common Pitfalls to Avoid

- Making synchronous external API calls in request handlers
- Forgetting multi-tenant filtering in database queries
- Bypassing the job queue for email or Shopify operations
- Hardcoding business logic instead of using shop policies
- Breaking conversation context in chat flows
- Ignoring Dutch language and cultural conventions