# Shopify Webhooks in Multi-Tenant Platforms

## The Webhook Secret Challenge

In a multi-tenant Shopify platform, webhook verification works differently than single-store apps:

### Single-Store Apps
- You manually create webhooks in Shopify admin
- Shopify provides a webhook secret
- You use this secret to verify HMAC signatures

### Multi-Tenant Apps (Like Nousu)
- Webhooks are created programmatically via API
- Each shop has its own webhooks
- **No webhook secret is provided by the API**

## Security Options for Multi-Tenant Webhooks

### Option 1: Database Validation (Currently Implemented) ✅
```typescript
// Webhook handler verifies:
1. Required Shopify headers are present
2. Shop domain matches a known site in database
3. Site has valid Shopify credentials
```

**Pros:**
- No manual configuration needed
- Works immediately after OAuth
- Secure enough for most use cases

**Cons:**
- Theoretically vulnerable to header spoofing
- No cryptographic verification

### Option 2: Shopify App Webhook Notifications (Recommended for Production)

If you're building a public Shopify app:

1. **Register as a Shopify App**
   - Create app in Shopify Partners dashboard
   - Get app credentials

2. **Use App-Level Webhooks**
   ```typescript
   // In your app configuration
   const webhooks = {
     "products/create": "/api/webhooks/shopify",
     "products/update": "/api/webhooks/shopify",
     // ... other topics
   };
   ```

3. **Shopify Provides HMAC Secret**
   - Available in Partners dashboard
   - Same secret for all shops using your app
   - Used to verify all webhooks

### Option 3: Custom Webhook Secret per Shop

Store a unique secret per shop during OAuth:

```typescript
// During OAuth callback
const webhookSecret = crypto.randomBytes(32).toString('hex');

await db.update(sites).set({
  platformToken: access_token,
  platformShopId: shop,
  webhookSecret: webhookSecret, // Store this
}).where(eq(sites.id, siteId));

// When registering webhooks, include it in the URL
const callbackUrl = `${baseUrl}/api/webhooks/shopify?site_id=${siteId}&secret=${webhookSecret}`;
```

### Option 4: OAuth Token Verification

Use the shop's OAuth token as the secret:

```typescript
function verifyWebhookSignature(rawBody: string, signature: string, site: Site): boolean {
  const hash = crypto
    .createHmac("sha256", site.platformToken) // Use OAuth token as secret
    .update(rawBody, "utf8")
    .digest("base64");
  return hash === signature;
}
```

## Current Implementation

Nousu currently uses **Option 1** (Database Validation) which:
- ✅ Works immediately without configuration
- ✅ Sufficient for private/internal use
- ✅ Validates shop exists in database
- ⚠️ Should be upgraded for public production use

## Upgrading Security for Production

For a production multi-tenant platform, combine multiple approaches:

```typescript
// Enhanced webhook verification
async function verifyWebhook(req: NextRequest): Promise<boolean> {
  // 1. Validate headers
  if (!hasRequiredHeaders(req)) return false;
  
  // 2. Check shop exists in database
  const site = await validateShopInDatabase(shopDomain);
  if (!site) return false;
  
  // 3. Verify request IP is from Shopify (optional)
  if (!isShopifyIP(req.ip)) return false;
  
  // 4. Rate limiting per shop
  if (!checkRateLimit(shopDomain)) return false;
  
  // 5. Validate webhook hasn't been processed (idempotency)
  const webhookId = req.headers.get("x-shopify-webhook-id");
  if (await wasWebhookProcessed(webhookId)) return false;
  
  return true;
}
```

## No Manual Configuration Needed! 🎉

The beauty of this system is that shop owners don't need to:
- Copy webhook URLs
- Configure webhook secrets  
- Set up individual webhooks
- Understand technical details

They just click "Connect Shopify" and everything works automatically!