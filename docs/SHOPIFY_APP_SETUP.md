# Shopify Public App Setup for Nousu

Since you have a public Shopify app, here's how to properly configure webhooks with modern Shopify webhook delivery:

## 1. Configure Webhooks in Your App

Shopify webhooks are now configured in your app configuration file (`shopify.app.toml` or through the Partner Dashboard):

### Option A: Using shopify.app.toml (Recommended)

```toml
[webhooks]
api_version = "2024-01"

# Use relative paths - Shopify will prepend your app URL
[[webhooks.subscriptions]]
topics = [
  "products/create",
  "products/update", 
  "products/delete"
]
uri = "/api/webhooks/shopify"

[[webhooks.subscriptions]]
topics = [
  "orders/create",
  "orders/updated",
  "orders/fulfilled",
  "orders/cancelled"
]
uri = "/api/webhooks/shopify"

[[webhooks.subscriptions]]
topics = ["app/uninstalled"]
uri = "/api/webhooks/shopify"
```

### Option B: Configure in Partner Dashboard

1. Go to your app in Partners Dashboard
2. Navigate to **Configuration** → **Webhooks**
3. Add subscriptions with endpoint: `/api/webhooks/shopify`

## 2. Environment Variables

Add to your `.env`:

```bash
# Shopify App Credentials
SHOPIFY_CLIENT_ID=your_app_client_id
SHOPIFY_CLIENT_SECRET=your_app_client_secret  # This is used for webhook verification!

# App URL (must be HTTPS in production)
NEXT_PUBLIC_APP_URL=https://app.nousu.nl
```

**Important**: Modern Shopify apps use the `SHOPIFY_CLIENT_SECRET` (your app's API secret key) for webhook HMAC verification. There's no separate webhook secret anymore.

## 4. Development Setup with ngrok

For local development, Shopify requires HTTPS URLs:

1. Install ngrok: `brew install ngrok` (or download from ngrok.com)
2. Start your Next.js app: `npm run dev`
3. In another terminal: `ngrok http 3000`
4. Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`)
5. Update your `.env.local`:
   ```bash
   NEXT_PUBLIC_APP_URL=https://abc123.ngrok.io
   ```

## 5. How It Works Now

### During OAuth:
1. Shop installs your app
2. OAuth flow completes
3. Webhooks are automatically registered (production only)
4. Initial product sync is queued

### Webhook Processing:
1. Shopify sends webhook to your endpoint
2. HMAC signature is verified using your app's webhook secret
3. Multi-tenant routing finds the correct shop
4. Products are upserted and embeddings are queued

### In Development:
- Webhook registration is skipped (Shopify blocks localhost)
- Use ngrok for testing webhooks
- Or use the periodic sync endpoint: `/api/cron/sync-products`

## 6. Testing Webhooks

1. With ngrok running, update a product in Shopify admin
2. Check your console for webhook logs
3. Verify products appear in your database
4. Check embedding jobs are queued

## 7. Webhook Verification

Shopify sends these headers with each webhook:
- `X-Shopify-Hmac-Sha256`: HMAC signature (verify with CLIENT_SECRET)
- `X-Shopify-Topic`: The webhook topic
- `X-Shopify-Shop-Domain`: The shop's domain
- `X-Shopify-API-Version`: API version
- `X-Shopify-Webhook-Id`: Unique webhook ID (for idempotency)

## 8. Production Checklist

- [ ] SHOPIFY_CLIENT_SECRET is set in production env
- [ ] NEXT_PUBLIC_APP_URL is HTTPS with valid SSL certificate
- [ ] Webhooks configured in shopify.app.toml or Partner Dashboard
- [ ] Webhook endpoint responds with 200 OK quickly
- [ ] Implement webhook retry logic for failed processing
- [ ] Set up monitoring for webhook failures
- [ ] Configure rate limiting to handle webhook bursts
- [ ] Database has proper indexes on catalog tables

## 9. Best Practices for Production

### Quick Response Times
```typescript
// Respond quickly, process async
export async function POST(req: NextRequest) {
  const rawBody = await req.text();
  
  // Quick validation
  if (!verifyWebhookSignature(rawBody, signature)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  // Queue for processing instead of processing inline
  await queueJob({
    type: "process_webhook",
    payload: { topic, shop, data: JSON.parse(rawBody) }
  });
  
  // Respond immediately
  return NextResponse.json({ success: true });
}
```

### Idempotency
```typescript
// Track processed webhooks to avoid duplicates
const webhookId = req.headers.get("x-shopify-webhook-id");
if (await wasWebhookProcessed(webhookId)) {
  return NextResponse.json({ success: true });
}
```

### Handle Bursts
Shopify can send many webhooks quickly during bulk operations. Use queue systems (like QStash) to handle these gracefully.