<svg width="396" height="396" viewBox="0 0 396 396" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_2_38)">
<rect x="80" y="80" width="236" height="236" rx="56" fill="url(#paint0_linear_2_38)" fill-opacity="0.8"/>
</g>
<rect x="80" y="39" width="236" height="236" rx="56" fill="url(#paint1_linear_2_38)"/>
<rect x="63" y="22" width="270" height="270" rx="56" fill="url(#paint2_linear_2_38)" fill-opacity="0.2"/>
<g filter="url(#filter1_dddd_2_38)">
<path d="M166.004 101.004C166.004 98.8823 165.161 96.8476 163.66 95.3474C162.16 93.8472 160.125 93.0044 158.003 93.0044H142.001C139.879 93.0044 137.844 93.8472 136.343 95.3474C134.843 96.8476 134 98.8823 134 101.004V212.996C134 215.118 134.843 217.153 136.343 218.653C137.844 220.153 139.879 220.996 142.001 220.996H158.003C160.125 220.996 162.16 220.153 163.66 218.653C165.161 217.153 166.004 215.118 166.004 212.996M166.004 101.004V212.996M166.004 101.004C166.004 98.8823 166.847 96.8476 168.347 95.3474C169.848 93.8472 171.883 93.0044 174.005 93.0044H190.007C192.128 93.0044 194.164 93.8472 195.664 95.3474C197.164 96.8476 198.007 98.8823 198.007 101.004V212.996C198.007 215.118 197.164 217.153 195.664 218.653C194.164 220.153 192.128 220.996 190.007 220.996H174.005C171.883 220.996 169.848 220.153 168.347 218.653C166.847 217.153 166.004 215.118 166.004 212.996M134 125.002H166.004M166.004 188.998H198.007M206.008 133.002L238.012 125.002M222.01 188.998L253.398 181.159M204.432 97.4839L221.906 93.2442C226.403 92.1643 230.971 94.7641 232.164 99.0998L261.727 206.437C262.249 208.402 262.018 210.491 261.079 212.294C260.141 214.098 258.563 215.487 256.654 216.188L255.59 216.516L238.116 220.756C233.62 221.836 229.051 219.236 227.859 214.9L198.296 107.563C197.774 105.598 198.005 103.509 198.943 101.706C199.882 99.9018 201.46 98.5133 203.368 97.8119L204.432 97.4839Z" stroke="white" stroke-width="9" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_f_2_38" x="0" y="0" width="396" height="396" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_2_38"/>
</filter>
<filter id="filter1_dddd_2_38" x="116.5" y="85.5017" width="163" height="185.997" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_38"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_2_38" result="effect2_dropShadow_2_38"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="18"/>
<feGaussianBlur stdDeviation="5.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_2_38" result="effect3_dropShadow_2_38"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="33"/>
<feGaussianBlur stdDeviation="6.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_2_38" result="effect4_dropShadow_2_38"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_2_38" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2_38" x1="91.5" y1="111" x2="288" y2="308" gradientUnits="userSpaceOnUse">
<stop stop-color="#809EFF"/>
<stop offset="1" stop-color="#10009D"/>
</linearGradient>
<linearGradient id="paint1_linear_2_38" x1="91.5" y1="70" x2="288" y2="267" gradientUnits="userSpaceOnUse">
<stop stop-color="#809EFF"/>
<stop offset="1" stop-color="#10009D"/>
</linearGradient>
<linearGradient id="paint2_linear_2_38" x1="76.1568" y1="57.4661" x2="300.966" y2="282.847" gradientUnits="userSpaceOnUse">
<stop stop-color="#809EFF"/>
<stop offset="1" stop-color="#10009D"/>
</linearGradient>
</defs>
</svg>
