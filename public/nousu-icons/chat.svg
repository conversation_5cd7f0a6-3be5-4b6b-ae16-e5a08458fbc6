<svg width="396" height="396" viewBox="0 0 396 396" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_1_26)">
<rect x="80" y="80" width="236" height="236" rx="56" fill="url(#paint0_linear_1_26)" fill-opacity="0.8"/>
</g>
<rect x="80" y="39" width="236" height="236" rx="56" fill="url(#paint1_linear_1_26)"/>
<rect x="63" y="22" width="270" height="270" rx="56" fill="url(#paint2_linear_1_26)" fill-opacity="0.2"/>
<g filter="url(#filter1_dddd_1_26)">
<path d="M169.556 142.647H226.444M169.556 172.765H212.222M240.667 105C246.325 105 251.751 107.38 255.752 111.616C259.752 115.852 262 121.597 262 127.588V187.824C262 193.814 259.752 199.56 255.752 203.796C251.751 208.032 246.325 210.412 240.667 210.412H205.111L169.556 233V210.412H155.333C149.675 210.412 144.249 208.032 140.248 203.796C136.248 199.56 134 193.814 134 187.824V127.588C134 121.597 136.248 115.852 140.248 111.616C144.249 107.38 149.675 105 155.333 105H240.667Z" stroke="white" stroke-width="9" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_f_1_26" x="0" y="0" width="396" height="396" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_1_26"/>
</filter>
<filter id="filter1_dddd_1_26" x="116.5" y="97.5" width="163" height="186" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_26"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1_26" result="effect2_dropShadow_1_26"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="18"/>
<feGaussianBlur stdDeviation="5.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_1_26" result="effect3_dropShadow_1_26"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="33"/>
<feGaussianBlur stdDeviation="6.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_1_26" result="effect4_dropShadow_1_26"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_1_26" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1_26" x1="91.5" y1="111" x2="288" y2="308" gradientUnits="userSpaceOnUse">
<stop stop-color="#C1FF80"/>
<stop offset="1" stop-color="#49F87E"/>
</linearGradient>
<linearGradient id="paint1_linear_1_26" x1="91.5" y1="70" x2="288" y2="267" gradientUnits="userSpaceOnUse">
<stop stop-color="#C1FF80"/>
<stop offset="1" stop-color="#10692B"/>
</linearGradient>
<linearGradient id="paint2_linear_1_26" x1="76.1568" y1="57.4661" x2="300.966" y2="282.847" gradientUnits="userSpaceOnUse">
<stop stop-color="#C1FF80"/>
<stop offset="1" stop-color="#49F87E"/>
</linearGradient>
</defs>
</svg>
