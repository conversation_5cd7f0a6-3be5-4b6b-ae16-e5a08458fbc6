<svg width="396" height="396" viewBox="0 0 396 396" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_12_2)">
<rect x="80" y="80" width="236" height="236" rx="56" fill="url(#paint0_linear_12_2)" fill-opacity="0.8"/>
</g>
<rect x="80" y="39" width="236" height="236" rx="56" fill="url(#paint1_linear_12_2)"/>
<rect x="63" y="22" width="270" height="270" rx="56" fill="url(#paint2_linear_12_2)" fill-opacity="0.2"/>
<g filter="url(#filter1_dddd_12_2)">
<path d="M198 206.778C188.271 201.161 177.234 198.203 166 198.203C154.766 198.203 143.729 201.161 134 206.778V114.333C138.723 111.606 143.775 109.493 149.033 108.047M198 206.778C210.68 199.458 225.486 196.707 239.948 198.984M198 206.778V157M176.055 106.554C183.664 107.763 191.102 110.351 198 114.333M198 114.333C207.729 108.716 218.766 105.759 230 105.759C241.234 105.759 252.271 108.716 262 114.333V192.556M198 114.333V128.556M134 93L262 221" stroke="black" stroke-width="9" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_f_12_2" x="0" y="0" width="396" height="396" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_12_2"/>
</filter>
<filter id="filter1_dddd_12_2" x="116.5" y="85.5" width="163" height="186" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_12_2" result="effect2_dropShadow_12_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="18"/>
<feGaussianBlur stdDeviation="5.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_12_2" result="effect3_dropShadow_12_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="33"/>
<feGaussianBlur stdDeviation="6.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_12_2" result="effect4_dropShadow_12_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_12_2" result="shape"/>
</filter>
<linearGradient id="paint0_linear_12_2" x1="91.5" y1="111" x2="288" y2="308" gradientUnits="userSpaceOnUse">
<stop stop-color="#6EA1FF"/>
<stop offset="1" stop-color="#FF6467"/>
</linearGradient>
<linearGradient id="paint1_linear_12_2" x1="91.5" y1="70" x2="288" y2="267" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_12_2" x1="76.1568" y1="57.4661" x2="300.966" y2="282.847" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBE4C4"/>
<stop offset="1" stop-color="#FF6467"/>
</linearGradient>
</defs>
</svg>
