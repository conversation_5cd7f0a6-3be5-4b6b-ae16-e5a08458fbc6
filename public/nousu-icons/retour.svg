<svg width="396" height="396" viewBox="0 0 396 396" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_1_25)">
<rect x="80" y="80" width="236" height="236" rx="56" fill="url(#paint0_linear_1_25)" fill-opacity="0.8"/>
</g>
<rect x="80" y="39" width="236" height="236" rx="56" fill="url(#paint1_linear_1_25)"/>
<rect x="63" y="22" width="270" height="270" rx="56" fill="url(#paint2_linear_1_25)" fill-opacity="0.2"/>
<path d="M258.75 143.5V130C258.748 127.633 258.123 125.307 256.938 123.258C255.753 121.208 254.05 119.506 252 118.323L204.75 91.3225C202.698 90.1376 200.37 89.5139 198 89.5139C195.63 89.5139 193.302 90.1376 191.25 91.3225L144 118.323C141.95 119.506 140.247 121.208 139.062 123.258C137.877 125.307 137.252 127.633 137.25 130V184C137.252 186.367 137.877 188.693 139.062 190.742C140.247 192.792 141.95 194.494 144 195.678L191.25 222.678C193.302 223.862 195.63 224.486 198 224.486C200.37 224.486 202.698 223.862 204.75 222.678L218.25 214.983M167.625 104.823L228.375 139.585M139.207 123.25L198 157M198 157L256.792 123.25M198 157V224.5M231.75 163.75L265.5 197.5M231.75 197.5L265.5 163.75" stroke="white" stroke-width="9" stroke-linecap="round" stroke-linejoin="round"/>
<defs>
<filter id="filter0_f_1_25" x="0" y="0" width="396" height="396" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_1_25"/>
</filter>
<linearGradient id="paint0_linear_1_25" x1="91.5" y1="111" x2="288" y2="308" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF76AF"/>
<stop offset="1" stop-color="#F8494C"/>
</linearGradient>
<linearGradient id="paint1_linear_1_25" x1="91.5" y1="70" x2="288" y2="267" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF76AF"/>
<stop offset="1" stop-color="#F8494C"/>
</linearGradient>
<linearGradient id="paint2_linear_1_25" x1="76.1568" y1="57.4661" x2="300.966" y2="282.847" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF76AF"/>
<stop offset="1" stop-color="#F8494C"/>
</linearGradient>
</defs>
</svg>
