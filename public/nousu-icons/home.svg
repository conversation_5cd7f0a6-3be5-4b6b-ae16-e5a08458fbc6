<svg width="396" height="396" viewBox="0 0 396 396" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_2_54)">
<rect x="80" y="80" width="236" height="236" rx="56" fill="url(#paint0_linear_2_54)" fill-opacity="0.8"/>
</g>
<rect x="80" y="39" width="236" height="236" rx="56" fill="url(#paint1_linear_2_54)"/>
<rect x="63" y="22" width="270" height="270" rx="56" fill="url(#paint2_linear_2_54)" fill-opacity="0.2"/>
<g filter="url(#filter1_dddd_2_54)">
<path d="M176.667 221V178.333C176.667 174.561 178.165 170.944 180.832 168.277C183.499 165.61 187.117 164.111 190.889 164.111H205.111C208.883 164.111 212.501 165.61 215.168 168.277C217.835 170.944 219.333 174.561 219.333 178.333V221M148.222 157H134L198 93L262 157H247.778V206.778C247.778 210.55 246.279 214.167 243.612 216.834C240.945 219.502 237.328 221 233.556 221H162.444C158.672 221 155.055 219.502 152.388 216.834C149.721 214.167 148.222 210.55 148.222 206.778V157Z" stroke="white" stroke-width="9" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_f_2_54" x="0" y="0" width="396" height="396" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_2_54"/>
</filter>
<filter id="filter1_dddd_2_54" x="116.5" y="85.5" width="163" height="186" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_54"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_2_54" result="effect2_dropShadow_2_54"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="18"/>
<feGaussianBlur stdDeviation="5.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_2_54" result="effect3_dropShadow_2_54"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="33"/>
<feGaussianBlur stdDeviation="6.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_2_54" result="effect4_dropShadow_2_54"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_2_54" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2_54" x1="91.5" y1="111" x2="288" y2="308" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBE4C4"/>
<stop offset="1" stop-color="#FF6467"/>
</linearGradient>
<linearGradient id="paint1_linear_2_54" x1="91.5" y1="70" x2="288" y2="267" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBE4C4"/>
<stop offset="1" stop-color="#FF6467"/>
</linearGradient>
<linearGradient id="paint2_linear_2_54" x1="76.1568" y1="57.4661" x2="300.966" y2="282.847" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBE4C4"/>
<stop offset="1" stop-color="#FF6467"/>
</linearGradient>
</defs>
</svg>
