import { db } from "@/db";
import { sites, catalogProducts } from "@/db/schema";
import { eq } from "drizzle-orm";
import { ShopifyClient } from "@/lib/clients";
import { queueJob } from "@/lib/workers/job-queue";
import crypto from "crypto";

async function backfillSite(siteId: string) {
  const site = await db.query.sites.findFirst({
    where: eq(sites.id, siteId),
  });

  if (!site || site.platform !== "shopify") {
    throw new Error("Invalid site or not a Shopify site");
  }

  const client = new ShopifyClient(site.platformToken!, site.platformShopId!);

  let hasNextPage = true;
  let cursor: string | null = null;
  let totalProducts = 0;

  console.log(`Starting backfill for site ${site.name} (${siteId})`);

  while (hasNextPage) {
    const query = `
      query ($cursor: String) {
        products(first: 50, after: $cursor) {
          edges {
            node {
              id
              title
              descriptionHtml
              vendor
              tags
              priceRange {
                minVariantPrice {
                  amount
                }
              }
              images(first: 10) {
                edges {
                  node {
                    url
                  }
                }
              }
            }
            cursor
          }
          pageInfo {
            hasNextPage
          }
        }
      }
    `;

    const response: any = await client.query<any>(query, { cursor });
    const products: any = response.data.products;

    for (const edge of products.edges) {
      const product = edge.node;
      const shopifyId = product.id.split("/").pop();

      // Upsert product
      const catalogProduct = {
        siteId,
        shopifyProductId: shopifyId,
        title: product.title,
        description: product.descriptionHtml,
        price: product.priceRange?.minVariantPrice?.amount || "0",
        vendor: product.vendor || "",
        tags: product.tags.join(", "),
        updatedAt: new Date(),
      };

      await db
        .insert(catalogProducts)
        .values(catalogProduct)
        .onConflictDoUpdate({
          target: [catalogProducts.siteId, catalogProducts.shopifyProductId],
          set: catalogProduct,
        });

      const savedProduct = await db.query.catalogProducts.findFirst({
        where: eq(catalogProducts.shopifyProductId, shopifyId),
      });

      if (!savedProduct) continue;

      // Queue text embedding job
      await queueJob({
        type: "product_text_embed",
        payload: {
          productId: savedProduct.id,
          siteId,
          content: `${product.title} ${product.descriptionHtml} ${product.tags.join(" ")}`,
          checksum: crypto
            .createHash("md5")
            .update(`${product.title}${product.descriptionHtml}${product.tags.join("")}`)
            .digest("hex"),
          jobType: "product_text_embed",
        },
      });

      // Queue image embeddings
      const images = product.images.edges;
      for (let i = 0; i < images.length; i++) {
        await queueJob({
          type: "product_image_embed",
          payload: {
            productId: savedProduct.id,
            siteId,
            imageUrl: images[i].node.url,
            position: i,
            checksum: crypto.createHash("md5").update(images[i].node.url).digest("hex"),
            jobType: "product_image_embed",
          },
          delay: totalProducts * 2 + i * 2, // Stagger processing
        });
      }

      totalProducts++;
      if (totalProducts % 10 === 0) {
        console.log(`Processed ${totalProducts} products so far...`);
      }
    }

    cursor = products.edges[products.edges.length - 1]?.cursor;
    hasNextPage = products.pageInfo.hasNextPage;
  }

  console.log(`✅ Backfill complete: ${totalProducts} products queued for embedding`);
  console.log(`Site: ${site.name} (${siteId})`);
  console.log(
    `Products will be processed over the next ${Math.ceil((totalProducts * 2) / 60)} minutes`,
  );
}

// Usage: npm run backfill -- --site-id=xxx
async function main() {
  const siteId = process.argv.find((arg) => arg.startsWith("--site-id="))?.split("=")[1];

  if (!siteId) {
    console.error("Usage: npm run backfill -- --site-id=<uuid>");
    console.error("");
    console.error("Example: npm run backfill -- --site-id=************************************");
    process.exit(1);
  }

  try {
    await backfillSite(siteId);
    process.exit(0);
  } catch (error) {
    console.error("❌ Backfill failed:", error);
    process.exit(1);
  }
}

main().catch(console.error);
