CREATE TABLE "once"."catalog_images" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"product_id" uuid NOT NULL,
	"src" varchar(600) NOT NULL,
	"position" integer DEFAULT 0,
	"embedding" vector(1024),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "once"."catalog_products" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"site_id" uuid NOT NULL,
	"shopify_id" varchar(32) NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"price" numeric(10, 2),
	"vendor" varchar(120),
	"tags" varchar(255),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "once"."catalog_text_embeds" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"product_id" uuid NOT NULL,
	"embedding" vector(1536),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "once"."catalog_images" ADD CONSTRAINT "catalog_images_product_id_catalog_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "once"."catalog_products"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "once"."catalog_products" ADD CONSTRAINT "catalog_products_site_id_sites_id_fk" FOREIGN KEY ("site_id") REFERENCES "once"."sites"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "once"."catalog_text_embeds" ADD CONSTRAINT "catalog_text_embeds_product_id_catalog_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "once"."catalog_products"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
CREATE INDEX "catalog_img_prod_idx" ON "once"."catalog_images" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "catalog_img_vec_idx" ON "once"."catalog_images" USING ivfflat ("embedding" vector_l2_ops) WITH (lists=100);--> statement-breakpoint
CREATE UNIQUE INDEX "catalog_site_shopify_uq" ON "once"."catalog_products" USING btree ("site_id","shopify_id");--> statement-breakpoint
CREATE INDEX "catalog_site_idx" ON "once"."catalog_products" USING btree ("site_id");--> statement-breakpoint
CREATE INDEX "catalog_txt_prod_idx" ON "once"."catalog_text_embeds" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "catalog_txt_vec_idx" ON "once"."catalog_text_embeds" USING ivfflat ("embedding" vector_l2_ops) WITH (lists=100);