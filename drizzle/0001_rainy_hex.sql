CREATE TABLE "once"."agent_configurations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"site_id" uuid,
	"agent_id" uuid,
	"config_type" varchar(30) NOT NULL,
	"enabled" boolean DEFAULT true,
	"settings" jsonb NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "once"."agent_configurations" ADD CONSTRAINT "agent_configurations_site_id_sites_id_fk" FOREIGN KEY ("site_id") REFERENCES "once"."sites"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "once"."agent_configurations" ADD CONSTRAINT "agent_configurations_agent_id_agents_id_fk" FOREIGN KEY ("agent_id") REFERENCES "once"."agents"("id") ON DELETE cascade ON UPDATE cascade;