CREATE TABLE "once"."email_notifications" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"return_request_id" uuid,
	"session_id" uuid,
	"to" varchar(256) NOT NULL,
	"from" varchar(256),
	"subject" text NOT NULL,
	"content" text NOT NULL,
	"template_type" varchar(50),
	"status" varchar(20) DEFAULT 'pending',
	"sent_at" timestamp with time zone,
	"scheduled_for" timestamp with time zone,
	"ai_generated" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "once"."return_requests" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"site_id" uuid,
	"session_id" uuid,
	"order_number" varchar(50) NOT NULL,
	"order_shopify_id" varchar(100),
	"customer_email" varchar(256),
	"customer_phone" varchar(50),
	"reason" text,
	"status" varchar(30) DEFAULT 'pending',
	"requested_items" jsonb,
	"chat_context" text,
	"staff_notes" text,
	"shopify_return_id" varchar(100),
	"reviewed_by" text,
	"reviewed_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "once"."jobs" ADD COLUMN "parent_job_id" uuid;--> statement-breakpoint
ALTER TABLE "once"."jobs" ADD COLUMN "stage" varchar(40);--> statement-breakpoint
ALTER TABLE "once"."email_notifications" ADD CONSTRAINT "email_notifications_return_request_id_return_requests_id_fk" FOREIGN KEY ("return_request_id") REFERENCES "once"."return_requests"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "once"."email_notifications" ADD CONSTRAINT "email_notifications_session_id_chat_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "once"."chat_sessions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "once"."return_requests" ADD CONSTRAINT "return_requests_site_id_sites_id_fk" FOREIGN KEY ("site_id") REFERENCES "once"."sites"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "once"."return_requests" ADD CONSTRAINT "return_requests_session_id_chat_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "once"."chat_sessions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "once"."return_requests" ADD CONSTRAINT "return_requests_reviewed_by_users_id_fk" FOREIGN KEY ("reviewed_by") REFERENCES "once"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "once"."jobs" ADD CONSTRAINT "jobs_parent_job_id_jobs_id_fk" FOREIGN KEY ("parent_job_id") REFERENCES "once"."jobs"("id") ON DELETE set null ON UPDATE no action;