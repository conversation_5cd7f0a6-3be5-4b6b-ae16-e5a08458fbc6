CREATE TABLE "once"."shop_policy_configs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"site_id" uuid NOT NULL,
	"personality_prompt" text NOT NULL,
	"enabled_tools" jsonb NOT NULL,
	"tool_policies" jsonb NOT NULL,
	"max_turns_before_escalation" integer DEFAULT 5,
	"escalation_enabled" boolean DEFAULT true,
	"escalation_email" varchar(256),
	"primary_model" varchar(120) DEFAULT 'gpt-4o',
	"fallback_model" varchar(120) DEFAULT 'gpt-4o-mini',
	"temperature" integer DEFAULT 20,
	"reflection_enabled" boolean DEFAULT true,
	"reflection_threshold" integer DEFAULT 40,
	"business_hours" jsonb,
	"active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "once"."shop_policy_configs" ADD CONSTRAINT "shop_policy_configs_site_id_sites_id_fk" FOREIGN KEY ("site_id") REFERENCES "once"."sites"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
CREATE UNIQUE INDEX "shop_policy_configs_site_active" ON "once"."shop_policy_configs" USING btree ("site_id") WHERE "once"."shop_policy_configs"."active" = true;