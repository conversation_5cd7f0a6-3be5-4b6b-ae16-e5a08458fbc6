import { relations } from "drizzle-orm/relations";
import {
  agentsInOnce,
  jobsInOnce,
  sitesInOnce,
  agentPromptsInOnce,
  organizationsInOnce,
  chatSessionsInOnce,
  chatMessagesInOnce,
  feedbackInOnce,
  fineTuneRunsInOnce,
  knowledgeSourcesInOnce,
  promptVersionsInOnce,
  usersInOnce,
  sessionsInOnce,
  knowledgeChunksInOnce,
  emailHistoryInOnce,
  returnRequestsInOnce,
  emailNotificationsInOnce,
  shopPolicyConfigsInOnce,
  agentConfigurationsInOnce,
  orgMembersInOnce,
  accountsInOnce,
} from "./schema";

export const jobsInOnceRelations = relations(jobsInOnce, ({ one, many }) => ({
  agentsInOnce: one(agentsInOnce, {
    fields: [jobsInOnce.agentId],
    references: [agentsInOnce.id],
  }),
  jobsInOnce: one(jobsInOnce, {
    fields: [jobsInOnce.parentJobId],
    references: [jobsInOnce.id],
    relationName: "jobsInOnce_parentJobId_jobsInOnce_id",
  }),
  jobsInOnces: many(jobsInOnce, {
    relationName: "jobsInOnce_parentJobId_jobsInOnce_id",
  }),
}));

export const agentsInOnceRelations = relations(agentsInOnce, ({ one, many }) => ({
  jobsInOnces: many(jobsInOnce),
  sitesInOnce: one(sitesInOnce, {
    fields: [agentsInOnce.siteId],
    references: [sitesInOnce.id],
  }),
  agentPromptsInOnces: many(agentPromptsInOnce),
  fineTuneRunsInOnces: many(fineTuneRunsInOnce),
  chatSessionsInOnces: many(chatSessionsInOnce),
  agentConfigurationsInOnces: many(agentConfigurationsInOnce),
}));

export const sitesInOnceRelations = relations(sitesInOnce, ({ one, many }) => ({
  agentsInOnces: many(agentsInOnce),
  organizationsInOnce: one(organizationsInOnce, {
    fields: [sitesInOnce.orgId],
    references: [organizationsInOnce.id],
  }),
  knowledgeSourcesInOnces: many(knowledgeSourcesInOnce),
  emailHistoryInOnces: many(emailHistoryInOnce),
  shopPolicyConfigsInOnces: many(shopPolicyConfigsInOnce),
  returnRequestsInOnces: many(returnRequestsInOnce),
  agentConfigurationsInOnces: many(agentConfigurationsInOnce),
}));

export const agentPromptsInOnceRelations = relations(agentPromptsInOnce, ({ one, many }) => ({
  agentsInOnce: one(agentsInOnce, {
    fields: [agentPromptsInOnce.agentId],
    references: [agentsInOnce.id],
  }),
  promptVersionsInOnces: many(promptVersionsInOnce),
}));

export const organizationsInOnceRelations = relations(organizationsInOnce, ({ many }) => ({
  sitesInOnces: many(sitesInOnce),
  orgMembersInOnces: many(orgMembersInOnce),
}));

export const chatMessagesInOnceRelations = relations(chatMessagesInOnce, ({ one, many }) => ({
  chatSessionsInOnce: one(chatSessionsInOnce, {
    fields: [chatMessagesInOnce.sessionId],
    references: [chatSessionsInOnce.id],
  }),
  feedbackInOnces: many(feedbackInOnce),
}));

export const chatSessionsInOnceRelations = relations(chatSessionsInOnce, ({ one, many }) => ({
  chatMessagesInOnces: many(chatMessagesInOnce),
  agentsInOnce: one(agentsInOnce, {
    fields: [chatSessionsInOnce.agentId],
    references: [agentsInOnce.id],
  }),
  emailHistoryInOnces: many(emailHistoryInOnce),
  emailNotificationsInOnces: many(emailNotificationsInOnce),
  returnRequestsInOnces: many(returnRequestsInOnce),
}));

export const feedbackInOnceRelations = relations(feedbackInOnce, ({ one }) => ({
  chatMessagesInOnce: one(chatMessagesInOnce, {
    fields: [feedbackInOnce.messageId],
    references: [chatMessagesInOnce.id],
  }),
}));

export const fineTuneRunsInOnceRelations = relations(fineTuneRunsInOnce, ({ one }) => ({
  agentsInOnce: one(agentsInOnce, {
    fields: [fineTuneRunsInOnce.agentId],
    references: [agentsInOnce.id],
  }),
}));

export const knowledgeSourcesInOnceRelations = relations(
  knowledgeSourcesInOnce,
  ({ one, many }) => ({
    sitesInOnce: one(sitesInOnce, {
      fields: [knowledgeSourcesInOnce.siteId],
      references: [sitesInOnce.id],
    }),
    knowledgeChunksInOnces: many(knowledgeChunksInOnce),
  }),
);

export const promptVersionsInOnceRelations = relations(promptVersionsInOnce, ({ one }) => ({
  agentPromptsInOnce: one(agentPromptsInOnce, {
    fields: [promptVersionsInOnce.promptId],
    references: [agentPromptsInOnce.id],
  }),
  usersInOnce: one(usersInOnce, {
    fields: [promptVersionsInOnce.createdBy],
    references: [usersInOnce.id],
  }),
}));

export const usersInOnceRelations = relations(usersInOnce, ({ many }) => ({
  promptVersionsInOnces: many(promptVersionsInOnce),
  sessionsInOnces: many(sessionsInOnce),
  returnRequestsInOnces: many(returnRequestsInOnce),
  orgMembersInOnces: many(orgMembersInOnce),
  accountsInOnces: many(accountsInOnce),
}));

export const sessionsInOnceRelations = relations(sessionsInOnce, ({ one }) => ({
  usersInOnce: one(usersInOnce, {
    fields: [sessionsInOnce.userId],
    references: [usersInOnce.id],
  }),
}));

export const knowledgeChunksInOnceRelations = relations(knowledgeChunksInOnce, ({ one }) => ({
  knowledgeSourcesInOnce: one(knowledgeSourcesInOnce, {
    fields: [knowledgeChunksInOnce.sourceId],
    references: [knowledgeSourcesInOnce.id],
  }),
}));

export const emailHistoryInOnceRelations = relations(emailHistoryInOnce, ({ one }) => ({
  sitesInOnce: one(sitesInOnce, {
    fields: [emailHistoryInOnce.siteId],
    references: [sitesInOnce.id],
  }),
  chatSessionsInOnce: one(chatSessionsInOnce, {
    fields: [emailHistoryInOnce.sessionId],
    references: [chatSessionsInOnce.id],
  }),
}));

export const emailNotificationsInOnceRelations = relations(emailNotificationsInOnce, ({ one }) => ({
  returnRequestsInOnce: one(returnRequestsInOnce, {
    fields: [emailNotificationsInOnce.returnRequestId],
    references: [returnRequestsInOnce.id],
  }),
  chatSessionsInOnce: one(chatSessionsInOnce, {
    fields: [emailNotificationsInOnce.sessionId],
    references: [chatSessionsInOnce.id],
  }),
}));

export const returnRequestsInOnceRelations = relations(returnRequestsInOnce, ({ one, many }) => ({
  emailNotificationsInOnces: many(emailNotificationsInOnce),
  sitesInOnce: one(sitesInOnce, {
    fields: [returnRequestsInOnce.siteId],
    references: [sitesInOnce.id],
  }),
  chatSessionsInOnce: one(chatSessionsInOnce, {
    fields: [returnRequestsInOnce.sessionId],
    references: [chatSessionsInOnce.id],
  }),
  usersInOnce: one(usersInOnce, {
    fields: [returnRequestsInOnce.reviewedBy],
    references: [usersInOnce.id],
  }),
}));

export const shopPolicyConfigsInOnceRelations = relations(shopPolicyConfigsInOnce, ({ one }) => ({
  sitesInOnce: one(sitesInOnce, {
    fields: [shopPolicyConfigsInOnce.siteId],
    references: [sitesInOnce.id],
  }),
}));

export const agentConfigurationsInOnceRelations = relations(
  agentConfigurationsInOnce,
  ({ one }) => ({
    sitesInOnce: one(sitesInOnce, {
      fields: [agentConfigurationsInOnce.siteId],
      references: [sitesInOnce.id],
    }),
    agentsInOnce: one(agentsInOnce, {
      fields: [agentConfigurationsInOnce.agentId],
      references: [agentsInOnce.id],
    }),
  }),
);

export const orgMembersInOnceRelations = relations(orgMembersInOnce, ({ one }) => ({
  organizationsInOnce: one(organizationsInOnce, {
    fields: [orgMembersInOnce.orgId],
    references: [organizationsInOnce.id],
  }),
  usersInOnce: one(usersInOnce, {
    fields: [orgMembersInOnce.userId],
    references: [usersInOnce.id],
  }),
}));

export const accountsInOnceRelations = relations(accountsInOnce, ({ one }) => ({
  usersInOnce: one(usersInOnce, {
    fields: [accountsInOnce.userId],
    references: [usersInOnce.id],
  }),
}));
