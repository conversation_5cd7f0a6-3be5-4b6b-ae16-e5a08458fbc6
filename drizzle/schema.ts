import {
  pgTable,
  pgSchema,
  index,
  foreignKey,
  uuid,
  varchar,
  jsonb,
  integer,
  timestamp,
  text,
  uniqueIndex,
  boolean,
  check,
  unique,
  vector,
  primaryKey,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

export const once = pgSchema("once");

export const jobsInOnce = once.table(
  "jobs",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    agentId: uuid("agent_id"),
    type: varchar({ length: 40 }),
    payload: jsonb(),
    status: varchar({ length: 20 }).default("queued"),
    attempts: integer().default(0),
    runAfter: timestamp("run_after", { withTimezone: true, mode: "string" }).defaultNow(),
    finishedAt: timestamp("finished_at", { withTimezone: true, mode: "string" }),
    parentJobId: uuid("parent_job_id"),
    stage: varchar({ length: 40 }),
  },
  (table) => [
    index("jobs_status_runafter_idx").using(
      "btree",
      table.status.asc().nullsLast().op("text_ops"),
      table.runAfter.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.agentId],
      foreignColumns: [agentsInOnce.id],
      name: "jobs_agent_id_agents_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.parentJobId],
      foreignColumns: [table.id],
      name: "jobs_parent_job_fk",
    }).onDelete("set null"),
  ],
);

export const usersInOnce = once.table("users", {
  id: text().primaryKey().notNull(),
  name: text(),
  email: text().notNull(),
  emailVerified: timestamp("email_verified", { withTimezone: true, mode: "string" }),
  image: text(),
  password: text(),
});

export const agentsInOnce = once.table(
  "agents",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    siteId: uuid("site_id"),
    name: varchar({ length: 120 }).notNull(),
    mode: varchar({ length: 30 }).default("cs_default"),
    currentModel: varchar("current_model", { length: 120 }).notNull(),
    reflectionEnabled: boolean("reflection_enabled").default(true),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    uniqueIndex("agents_site_name_uq").using(
      "btree",
      table.siteId.asc().nullsLast().op("text_ops"),
      table.name.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.siteId],
      foreignColumns: [sitesInOnce.id],
      name: "agents_site_id_sites_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const agentPromptsInOnce = once.table(
  "agent_prompts",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    agentId: uuid("agent_id"),
    slot: varchar({ length: 24 }),
    content: text().notNull(),
    temperature: integer().default(0),
    topP: integer("top_p").default(1),
    active: boolean().default(true),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    uniqueIndex("agent_prompts_active_slot")
      .using(
        "btree",
        table.agentId.asc().nullsLast().op("text_ops"),
        table.slot.asc().nullsLast().op("text_ops"),
      )
      .where(sql`(active = true)`),
    foreignKey({
      columns: [table.agentId],
      foreignColumns: [agentsInOnce.id],
      name: "agent_prompts_agent_id_agents_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const sitesInOnce = once.table(
  "sites",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    orgId: uuid("org_id").notNull(),
    name: varchar({ length: 120 }).notNull(),
    url: varchar({ length: 256 }).notNull(),
    platform: varchar({ length: 32 }),
    platformShopId: varchar("platform_shop_id", { length: 128 }),
    platformToken: text("platform_token"),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.orgId],
      foreignColumns: [organizationsInOnce.id],
      name: "sites_org_id_organizations_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const chatMessagesInOnce = once.table(
  "chat_messages",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    sessionId: uuid("session_id"),
    role: varchar({ length: 10 }),
    content: text().notNull(),
    traceId: varchar("trace_id", { length: 60 }),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.sessionId],
      foreignColumns: [chatSessionsInOnce.id],
      name: "chat_messages_session_id_chat_sessions_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const feedbackInOnce = once.table(
  "feedback",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    messageId: uuid("message_id"),
    rating: integer().notNull(),
    correction: text(),
    comment: text(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    uniqueIndex("feedback_msg_uq").using("btree", table.messageId.asc().nullsLast().op("uuid_ops")),
    foreignKey({
      columns: [table.messageId],
      foreignColumns: [chatMessagesInOnce.id],
      name: "feedback_message_id_chat_messages_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    check("feedback_rating_chk", sql`rating = ANY (ARRAY['-1'::integer, 0, 1])`),
  ],
);

export const fineTuneRunsInOnce = once.table(
  "fine_tune_runs",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    agentId: uuid("agent_id"),
    openaiJobId: varchar("openai_job_id", { length: 60 }),
    baseModel: varchar("base_model", { length: 120 }),
    outputModel: varchar("output_model", { length: 120 }),
    status: varchar({ length: 20 }),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
    completedAt: timestamp("completed_at", { withTimezone: true, mode: "string" }),
  },
  (table) => [
    foreignKey({
      columns: [table.agentId],
      foreignColumns: [agentsInOnce.id],
      name: "fine_tune_runs_agent_id_agents_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const knowledgeSourcesInOnce = once.table(
  "knowledge_sources",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    siteId: uuid("site_id"),
    kind: varchar({ length: 24 }),
    label: varchar({ length: 120 }),
    meta: jsonb(),
    status: varchar({ length: 16 }).default("new"),
    insertedAt: timestamp("inserted_at", { withTimezone: true, mode: "string" }).defaultNow(),
    processedAt: timestamp("processed_at", { withTimezone: true, mode: "string" }),
  },
  (table) => [
    foreignKey({
      columns: [table.siteId],
      foreignColumns: [sitesInOnce.id],
      name: "knowledge_sources_site_id_sites_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const organizationsInOnce = once.table(
  "organizations",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: varchar({ length: 120 }).notNull(),
    slug: varchar({ length: 120 }).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [unique("organizations_slug_unique").on(table.slug)],
);

export const promptVersionsInOnce = once.table(
  "prompt_versions",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    promptId: uuid("prompt_id"),
    content: text().notNull(),
    createdBy: text("created_by"),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.promptId],
      foreignColumns: [agentPromptsInOnce.id],
      name: "prompt_versions_prompt_id_agent_prompts_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [usersInOnce.id],
      name: "prompt_versions_created_by_users_id_fk",
    }),
  ],
);

export const sessionsInOnce = once.table(
  "sessions",
  {
    sessionToken: text("session_token").primaryKey().notNull(),
    userId: text("user_id").notNull(),
    expires: timestamp({ withTimezone: true, mode: "string" }).notNull(),
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [usersInOnce.id],
      name: "sessions_user_id_users_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const knowledgeChunksInOnce = once.table(
  "knowledge_chunks",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    sourceId: uuid("source_id"),
    content: text().notNull(),
    embedding: vector({ dimensions: 1536 }),
    tokenCount: integer("token_count"),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
    criticalityScore: integer("criticality_score").default(50).notNull(),
    clarityScore: integer("clarity_score").default(50).notNull(),
    actionabilityScore: integer("actionability_score").default(50).notNull(),
    expectedFrequencyScore: integer("expected_frequency_score").default(50).notNull(),
  },
  (table) => [
    index("chunks_vec_idx").using("ivfflat", table.embedding.asc().nullsLast().op("vector_l2_ops")),
    foreignKey({
      columns: [table.sourceId],
      foreignColumns: [knowledgeSourcesInOnce.id],
      name: "knowledge_chunks_source_id_knowledge_sources_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const chatSessionsInOnce = once.table(
  "chat_sessions",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    agentId: uuid("agent_id"),
    externalUserId: varchar("external_user_id", { length: 120 }),
    state: varchar({ length: 20 }).default("open"),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
    closedAt: timestamp("closed_at", { withTimezone: true, mode: "string" }),
    metadata: jsonb().default({}),
    agentName: varchar("agent_name", { length: 120 }),
    siteName: varchar("site_name", { length: 120 }),
    siteId: uuid("site_id"),
  },
  (table) => [
    foreignKey({
      columns: [table.agentId],
      foreignColumns: [agentsInOnce.id],
      name: "chat_sessions_agent_id_agents_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const emailHistoryInOnce = once.table(
  "email_history",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    siteId: uuid("site_id").notNull(),
    sessionId: uuid("session_id"),
    customerEmail: varchar("customer_email", { length: 256 }).notNull(),
    direction: varchar({ length: 10 }).notNull(),
    subject: text(),
    content: text().notNull(),
    messageId: varchar("message_id", { length: 256 }),
    replyToId: varchar("reply_to_id", { length: 256 }),
    aiGenerated: boolean("ai_generated").default(false),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.siteId],
      foreignColumns: [sitesInOnce.id],
      name: "email_history_site_id_sites_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.sessionId],
      foreignColumns: [chatSessionsInOnce.id],
      name: "email_history_session_id_chat_sessions_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const emailNotificationsInOnce = once.table(
  "email_notifications",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    returnRequestId: uuid("return_request_id"),
    sessionId: uuid("session_id"),
    to: varchar({ length: 256 }).notNull(),
    from: varchar({ length: 256 }),
    subject: text().notNull(),
    content: text().notNull(),
    templateType: varchar("template_type", { length: 50 }),
    status: varchar({ length: 20 }).default("pending"),
    sentAt: timestamp("sent_at", { withTimezone: true, mode: "string" }),
    scheduledFor: timestamp("scheduled_for", { withTimezone: true, mode: "string" }),
    aiGenerated: boolean("ai_generated").default(true),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.returnRequestId],
      foreignColumns: [returnRequestsInOnce.id],
      name: "email_notifications_return_request_id_return_requests_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.sessionId],
      foreignColumns: [chatSessionsInOnce.id],
      name: "email_notifications_session_id_chat_sessions_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const shopPolicyConfigsInOnce = once.table(
  "shop_policy_configs",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    siteId: uuid("site_id").notNull(),
    personalityPrompt: text("personality_prompt").notNull(),
    enabledTools: jsonb("enabled_tools").notNull(),
    toolPolicies: jsonb("tool_policies").notNull(),
    maxTurnsBeforeEscalation: integer("max_turns_before_escalation").default(5),
    escalationEnabled: boolean("escalation_enabled").default(true),
    escalationEmail: varchar("escalation_email", { length: 256 }),
    primaryModel: varchar("primary_model", { length: 120 }).default("gpt-4o"),
    fallbackModel: varchar("fallback_model", { length: 120 }).default("gpt-5-mini"),
    temperature: integer().default(20),
    reflectionEnabled: boolean("reflection_enabled").default(true),
    reflectionThreshold: integer("reflection_threshold").default(40),
    businessHours: jsonb("business_hours"),
    active: boolean().default(true),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    uniqueIndex("shop_policy_configs_site_active")
      .using("btree", table.siteId.asc().nullsLast().op("uuid_ops"))
      .where(sql`(active = true)`),
    foreignKey({
      columns: [table.siteId],
      foreignColumns: [sitesInOnce.id],
      name: "shop_policy_configs_site_id_sites_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const returnRequestsInOnce = once.table(
  "return_requests",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    siteId: uuid("site_id"),
    sessionId: uuid("session_id"),
    orderNumber: varchar("order_number", { length: 50 }).notNull(),
    orderShopifyId: varchar("order_shopify_id", { length: 100 }),
    customerEmail: varchar("customer_email", { length: 256 }),
    customerPhone: varchar("customer_phone", { length: 50 }),
    reason: text(),
    status: varchar({ length: 30 }).default("pending"),
    requestedItems: jsonb("requested_items"),
    chatContext: text("chat_context"),
    staffNotes: text("staff_notes"),
    shopifyReturnId: varchar("shopify_return_id", { length: 100 }),
    reviewedBy: text("reviewed_by"),
    reviewedAt: timestamp("reviewed_at", { withTimezone: true, mode: "string" }),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" }).defaultNow(),
    analysisData: jsonb("analysis_data"),
  },
  (table) => [
    foreignKey({
      columns: [table.siteId],
      foreignColumns: [sitesInOnce.id],
      name: "return_requests_site_id_sites_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.sessionId],
      foreignColumns: [chatSessionsInOnce.id],
      name: "return_requests_session_id_chat_sessions_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.reviewedBy],
      foreignColumns: [usersInOnce.id],
      name: "return_requests_reviewed_by_users_id_fk",
    }),
  ],
);

export const agentConfigurationsInOnce = once.table(
  "agent_configurations",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    siteId: uuid("site_id"),
    agentId: uuid("agent_id"),
    configType: varchar("config_type", { length: 30 }).notNull(),
    enabled: boolean().default(true),
    settings: jsonb().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" }).defaultNow(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" }).defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.siteId],
      foreignColumns: [sitesInOnce.id],
      name: "agent_configurations_site_id_sites_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.agentId],
      foreignColumns: [agentsInOnce.id],
      name: "agent_configurations_agent_id_agents_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const verificationTokensInOnce = once.table(
  "verification_tokens",
  {
    identifier: text().notNull(),
    token: text().notNull(),
    expires: timestamp({ withTimezone: true, mode: "string" }).notNull(),
  },
  (table) => [
    primaryKey({
      columns: [table.identifier, table.token],
      name: "verification_tokens_identifier_token_pk",
    }),
  ],
);

export const orgMembersInOnce = once.table(
  "org_members",
  {
    orgId: uuid("org_id").notNull(),
    userId: text("user_id").notNull(),
    role: varchar({ length: 20 }).default("admin"),
  },
  (table) => [
    foreignKey({
      columns: [table.orgId],
      foreignColumns: [organizationsInOnce.id],
      name: "org_members_org_id_organizations_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [usersInOnce.id],
      name: "org_members_user_id_users_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    primaryKey({ columns: [table.orgId, table.userId], name: "org_members_org_id_user_id_pk" }),
  ],
);

export const accountsInOnce = once.table(
  "accounts",
  {
    userId: text("user_id").notNull(),
    type: text().notNull(),
    provider: text().notNull(),
    providerAccountId: text("provider_account_id").notNull(),
    refreshToken: text("refresh_token"),
    accessToken: text("access_token"),
    expiresAt: integer("expires_at"),
    tokenType: text("token_type"),
    scope: text(),
    idToken: text("id_token"),
    sessionState: text("session_state"),
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [usersInOnce.id],
      name: "accounts_user_id_users_id_fk",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    primaryKey({
      columns: [table.provider, table.providerAccountId],
      name: "accounts_provider_provider_account_id_pk",
    }),
  ],
);
