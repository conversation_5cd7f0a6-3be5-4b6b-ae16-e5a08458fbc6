{"id": "a3c712d9-1410-4fc0-b054-331f706ed356", "prevId": "edc37d4b-3b6b-4ade-be44-1e316bae74de", "version": "7", "dialect": "postgresql", "tables": {"once.accounts": {"name": "accounts", "schema": "once", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "schemaTo": "once", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"accounts_provider_provider_account_id_pk": {"name": "accounts_provider_provider_account_id_pk", "columns": ["provider", "provider_account_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.agent_configurations": {"name": "agent_configurations", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "site_id": {"name": "site_id", "type": "uuid", "primaryKey": false, "notNull": false}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "config_type": {"name": "config_type", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"agent_configurations_site_id_sites_id_fk": {"name": "agent_configurations_site_id_sites_id_fk", "tableFrom": "agent_configurations", "tableTo": "sites", "schemaTo": "once", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "agent_configurations_agent_id_agents_id_fk": {"name": "agent_configurations_agent_id_agents_id_fk", "tableFrom": "agent_configurations", "tableTo": "agents", "schemaTo": "once", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.agent_prompts": {"name": "agent_prompts", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "slot": {"name": "slot", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "temperature": {"name": "temperature", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "top_p": {"name": "top_p", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"agent_prompts_active_slot": {"name": "agent_prompts_active_slot", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "slot", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"once\".\"agent_prompts\".\"active\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"agent_prompts_agent_id_agents_id_fk": {"name": "agent_prompts_agent_id_agents_id_fk", "tableFrom": "agent_prompts", "tableTo": "agents", "schemaTo": "once", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.agents": {"name": "agents", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "site_id": {"name": "site_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "mode": {"name": "mode", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "default": "'cs_default'"}, "current_model": {"name": "current_model", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "reflection_enabled": {"name": "reflection_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"agents_site_name_uq": {"name": "agents_site_name_uq", "columns": [{"expression": "site_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"agents_site_id_sites_id_fk": {"name": "agents_site_id_sites_id_fk", "tableFrom": "agents", "tableTo": "sites", "schemaTo": "once", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.chat_messages": {"name": "chat_messages", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "trace_id": {"name": "trace_id", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_messages_session_id_chat_sessions_id_fk": {"name": "chat_messages_session_id_chat_sessions_id_fk", "tableFrom": "chat_messages", "tableTo": "chat_sessions", "schemaTo": "once", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.chat_sessions": {"name": "chat_sessions", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "external_user_id": {"name": "external_user_id", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'open'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "closed_at": {"name": "closed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chat_sessions_agent_id_agents_id_fk": {"name": "chat_sessions_agent_id_agents_id_fk", "tableFrom": "chat_sessions", "tableTo": "agents", "schemaTo": "once", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.email_notifications": {"name": "email_notifications", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "return_request_id": {"name": "return_request_id", "type": "uuid", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "to": {"name": "to", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "from": {"name": "from", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "template_type": {"name": "template_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "sent_at": {"name": "sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "scheduled_for": {"name": "scheduled_for", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "ai_generated": {"name": "ai_generated", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"email_notifications_return_request_id_return_requests_id_fk": {"name": "email_notifications_return_request_id_return_requests_id_fk", "tableFrom": "email_notifications", "tableTo": "return_requests", "schemaTo": "once", "columnsFrom": ["return_request_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "email_notifications_session_id_chat_sessions_id_fk": {"name": "email_notifications_session_id_chat_sessions_id_fk", "tableFrom": "email_notifications", "tableTo": "chat_sessions", "schemaTo": "once", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.feedback": {"name": "feedback", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "correction": {"name": "correction", "type": "text", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"feedback_msg_uq": {"name": "feedback_msg_uq", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"feedback_message_id_chat_messages_id_fk": {"name": "feedback_message_id_chat_messages_id_fk", "tableFrom": "feedback", "tableTo": "chat_messages", "schemaTo": "once", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"feedback_rating_chk": {"name": "feedback_rating_chk", "value": "\"once\".\"feedback\".\"rating\" IN (-1,0,1)"}}, "isRLSEnabled": false}, "once.fine_tune_runs": {"name": "fine_tune_runs", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "openai_job_id": {"name": "openai_job_id", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": false}, "base_model": {"name": "base_model", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "output_model": {"name": "output_model", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"fine_tune_runs_agent_id_agents_id_fk": {"name": "fine_tune_runs_agent_id_agents_id_fk", "tableFrom": "fine_tune_runs", "tableTo": "agents", "schemaTo": "once", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.jobs": {"name": "jobs", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": false}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'queued'"}, "attempts": {"name": "attempts", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "run_after": {"name": "run_after", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "finished_at": {"name": "finished_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "parent_job_id": {"name": "parent_job_id", "type": "uuid", "primaryKey": false, "notNull": false}, "stage": {"name": "stage", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": false}}, "indexes": {"jobs_status_runafter_idx": {"name": "jobs_status_runafter_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "run_after", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"jobs_agent_id_agents_id_fk": {"name": "jobs_agent_id_agents_id_fk", "tableFrom": "jobs", "tableTo": "agents", "schemaTo": "once", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "jobs_parent_job_fk": {"name": "jobs_parent_job_fk", "tableFrom": "jobs", "tableTo": "jobs", "schemaTo": "once", "columnsFrom": ["parent_job_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.knowledge_chunks": {"name": "knowledge_chunks", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "source_id": {"name": "source_id", "type": "uuid", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": false}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"chunks_vec_idx": {"name": "chunks_vec_idx", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_l2_ops"}], "isUnique": false, "concurrently": false, "method": "ivff<PERSON>", "with": {}}}, "foreignKeys": {"knowledge_chunks_source_id_knowledge_sources_id_fk": {"name": "knowledge_chunks_source_id_knowledge_sources_id_fk", "tableFrom": "knowledge_chunks", "tableTo": "knowledge_sources", "schemaTo": "once", "columnsFrom": ["source_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.knowledge_sources": {"name": "knowledge_sources", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "site_id": {"name": "site_id", "type": "uuid", "primaryKey": false, "notNull": false}, "kind": {"name": "kind", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "meta": {"name": "meta", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(16)", "primaryKey": false, "notNull": false, "default": "'new'"}, "inserted_at": {"name": "inserted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "processed_at": {"name": "processed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"knowledge_sources_site_id_sites_id_fk": {"name": "knowledge_sources_site_id_sites_id_fk", "tableFrom": "knowledge_sources", "tableTo": "sites", "schemaTo": "once", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.org_members": {"name": "org_members", "schema": "once", "columns": {"org_id": {"name": "org_id", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'admin'"}}, "indexes": {}, "foreignKeys": {"org_members_org_id_organizations_id_fk": {"name": "org_members_org_id_organizations_id_fk", "tableFrom": "org_members", "tableTo": "organizations", "schemaTo": "once", "columnsFrom": ["org_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "org_members_user_id_users_id_fk": {"name": "org_members_user_id_users_id_fk", "tableFrom": "org_members", "tableTo": "users", "schemaTo": "once", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"org_members_org_id_user_id_pk": {"name": "org_members_org_id_user_id_pk", "columns": ["org_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.organizations": {"name": "organizations", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizations_slug_unique": {"name": "organizations_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.prompt_versions": {"name": "prompt_versions", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "prompt_id": {"name": "prompt_id", "type": "uuid", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"prompt_versions_prompt_id_agent_prompts_id_fk": {"name": "prompt_versions_prompt_id_agent_prompts_id_fk", "tableFrom": "prompt_versions", "tableTo": "agent_prompts", "schemaTo": "once", "columnsFrom": ["prompt_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "prompt_versions_created_by_users_id_fk": {"name": "prompt_versions_created_by_users_id_fk", "tableFrom": "prompt_versions", "tableTo": "users", "schemaTo": "once", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.return_requests": {"name": "return_requests", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "site_id": {"name": "site_id", "type": "uuid", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "order_number": {"name": "order_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "order_shopify_id": {"name": "order_shopify_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "customer_email": {"name": "customer_email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "customer_phone": {"name": "customer_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "requested_items": {"name": "requested_items", "type": "jsonb", "primaryKey": false, "notNull": false}, "chat_context": {"name": "chat_context", "type": "text", "primaryKey": false, "notNull": false}, "analysis_data": {"name": "analysis_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "staff_notes": {"name": "staff_notes", "type": "text", "primaryKey": false, "notNull": false}, "shopify_return_id": {"name": "shopify_return_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "text", "primaryKey": false, "notNull": false}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"return_requests_site_id_sites_id_fk": {"name": "return_requests_site_id_sites_id_fk", "tableFrom": "return_requests", "tableTo": "sites", "schemaTo": "once", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "return_requests_session_id_chat_sessions_id_fk": {"name": "return_requests_session_id_chat_sessions_id_fk", "tableFrom": "return_requests", "tableTo": "chat_sessions", "schemaTo": "once", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "return_requests_reviewed_by_users_id_fk": {"name": "return_requests_reviewed_by_users_id_fk", "tableFrom": "return_requests", "tableTo": "users", "schemaTo": "once", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.sessions": {"name": "sessions", "schema": "once", "columns": {"session_token": {"name": "session_token", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "schemaTo": "once", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.shop_policy_configs": {"name": "shop_policy_configs", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "site_id": {"name": "site_id", "type": "uuid", "primaryKey": false, "notNull": true}, "personality_prompt": {"name": "personality_prompt", "type": "text", "primaryKey": false, "notNull": true}, "enabled_tools": {"name": "enabled_tools", "type": "jsonb", "primaryKey": false, "notNull": true}, "tool_policies": {"name": "tool_policies", "type": "jsonb", "primaryKey": false, "notNull": true}, "max_turns_before_escalation": {"name": "max_turns_before_escalation", "type": "integer", "primaryKey": false, "notNull": false, "default": 5}, "escalation_enabled": {"name": "escalation_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "escalation_email": {"name": "escalation_email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "primary_model": {"name": "primary_model", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false, "default": "'gpt-4o'"}, "fallback_model": {"name": "fallback_model", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false, "default": "'gpt-4o-mini'"}, "temperature": {"name": "temperature", "type": "integer", "primaryKey": false, "notNull": false, "default": 20}, "reflection_enabled": {"name": "reflection_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "reflection_threshold": {"name": "reflection_threshold", "type": "integer", "primaryKey": false, "notNull": false, "default": 40}, "business_hours": {"name": "business_hours", "type": "jsonb", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"shop_policy_configs_site_active": {"name": "shop_policy_configs_site_active", "columns": [{"expression": "site_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"once\".\"shop_policy_configs\".\"active\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"shop_policy_configs_site_id_sites_id_fk": {"name": "shop_policy_configs_site_id_sites_id_fk", "tableFrom": "shop_policy_configs", "tableTo": "sites", "schemaTo": "once", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.sites": {"name": "sites", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "org_id": {"name": "org_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "platform_shop_id": {"name": "platform_shop_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "platform_token": {"name": "platform_token", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sites_org_id_organizations_id_fk": {"name": "sites_org_id_organizations_id_fk", "tableFrom": "sites", "tableTo": "organizations", "schemaTo": "once", "columnsFrom": ["org_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.users": {"name": "users", "schema": "once", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.verification_tokens": {"name": "verification_tokens", "schema": "once", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verification_tokens_identifier_token_pk": {"name": "verification_tokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {"once": "once"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}