{"id": "843de989-dfd1-454d-b6f1-8ad4f4ce1b51", "prevId": "********-0000-0000-0000-************", "version": "7", "dialect": "postgresql", "tables": {"once.accounts": {"name": "accounts", "schema": "once", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "schemaTo": "once", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"accounts_provider_provider_account_id_pk": {"name": "accounts_provider_provider_account_id_pk", "columns": ["provider", "provider_account_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.agent_prompts": {"name": "agent_prompts", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "slot": {"name": "slot", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "temperature": {"name": "temperature", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "top_p": {"name": "top_p", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"agent_prompts_active_slot": {"name": "agent_prompts_active_slot", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "slot", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"once\".\"agent_prompts\".\"active\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"agent_prompts_agent_id_agents_id_fk": {"name": "agent_prompts_agent_id_agents_id_fk", "tableFrom": "agent_prompts", "tableTo": "agents", "schemaTo": "once", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.agents": {"name": "agents", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "site_id": {"name": "site_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "mode": {"name": "mode", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "default": "'cs_default'"}, "current_model": {"name": "current_model", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "reflection_enabled": {"name": "reflection_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"agents_site_name_uq": {"name": "agents_site_name_uq", "columns": [{"expression": "site_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"agents_site_id_sites_id_fk": {"name": "agents_site_id_sites_id_fk", "tableFrom": "agents", "tableTo": "sites", "schemaTo": "once", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.chat_messages": {"name": "chat_messages", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "trace_id": {"name": "trace_id", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_messages_session_id_chat_sessions_id_fk": {"name": "chat_messages_session_id_chat_sessions_id_fk", "tableFrom": "chat_messages", "tableTo": "chat_sessions", "schemaTo": "once", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.chat_sessions": {"name": "chat_sessions", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "external_user_id": {"name": "external_user_id", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'open'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "closed_at": {"name": "closed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chat_sessions_agent_id_agents_id_fk": {"name": "chat_sessions_agent_id_agents_id_fk", "tableFrom": "chat_sessions", "tableTo": "agents", "schemaTo": "once", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.feedback": {"name": "feedback", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "correction": {"name": "correction", "type": "text", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"feedback_msg_uq": {"name": "feedback_msg_uq", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"feedback_message_id_chat_messages_id_fk": {"name": "feedback_message_id_chat_messages_id_fk", "tableFrom": "feedback", "tableTo": "chat_messages", "schemaTo": "once", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"feedback_rating_chk": {"name": "feedback_rating_chk", "value": "\"once\".\"feedback\".\"rating\" IN (-1,0,1)"}}, "isRLSEnabled": false}, "once.fine_tune_runs": {"name": "fine_tune_runs", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "openai_job_id": {"name": "openai_job_id", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": false}, "base_model": {"name": "base_model", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "output_model": {"name": "output_model", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"fine_tune_runs_agent_id_agents_id_fk": {"name": "fine_tune_runs_agent_id_agents_id_fk", "tableFrom": "fine_tune_runs", "tableTo": "agents", "schemaTo": "once", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.jobs": {"name": "jobs", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": false}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'queued'"}, "attempts": {"name": "attempts", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "run_after": {"name": "run_after", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "finished_at": {"name": "finished_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"jobs_status_runafter_idx": {"name": "jobs_status_runafter_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "run_after", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"jobs_agent_id_agents_id_fk": {"name": "jobs_agent_id_agents_id_fk", "tableFrom": "jobs", "tableTo": "agents", "schemaTo": "once", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.knowledge_chunks": {"name": "knowledge_chunks", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "source_id": {"name": "source_id", "type": "uuid", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": false}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"chunks_vec_idx": {"name": "chunks_vec_idx", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_l2_ops"}], "isUnique": false, "concurrently": false, "method": "ivff<PERSON>", "with": {}}}, "foreignKeys": {"knowledge_chunks_source_id_knowledge_sources_id_fk": {"name": "knowledge_chunks_source_id_knowledge_sources_id_fk", "tableFrom": "knowledge_chunks", "tableTo": "knowledge_sources", "schemaTo": "once", "columnsFrom": ["source_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.knowledge_sources": {"name": "knowledge_sources", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "site_id": {"name": "site_id", "type": "uuid", "primaryKey": false, "notNull": false}, "kind": {"name": "kind", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "meta": {"name": "meta", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(16)", "primaryKey": false, "notNull": false, "default": "'new'"}, "inserted_at": {"name": "inserted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "processed_at": {"name": "processed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"knowledge_sources_site_id_sites_id_fk": {"name": "knowledge_sources_site_id_sites_id_fk", "tableFrom": "knowledge_sources", "tableTo": "sites", "schemaTo": "once", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.org_members": {"name": "org_members", "schema": "once", "columns": {"org_id": {"name": "org_id", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'admin'"}}, "indexes": {}, "foreignKeys": {"org_members_org_id_organizations_id_fk": {"name": "org_members_org_id_organizations_id_fk", "tableFrom": "org_members", "tableTo": "organizations", "schemaTo": "once", "columnsFrom": ["org_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "org_members_user_id_users_id_fk": {"name": "org_members_user_id_users_id_fk", "tableFrom": "org_members", "tableTo": "users", "schemaTo": "once", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"org_members_org_id_user_id_pk": {"name": "org_members_org_id_user_id_pk", "columns": ["org_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.organizations": {"name": "organizations", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizations_slug_unique": {"name": "organizations_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.prompt_versions": {"name": "prompt_versions", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "prompt_id": {"name": "prompt_id", "type": "uuid", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"prompt_versions_prompt_id_agent_prompts_id_fk": {"name": "prompt_versions_prompt_id_agent_prompts_id_fk", "tableFrom": "prompt_versions", "tableTo": "agent_prompts", "schemaTo": "once", "columnsFrom": ["prompt_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "prompt_versions_created_by_users_id_fk": {"name": "prompt_versions_created_by_users_id_fk", "tableFrom": "prompt_versions", "tableTo": "users", "schemaTo": "once", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.sessions": {"name": "sessions", "schema": "once", "columns": {"session_token": {"name": "session_token", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "schemaTo": "once", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.sites": {"name": "sites", "schema": "once", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "org_id": {"name": "org_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "platform_shop_id": {"name": "platform_shop_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "platform_token": {"name": "platform_token", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sites_org_id_organizations_id_fk": {"name": "sites_org_id_organizations_id_fk", "tableFrom": "sites", "tableTo": "organizations", "schemaTo": "once", "columnsFrom": ["org_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.users": {"name": "users", "schema": "once", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "once.verification_tokens": {"name": "verification_tokens", "schema": "once", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verification_tokens_identifier_token_pk": {"name": "verification_tokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {"once": "once"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}