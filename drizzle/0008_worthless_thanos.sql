CREATE TABLE "once"."knowledge_topics" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"site_id" uuid NOT NULL,
	"slug" varchar(100) NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"icon" varchar(50),
	"display_order" integer DEFAULT 0,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "once"."knowledge_sources" ADD COLUMN "topic_id" uuid;--> statement-breakpoint
ALTER TABLE "once"."knowledge_topics" ADD CONSTRAINT "knowledge_topics_site_id_sites_id_fk" FOREIGN KEY ("site_id") REFERENCES "once"."sites"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
CREATE UNIQUE INDEX "knowledge_topics_site_slug_uq" ON "once"."knowledge_topics" USING btree ("site_id","slug");--> statement-breakpoint
CREATE INDEX "knowledge_topics_site_idx" ON "once"."knowledge_topics" USING btree ("site_id");--> statement-breakpoint
CREATE INDEX "knowledge_topics_display_order_idx" ON "once"."knowledge_topics" USING btree ("display_order");--> statement-breakpoint
ALTER TABLE "once"."knowledge_sources" ADD CONSTRAINT "knowledge_sources_topic_id_knowledge_topics_id_fk" FOREIGN KEY ("topic_id") REFERENCES "once"."knowledge_topics"("id") ON DELETE cascade ON UPDATE cascade;